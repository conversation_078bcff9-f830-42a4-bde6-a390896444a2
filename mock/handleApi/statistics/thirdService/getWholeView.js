module.exports = {
  "success": true,
  "code": 200,
  "msg": "处理成功",
  "data": {
    "total": 22,
    "changeNum": null,
    "providerRanks": [
      {
        "providerCode": "74338757d9084d0f8e1b634483a41dc8",
        "providerName": "NBA",
        "count": 16,
        "rate": 0.7273
      },
      {
        "providerCode": "5a36a98863b948bf9db56dbba55175f3",
        "providerName": "neco",
        "count": 3,
        "rate": 0.1364
      },
      {
        "providerCode": "5094bc5bf1f34f7e9a3a82f3359e9a13",
        "providerName": "necoMock",
        "count": 2,
        "rate": 0.0909
      },
      {
        "providerCode": "a4ced98eb646438c96c1d38624f1e8e7",
        "providerName": "泰康",
        "count": 1,
        "rate": 0.0455
      }
    ],
    "typeRanks": [
      {
        "type": 1,
        "typeName": "身份验证类",
        "count": 10,
        "rate": 0.4545
      },
      {
        "type": 2,
        "typeName": "行为类",
        "count": 8,
        "rate": 0.3636
      },
      {
        "type": 7,
        "typeName": "其他类",
        "count": 4,
        "rate": 0.1818
      }
    ]
  }
}