module.exports = {
  "success": true,
  "code": 200,
  "msg": "处理成功",
  "data": {
    "total": 4,
    "curPage": 1,
    "pageSize": 10,
    "pages": 1,
    "size": 4,
    "contents": [
      {
        "sequenceId": "1733476526012S33763",
        "partnerName": "测试合作方",
        "contractName": null,
        "contractVersion": "",
        "appDisplayName": null,
        "serviceName": "case函数调用",
        "serviceTypeName": "身份验证类",
        "responseResult": "处理成功",
        "msg": "成功",
        "searchResult": "未查得",
        "requestTime": "2024-12-06 17:15:26",
        "cost": 3,
        "retryFlag": null,
        "invokeSource": "数据源调用",
        "cachedName": "否",
        "recordTime": 1733476526046,
        "invokeSequenceId": "1733476526013IV64419",
        "channelSequenceId": "173347652212214D200647AUK6DNIUML2BU"
      },
      {
        "sequenceId": "1733476454113S67801",
        "partnerName": "测试合作方",
        "contractName": null,
        "contractVersion": "",
        "appDisplayName": null,
        "serviceName": "case函数调用",
        "serviceTypeName": "身份验证类",
        "responseResult": "处理成功",
        "msg": "成功",
        "searchResult": "未查得",
        "requestTime": "2024-12-06 17:14:14",
        "cost": 2,
        "retryFlag": null,
        "invokeSource": "数据源调用",
        "cachedName": "否",
        "recordTime": 1733476454154,
        "invokeSequenceId": "1733476454114IV88549",
        "channelSequenceId": "173347645018514D200640GZEF02EHB6HZI"
      },
      {
        "sequenceId": "1733476341224S96245",
        "partnerName": "测试合作方",
        "contractName": null,
        "contractVersion": "",
        "appDisplayName": null,
        "serviceName": "case函数调用",
        "serviceTypeName": "身份验证类",
        "responseResult": "处理成功",
        "msg": "成功",
        "searchResult": "未查得",
        "requestTime": "2024-12-06 17:12:21",
        "cost": 3,
        "retryFlag": null,
        "invokeSource": "数据源调用",
        "cachedName": "否",
        "recordTime": 1733476341258,
        "invokeSequenceId": "1733476341225IV55587",
        "channelSequenceId": "173347633733014D2006400KMJ2MGP0Y7VE"
      },
      {
        "sequenceId": "1733475784406S97687",
        "partnerName": "测试合作方",
        "contractName": null,
        "contractVersion": "",
        "appDisplayName": null,
        "serviceName": "case函数调用",
        "serviceTypeName": "身份验证类",
        "responseResult": "处理成功",
        "msg": "成功",
        "searchResult": "未查得",
        "requestTime": "2024-12-06 17:03:04",
        "cost": 3,
        "retryFlag": null,
        "invokeSource": "数据源调用",
        "cachedName": "否",
        "recordTime": 1733475785046,
        "invokeSequenceId": "1733475784409IV78107",
        "channelSequenceId": "173347578018914D20064UVAFEXO4EKLZ7L"
      }
    ]
  }
}