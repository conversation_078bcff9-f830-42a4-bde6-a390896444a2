module.exports = {
  "success": true,
  "code": 200,
  "msg": "处理成功",
  "data": {
    "sequenceId": "1733476526012S33763",
    "inputParams": "{\"headers\":{},\"bodyParam\":{},\"sqlParam\":{},\"outBody\":\"\",\"urlParam\":{\"S_S_CUSTCRTDAT\":\"2024\",\"S_N_WEEKLYTIME\":\"21\",\"C_S_ISUPDACCOUNTINFO\":\"true\"},\"allParam\":{\"S_S_CUSTCRTDAT\":\"2024\",\"S_N_WEEKLYTIME\":\"21\",\"C_S_ISUPDACCOUNTINFO\":\"true\"},\"sql\":{}}",
    "outputParams": "{\"processData\":\"{\\\"S_S_METRICRESULT\\\":null}\",\"extraData\":\"{\\\"indexResult\\\":{\\\"caseStringTemplate\\\":{\\\"callDate\\\":*************,\\\"code\\\":0,\\\"defaultValue\\\":\\\"001\\\",\\\"indexCategoryUuid\\\":\\\"95cad163fc3a4bda827d8315ba154bdb\\\",\\\"indexDisplayName\\\":\\\"case外数指标\\\",\\\"indexName\\\":\\\"caseStringTemplate\\\",\\\"indexUuid\\\":\\\"bcf57cf2d9434bcb986debf156659745\\\",\\\"result\\\":\\\"123\\\",\\\"resultType\\\":\\\"STRING\\\",\\\"success\\\":true},\\\"caseReturndubbo\\\":{\\\"callDate\\\":*************,\\\"code\\\":0,\\\"indexCategoryUuid\\\":\\\"95cad163fc3a4bda827d8315ba154bdb\\\",\\\"indexDisplayName\\\":\\\"case外数指标小数\\\",\\\"indexName\\\":\\\"caseReturndubbo\\\",\\\"indexUuid\\\":\\\"a339392e33d943cfada1e66341ad6fdc\\\",\\\"result\\\":1.12,\\\"resultType\\\":\\\"FLOAT\\\",\\\"success\\\":true}}}\",\"responseData\":\"ok\\n\"}"
  }
}