module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "creator": "yulong.guan(yulong.guan)",
    "operator": "yulong.guan(yulong.guan)",
    "gmtCreate": "2024-11-20 20:29:07",
    "gmtModify": "2024-11-20 20:30:02",
    "uuid": "bce921a87a3d4771b223c6bfee43e51f",
    "name": "gyl1120sfwd",
    "displayName": "gyl1120三方勿动",
    "status": 1,
    "partnerId": "c0aeeee9400d41a7a142a33acd105916",
    "confidence": 2,
    "costLevel": 1,
    "timeout": 20000,
    "methodType": "get",
    "contentType": "default",
    "url": "http://10.59.207.67:8088/ok.htm",
    "postEtlHandlerName": "json",
    "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_TRANSBANKNO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":false,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"tran\",\"type\":\"variable\"},{\"dataType\":1,\"field\":\"S_S_PAYERCUSTNO\",\"fromContract\":false,\"keyInclude\":false,\"mustInput\":false,\"order\":1,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"payer\",\"type\":\"variable\"}]",
    "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_MERNO\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":null,\"type\":\"constant\",\"value\":\"1\"},{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_DEVICETOKENID\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":null,\"type\":\"constant\",\"value\":\"2\"}]",
    "dataType": 1,
    "chargeMethod": 1,
    "contractId": "f38a317e4e0d4534b736f699163b5301",
    "contractCode": "060401",
    "mockFlag": 0,
    "cacheOpen": 0,
    "limitConfig": "[]",
    "proxy": 0,
    "proxyInfo": "",
    "partnerDisplayName": "CYY供应商",
    "invokePolicy": 0,
    "indexPackageName": "",
    "dataSourceType": "SYNC",
    "pagination": false
  }
}