module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "creator": "yulong.guan(yulong.guan)",
      "operator": "yulong.guan(yulong.guan)",
      "gmtCreate": "2024-11-19 11:30:44",
      "gmtModify": "2024-11-19 11:30:46",
      "uuid": "4a3555d065184cd4895aebe9d966e246",
      "name": "gyl1119wudong",
      "displayName": "gyl1119三方勿动",
      "status": 1,
      "partnerId": "c0aeeee9400d41a7a142a33acd105916",
      "confidence": 2,
      "costLevel": 1,
      "timeout": 20000,
      "methodType": "get",
      "contentType": "default",
      "url": "http://10.59.207.67:8088/ok.htm",
      "postEtlHandlerName": "json",
      "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"cust\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":false,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"},{\"displayName\":null,\"serviceParam\":\"mpsu\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":false,\"sendSpace\":\"url\",\"keyInclude\":false,\"uuid\":1,\"field\":\"S_S_MPSUBID\"}]",
      "outputConfig": "[{\"displayName\":null,\"serviceParam\":null,\"type\":\"constant\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"value\":\"1\"},{\"displayName\":null,\"serviceParam\":null,\"type\":\"constant\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":1,\"dataType\":1,\"field\":\"S_S_MPSUBID\",\"value\":\"2\"}]",
      "dataType": 1,
      "chargeMethod": 1,
      "contractId": "f38a317e4e0d4534b736f699163b5301",
      "mockFlag": 0,
      "cacheOpen": 0,
      "limitConfig": "[]",
      "proxy": 0,
      "proxyInfo": "",
      "invokePolicy": 0,
      "indexPackageName": "",
      "priority": 1,
      "dataSourceType": "SYNC",
      "pagination": false
    }
  ]
}