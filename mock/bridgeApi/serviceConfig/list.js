module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "total": 37,
    "curPage": 1,
    "pageSize": 1000,
    "pages": 1,
    "size": 37,
    "contents": [
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "fujun.cai(蔡福君)",
        "gmtCreate": "2024-10-31 12:07:13",
        "gmtModify": "2024-12-23 10:38:56",
        "uuid": "6c86bf49702a4877b84a8ba156096952",
        "name": "45678",
        "displayName": "真实接口限流",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/hello",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0},{\"displayName\":null,\"serviceParam\":\"fileName\",\"type\":\"constant\",\"value\":\"1\",\"dataType\":null,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":null,\"uuid\":1,\"field\":null}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 0,
        "limitConfig": "[{\"type\":3,\"limit\":9999999}]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "necoReporHeaderInfoSet",
        "dataSourceType": "SYNC",
        "documentTypeUuid": "2db2facd984f42ceabeeefeb4f0b9e44",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "fujun.cai(蔡福君)",
        "gmtCreate": "2024-10-31 12:06:49",
        "gmtModify": "2024-12-23 10:42:10",
        "uuid": "ebf085b66c8c467ab3788aeaded2e6ed",
        "name": "34567",
        "displayName": "mock接口限流",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/hello",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0},{\"displayName\":null,\"serviceParam\":\"fileName\",\"type\":\"constant\",\"value\":\"1\",\"dataType\":null,\"mustInput\":false,\"sendSpace\":\"url\",\"keyInclude\":null,\"uuid\":1,\"field\":null}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 1,
        "mockType": 1,
        "cacheOpen": 0,
        "limitConfig": "[{\"type\":3,\"limit\":999999}]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "fujun.cai(蔡福君)",
        "gmtCreate": "2024-10-31 12:06:34",
        "gmtModify": "2024-12-23 10:42:43",
        "uuid": "c7eb5afcdce54ff398befa562c194b88",
        "name": "23456",
        "displayName": "真实接口不限流",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/hello",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0},{\"displayName\":null,\"serviceParam\":\"fileName\",\"type\":\"constant\",\"value\":\"1\",\"dataType\":null,\"mustInput\":false,\"sendSpace\":\"url\",\"keyInclude\":null,\"uuid\":1,\"field\":null}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "necoReporHeaderInfoSet",
        "dataSourceType": "SYNC",
        "documentTypeUuid": "2db2facd984f42ceabeeefeb4f0b9e44",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "fujun.cai(蔡福君)",
        "gmtCreate": "2024-10-31 12:06:18",
        "gmtModify": "2024-12-23 10:43:08",
        "uuid": "ba0e95e17d484e29aa4e67dc6e084812",
        "name": "12345",
        "displayName": "mock接口不限流",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/fileName",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0},{\"displayName\":null,\"serviceParam\":\"fileName\",\"type\":\"constant\",\"value\":\"1\",\"dataType\":null,\"mustInput\":false,\"sendSpace\":\"url\",\"keyInclude\":null,\"uuid\":1,\"field\":null}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 1,
        "mockType": 1,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "byh(byh)",
        "operator": "byh(byh)",
        "gmtCreate": "2024-10-25 17:15:46",
        "gmtModify": "2024-10-25 17:15:56",
        "uuid": "42cd2b4ac0c6468395faa39555ff71bf",
        "name": "wqq",
        "displayName": "wqq",
        "status": 2,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 2,
        "costLevel": 3,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "post",
        "contentType": "application/x-www-form-urlencoded",
        "url": "http://192.168.1.1",
        "postEtlHandlerName": "json",
        "preEtlHandlerName": "TestWebService",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"ll\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"header\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"l\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":true,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_METRICRESULT\"}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "e0db083e01904969bf42e0bb1d5fde9f",
        "mockFlag": 1,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "documentTypeUuid": "ca48e0130b2c4149af78b3c5f4a4167f",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "chen.shen(申晨)",
        "gmtCreate": "2024-10-24 18:18:35",
        "gmtModify": "2024-10-25 14:23:50",
        "uuid": "8aa88eae570c42b4ab8d68951ce9380e",
        "name": "sctest6",
        "displayName": "测试缓存1小时异步",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "HOURS",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "ASYNC",
        "asyncMethodType": "get",
        "asyncPollTimeInterval": 1000,
        "asyncPollMaxCount": 10,
        "asyncContentType": "default",
        "asyncUrl": "http://10.58.16.178:8123/",
        "asyncPostEtlHandlerName": "json",
        "asyncInputConfig": "[{\"displayName\":null,\"serviceParam\":\"abc\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "asyncOutputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age1\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age1\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code1\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code1\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "asyncProxy": 0,
        "asyncProxyInfo": "",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "chen.shen(申晨)",
        "gmtCreate": "2024-10-24 18:14:30",
        "gmtModify": "2024-10-29 16:53:18",
        "uuid": "6322367f2f4448bfbc43a52307de6bc7",
        "name": "sctest5",
        "displayName": "测试缓存1天异步",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "DAYS",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "ASYNC",
        "asyncMethodType": "get",
        "asyncPollTimeInterval": 1000,
        "asyncPollMaxCount": 10,
        "asyncContentType": "default",
        "asyncUrl": "http://10.58.16.178:8123/",
        "asyncPostEtlHandlerName": "json",
        "asyncInputConfig": "[{\"displayName\":null,\"serviceParam\":\"abc\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "asyncOutputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age1\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age1\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code1\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code1\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "asyncProxy": 0,
        "asyncProxyInfo": "",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "fujun.cai(蔡福君)",
        "gmtCreate": "2024-10-24 18:13:39",
        "gmtModify": "2024-12-09 15:31:49",
        "uuid": "909c7ee0cc56462bba95b5ceef0f742a",
        "name": "sctest4",
        "displayName": "测试缓存当天异步",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"abc\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"age\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"dataType\":2,\"field\":\"S_N_AGE\",\"defaultValue\":\"default_age\"},{\"displayName\":null,\"serviceParam\":\"code\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":1,\"dataType\":1,\"field\":\"S_S_METRICRESULT\",\"defaultValue\":\"default_code\"}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "ASYNC",
        "asyncMethodType": "get",
        "asyncPollTimeInterval": 1000,
        "asyncPollMaxCount": 10,
        "asyncContentType": "default",
        "asyncUrl": "http://10.58.16.178:8123/",
        "asyncPostEtlHandlerName": "json",
        "asyncInputConfig": "[{\"displayName\":null,\"serviceParam\":\"abc\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "asyncOutputConfig": "[{\"displayName\":null,\"serviceParam\":\"age1\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"field\":\"S_N_AGE\",\"defaultValue\":\"default_age1\"},{\"displayName\":null,\"serviceParam\":\"code1\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":1,\"field\":\"S_S_METRICRESULT\",\"defaultValue\":\"default_code1\"}]",
        "asyncProxy": 0,
        "asyncProxyInfo": "",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "chen.shen(申晨)",
        "gmtCreate": "2024-10-24 16:55:42",
        "gmtModify": "2024-10-25 11:03:27",
        "uuid": "70b6a24b9ffd431a8b33054c60fe2aac",
        "name": "sctest3",
        "displayName": "测试缓存1小时同步",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "HOURS",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "chen.shen(申晨)",
        "gmtCreate": "2024-10-24 16:55:18",
        "gmtModify": "2024-10-24 17:07:06",
        "uuid": "06a510bc28d345718d73e69850549d0a",
        "name": "sctest2",
        "displayName": "测试缓存1天同步",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "DAYS",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "chen.shen(申晨)",
        "gmtCreate": "2024-10-24 16:54:46",
        "gmtModify": "2024-10-28 15:38:14",
        "uuid": "f68e0704d8fa4c88b33c2994c1294ff7",
        "name": "sctest1",
        "displayName": "测试缓存当天同步",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "fujun.cai(蔡福君)",
        "operator": "chen.shen(申晨)",
        "gmtCreate": "2024-10-22 14:33:08",
        "gmtModify": "2024-10-29 10:05:52",
        "uuid": "5d5a909ddf4b4c0d8dc03977084009d7",
        "name": "cc_cp",
        "displayName": "cc_cp",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "byh(byh)",
        "operator": "byh(byh)",
        "gmtCreate": "2024-10-21 20:04:25",
        "gmtModify": "2024-10-22 15:11:41",
        "uuid": "ff9cb74759ba4e29b7942011a01d566c",
        "name": "ee",
        "displayName": "qqq",
        "status": 2,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 2,
        "costLevel": 3,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "post",
        "contentType": "application/x-www-form-urlencoded",
        "url": "http://192.168.1.1",
        "postEtlHandlerName": "json",
        "preEtlHandlerName": "TestWebService",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"ll\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"header\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"l\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":true,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_METRICRESULT\"}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "e0db083e01904969bf42e0bb1d5fde9f",
        "mockFlag": 1,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "documentTypeUuid": "ca48e0130b2c4149af78b3c5f4a4167f",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "byh(byh)",
        "operator": "byh(byh)",
        "gmtCreate": "2024-10-21 19:56:07",
        "gmtModify": "2024-10-21 19:56:17",
        "uuid": "2a3335c558884a96bb60eb089c75eea1",
        "name": "23",
        "displayName": "ws",
        "status": 2,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 2,
        "costLevel": 3,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "post",
        "contentType": "application/x-www-form-urlencoded",
        "url": "http://192.168.1.1",
        "postEtlHandlerName": "json",
        "preEtlHandlerName": "TestWebService",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"ll\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"header\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"l\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":true,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_METRICRESULT\"}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "e0db083e01904969bf42e0bb1d5fde9f",
        "mockFlag": 1,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "documentTypeUuid": "ca48e0130b2c4149af78b3c5f4a4167f",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "fujun.cai(蔡福君)",
        "operator": "fujun.cai(蔡福君)",
        "gmtCreate": "2024-10-21 16:57:27",
        "gmtModify": "2024-10-21 16:57:46",
        "uuid": "d4bd73db02e6435aaa369bb3b785110c",
        "name": "c_test_del",
        "displayName": "c_test_del",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 1,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 1,
        "mockType": 1,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "fujun.cai(蔡福君)",
        "operator": "fujun.cai(蔡福君)",
        "gmtCreate": "2024-10-12 17:16:23",
        "gmtModify": "2024-10-23 14:36:19",
        "uuid": "b270dd504d824f85849e1276676e22a4",
        "name": "ccc_test",
        "displayName": "ccc_test",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheday": 0,
        "cacheUnit": "SAME_DAY",
        "cacheUnitNumber": 2,
        "cacheInfrastructureType": "nosql",
        "retry": 1,
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.178:8123/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_CUSTCRTDAT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"abc\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"defaultValue\":\"default_age\",\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":\"default_code\",\"displayName\":null,\"field\":\"S_S_METRICRESULT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\"items\": [{\"id\": 1, \"name\": \"Item 1\"}, {\"id\": 2, \"name\": \"Item 2\"}], \"message\": \"Hello from the JSON server!\", \"code\": \"${code}\", \"params\": {},\"age\":\"${age}\"}",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "mockType": 1,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "wty(wty)",
        "operator": "fujun.cai(蔡福君)",
        "gmtCreate": "2024-08-16 15:00:07",
        "gmtModify": "2024-10-12 15:07:13",
        "uuid": "0db1134777d441929539a1e186ad75e2",
        "name": "ceshidaorujiekou",
        "displayName": "测试导入接口",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 3,
        "cacheday": 7,
        "cacheUnit": "DAYS",
        "cacheUnitNumber": 7,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "https://www.tongdun.cn/",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":2,\"field\":\"C_N_CS\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"order\":0,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"SS\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":2,\"displayName\":null,\"field\":\"C_N_CS\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"SS\",\"type\":\"variable\",\"value\":null,\"uuid\":0,\"defaultValue\":\"asdf\"}]",
        "dataType": 7,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 1,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yujiang.ye(yujiang.ye)",
        "operator": "wrm(wrm)",
        "gmtCreate": "2024-07-21 17:41:32",
        "gmtModify": "2024-07-23 20:02:52",
        "uuid": "676090ff25ee4af58d3511c03f3aff5b",
        "name": "necoXml",
        "displayName": "necoXml",
        "status": 1,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 1,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.59.210.31:8088/bridgeApi/system/config/value/withoutLogin/I18NController",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_WORKUNIT\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"key\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":true,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_WORKUNIT\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"data\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "outputTemplate": "{\"data\": \"${data}\"}",
        "dataType": 1,
        "chargeMethod": 2,
        "contractId": "e0db083e01904969bf42e0bb1d5fde9f",
        "mockFlag": 1,
        "mockType": 1,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 1,
        "indexPackageName": "necoReporHeaderInfoSet",
        "dataSourceType": "SYNC",
        "documentTypeUuid": "2db2facd984f42ceabeeefeb4f0b9e44",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yujiang.ye.mock(yujiang.ye.mock)",
        "operator": "yujiang.ye.mock(yujiang.ye.mock)",
        "gmtCreate": "2024-07-15 20:38:59",
        "gmtModify": "2024-07-17 20:10:31",
        "uuid": "bce40b1301ef44589ae899485cddd3bd",
        "appName": "init",
        "name": "necoBifrostI18n",
        "displayName": "necoBifrostI18n",
        "status": 1,
        "partnerId": "5094bc5bf1f34f7e9a3a82f3359e9a13",
        "confidence": 1,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.59.210.31:8088/bridgeApi/system/config/value/withoutLogin/I18NController",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"key\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_COMPWEBADDR\"}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"C_S_NODERESULTLIST\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"data\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "outputTemplate": "{\"data\": \"${data}\"}",
        "dataType": 7,
        "chargeMethod": 1,
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "necoMock",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yujiang.ye.mock(yujiang.ye.mock)",
        "operator": "yujiang.ye.mock(yujiang.ye.mock)",
        "gmtCreate": "2024-07-10 21:06:05",
        "gmtModify": "2024-07-10 21:08:30",
        "uuid": "20d04024166c463db69488a0a7b8ae3b",
        "appName": "init",
        "name": "necoSalaxySceneList",
        "displayName": "necoSalaxySceneList",
        "status": 1,
        "partnerId": "5094bc5bf1f34f7e9a3a82f3359e9a13",
        "confidence": 1,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.59.207.54:8088/indexApi/scene/list",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"userNo\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_USERNO\"}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":5,\"displayName\":null,\"field\":\"S_B_BANKSTAFF\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"success\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_THIRDRESCODE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"code\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\n\"success\": \"${success}\",\n\"code\": \"${code}\"\n}",
        "dataType": 7,
        "chargeMethod": 1,
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "necoMock",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "cty(cty)",
        "operator": "cty(cty)",
        "gmtCreate": "2024-06-26 14:44:06",
        "gmtModify": "2024-06-26 14:44:06",
        "uuid": "f19ba0e1012845d8a0c0521e83ac7141",
        "appName": "init",
        "name": "ceshilimitConfig",
        "displayName": "测试无limitConfig能否保存同步",
        "status": 2,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 1,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "https://www.nihao.com",
        "postEtlHandlerName": "json",
        "preEtlHandlerName": "TestWebService",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"1ddd\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":false,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"333\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_ACTCNTRCERTNO\",\"formatCheck\":\"\"}]",
        "dataType": 7,
        "chargeMethod": 1,
        "mockFlag": 0,
        "cacheOpen": 0,
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "cty(cty)",
        "operator": "cty(cty)",
        "gmtCreate": "2024-06-26 14:13:50",
        "gmtModify": "2024-06-26 14:13:50",
        "uuid": "4c712295ead24a65b881dd04bf84b5cb",
        "appName": "init",
        "name": "321",
        "displayName": "123",
        "status": 2,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 1,
        "costLevel": 3,
        "cacheday": 7,
        "cacheUnit": "DAYS",
        "cacheUnitNumber": 7,
        "cacheInfrastructureType": "nosql",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "https://www.nihao.com",
        "postEtlHandlerName": "json",
        "preEtlHandlerName": "TestWebService",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"123\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":false,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTCRTDAT\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"123\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_ACTCNTRCERTNO\"}]",
        "dataType": 7,
        "chargeMethod": 1,
        "mockFlag": 0,
        "cacheOpen": 1,
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yujiang.ye(yujiang.ye)",
        "operator": "cty(cty)",
        "gmtCreate": "2024-06-20 17:16:08",
        "gmtModify": "2024-06-26 10:38:05",
        "uuid": "93496c6025734ee0b1ecd60fe11b526e",
        "appName": "init",
        "name": "necoSalaxyOk",
        "displayName": "necoSalaxyOk",
        "status": 2,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 1,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.59.210.31:8088/indexApi/ok",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"fromContract\":false,\"keyInclude\":false,\"mustInput\":false,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"OrgCode\",\"type\":\"constant\",\"value\":\"Tongdun\",\"uuid\":0},{\"dataType\":1,\"field\":\"S_S_CUSTNO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"custNo\",\"type\":\"variable\",\"uuid\":1}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_LOANINSTITUTION\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":null,\"type\":\"constant\",\"value\":\"Tongdun\",\"uuid\":0}]",
        "outputTemplate": "{\"data\": \"success\"}",
        "dataType": 7,
        "chargeMethod": 1,
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "byh(byh)",
        "operator": "byh(byh)",
        "gmtCreate": "2024-06-19 11:05:48",
        "gmtModify": "2024-12-16 10:09:05",
        "uuid": "bb560863bf3047c6b45c6746cbd59236",
        "appName": "init",
        "name": "BYH",
        "displayName": "BYH",
        "status": 1,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 1,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "post",
        "contentType": "application/x-www-form-urlencoded",
        "url": "https://apitest.tongdun.cn/bodyguard/apply/v4.5?partner_code=tsbank&partner_key=e0c34668823548f1936c378950db9242&app_name=360xfd",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"fromContract\":false,\"keyInclude\":false,\"mustInput\":false,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"biz_code\",\"type\":\"constant\",\"value\":\"credit_score\",\"uuid\":0},{\"dataType\":1,\"field\":\"S_S_CUSTNAME\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"account_name\",\"type\":\"variable\",\"uuid\":1},{\"dataType\":1,\"field\":\"S_S_PHONENO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"account_mobile\",\"type\":\"variable\",\"uuid\":2},{\"dataType\":1,\"field\":\"S_S_CERTID\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"id_number\",\"type\":\"variable\",\"uuid\":3}]",
        "outputConfig": "[{\"checked\":true,\"dataType\":3,\"defaultValue\":null,\"displayName\":null,\"field\":\"S_F_TDZHIXINFENG\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":0,\"paasDown\":false,\"serviceParam\":\"micro_cash_score\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"defaultValue\":null,\"displayName\":null,\"field\":\"S_S_POLITICALSTATUS\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"order\":1,\"paasDown\":false,\"serviceParam\":\"model_version\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\n    \"success\": true,\n    \"id\": \"************************\",\n    \"result_desc\": {\n        \"CREDIT_MODEL\": {\n            \"35a1cb47a68afe8b\": {\n                \"micro_cash_score\": \"${micro_cash_score}\",\n                \"model_version\": \"${model_version}\"\n            }\n        }\n    }\n}",
        "dataType": 7,
        "chargeMethod": 1,
        "contractId": "e0db083e01904969bf42e0bb1d5fde9f",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[{\"type\":5,\"limit\":2,\"begin\":5,\"end\":6}]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yujiang.ye(yujiang.ye)",
        "operator": "yujiang.ye(yujiang.ye)",
        "gmtCreate": "2024-06-18 16:47:12",
        "gmtModify": "2024-06-19 17:03:08",
        "uuid": "d9fcdae08b5340e3967218ac22d5641b",
        "appName": "init",
        "name": "TDZXF001",
        "displayName": "同盾智信分_演示接口_直走接口",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "post",
        "contentType": "application/x-www-form-urlencoded",
        "url": "https://apitest.tongdun.cn/bodyguard/apply/v4.5?partner_code=tsbank&partner_key=e0c34668823548f1936c378950db9242&app_name=360xfd",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"fromContract\":false,\"keyInclude\":false,\"mustInput\":false,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"biz_code\",\"type\":\"constant\",\"value\":\"credit_score\",\"uuid\":0},{\"dataType\":1,\"field\":\"S_S_CUSTNAME\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"account_name\",\"type\":\"variable\",\"uuid\":1},{\"dataType\":1,\"field\":\"S_S_PHONENO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"account_mobile\",\"type\":\"variable\",\"uuid\":2},{\"dataType\":1,\"field\":\"S_S_CERTID\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"id_number\",\"type\":\"variable\",\"uuid\":3}]",
        "outputConfig": "[{\"checked\":true,\"dataType\":3,\"displayName\":null,\"field\":\"S_F_TDZHIXINFENG\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"micro_cash_score\",\"type\":\"variable\",\"value\":null,\"uuid\":0},{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_POLITICALSTATUS\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"model_version\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\n    \"success\": true,\n    \"id\": \"************************\",\n    \"result_desc\": {\n        \"CREDIT_MODEL\": {\n            \"35a1cb47a68afe8b\": {\n                \"micro_cash_score\": \"${micro_cash_score}\",\n                \"model_version\": \"${model_version}\"\n            }\n        }\n    }\n}",
        "dataType": 7,
        "chargeMethod": 1,
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yujiang.ye(yujiang.ye)",
        "operator": "yujiang.ye(yujiang.ye)",
        "gmtCreate": "2024-06-17 20:33:00",
        "gmtModify": "2024-06-18 11:06:57",
        "uuid": "61a6125cd5ab4e3dabe0ae737f54f3f0",
        "name": "necoBifrostOk",
        "displayName": "necoBifrostOk",
        "status": 1,
        "partnerId": "5a36a98863b948bf9db56dbba55175f3",
        "confidence": 1,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.59.210.31:8088/bridgeApi/ok",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_USERNO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":false,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"userNo\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_CUSTCRTOG\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":null,\"type\":\"constant\",\"value\":\"Tongdun\",\"uuid\":0},{\"checked\":true,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_REALINFOITGRFLAG\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"data\",\"type\":\"variable\",\"value\":null,\"uuid\":1}]",
        "outputTemplate": "{\n \"originalData\": {\n    \"content\": \"${data}\"\n }\n}",
        "dataType": 1,
        "chargeMethod": 2,
        "contractId": "e0db083e01904969bf42e0bb1d5fde9f",
        "mockFlag": 0,
        "mockType": 2,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "neco",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "dy(dy)",
        "operator": "dy(dy)",
        "gmtCreate": "2024-06-05 19:25:40",
        "gmtModify": "2024-06-12 16:04:57",
        "uuid": "56a9e2d85a3f4ebfb3bf075a33dd76f9",
        "name": "testFeatureDy",
        "displayName": "测试外数指标dy",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 3,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.58.16.250:9200/tiance-dev-tianzuo_indexresult-********?pretty",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_PADINCAP\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":false,\"paasDown\":false,\"sendSpace\":\"body\",\"serviceParam\":\"a\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_FRZMATUREDATE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"date\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "outputTemplate": "{\n    \"tiance-dev-tianzuo_indexresult-********\": {\n        \"aliases\": {\n            \"tiance-dev-tianzuo_indexresult-r-********\": {},\n            \"tiance-dev-tianzuo_indexresult-r-20240604\": {},\n            \"tiance-dev-tianzuo_indexresult-r-20240605\": {},\n            \"tiance-dev-tianzuo_indexresult-r-20240606\": {},\n            \"tiance-dev-tianzuo_indexresult-r-20240607\": {},\n            \"tiance-dev-tianzuo_indexresult-r-20240608\": {},\n            \"tiance-dev-tianzuo_indexresult-r-20240609\": {},\n            \"tiance-dev-tianzuo_indexresult-w-********\": {},\n            \"tiance-dev-tianzuo_indexresult-w-20240604\": {},\n            \"tiance-dev-tianzuo_indexresult-w-20240605\": {},\n            \"tiance-dev-tianzuo_indexresult-w-20240606\": {},\n            \"tiance-dev-tianzuo_indexresult-w-20240607\": {},\n            \"tiance-dev-tianzuo_indexresult-w-20240608\": {},\n            \"tiance-dev-tianzuo_indexresult-w-20240609\": {}\n        },\n        \"mappings\": {\n            \"dynamic_templates\": [\n                {\n                    \"str\": {\n                        \"match_mapping_type\": \"string\",\n                        \"mapping\": {\n                            \"fields\": {\n                                \"raw\": {\n                                    \"type\": \"keyword\"\n                                }\n                            },\n                            \"norms\": false,\n                            \"type\": \"text\"\n                        }\n                    }\n                }\n            ],\n            \"date_detection\": false,\n            \"properties\": {\n                \"callDate\": {\n                    \"type\": \"long\",\n                    \"ignore_malformed\": true\n                },\n                \"channelSequenceId\": {\n                    \"type\": \"text\",\n                    \"fields\": {\n                        \"raw\": {\n                            \"type\": \"keyword\",\n                            \"ignore_above\": 256\n                        }\n                    },\n                    \"norms\": false\n                },\n                \"code\": {\n                    \"type\": \"integer\",\n                    \"ignore_malformed\": true\n                },\n                \"hitIndex\": {\n                    \"type\": \"nested\",\n                    \"properties\": {\n                        \"indexCategoryUuid\": {\n                            \"type\": \"text\",\n                            \"fields\": {\n                                \"raw\": {\n                                    \"type\": \"keyword\",\n                                    \"ignore_above\": 256\n                                }\n                            },\n                            \"norms\": false\n                        },\n                        \"indexDisplayName\": {\n                            \"type\": \"text\",\n                            \"fields\": {\n                                \"raw\": {\n                                    \"type\": \"keyword\",\n                                    \"ignore_above\": 256\n                                }\n                            },\n                            \"norms\": false\n                        },\n                        \"indexName\": {\n                            \"type\": \"text\",\n                            \"fields\": {\n                                \"raw\": {\n                                    \"type\": \"keyword\",\n                                    \"ignore_above\": 256\n                                }\n                            },\n                            \"norms\": false\n                        },\n                        \"indexUuid\": {\n                            \"type\": \"text\",\n                            \"fields\": {\n                                \"raw\": {\n                                    \"type\": \"keyword\",\n                                    \"ignore_above\": 256\n                                }\n                            },\n                            \"norms\": false\n                        },\n                        \"parseResult\": {\n                            \"properties\": {\n                                \"doubleResult\": {\n                                    \"type\": \"double\"\n                                },\n                                \"longResult\": {\n                                    \"type\": \"long\",\n                                    \"ignore_malformed\": true\n                                },\n                                \"strResult\": {\n                                    \"type\": \"keyword\",\n                                    \"ignore_above\": 256\n                                }\n                            }\n                        },\n                        \"result\": {\n                            \"type\": \"text\",\n                            \"fields\": {\n                                \"raw\": {\n                                    \"type\": \"keyword\",\n                                    \"ignore_above\": 256\n                                }\n                            },\n                            \"norms\": false\n                        },\n                        \"resultType\": {\n                            \"type\": \"text\",\n                            \"fields\": {\n                                \"raw\": {\n                                    \"type\": \"keyword\",\n                                    \"ignore_above\": 256\n                                }\n                            },\n                            \"norms\": false\n                        }\n                    }\n                },\n                \"indexPackageName\": {\n                    \"type\": \"text\",\n                    \"fields\": {\n                        \"raw\": {\n                            \"type\": \"keyword\",\n                            \"ignore_above\": 256\n                        }\n                    },\n                    \"norms\": false\n                },\n                \"taskUuid\": {\n                    \"type\": \"text\",\n                    \"fields\": {\n                        \"raw\": {\n                            \"type\": \"keyword\",\n                            \"ignore_above\": 256\n                        }\n                    },\n                    \"norms\": false\n                }\n            }\n        },\n        \"settings\": {\n            \"index\": {\n                \"routing\": {\n                    \"allocation\": {\n                        \"include\": {\n                            \"_tier_preference\": \"data_content\"\n                        }\n                    }\n                },\n                \"number_of_shards\": \"5\",\n                \"provided_name\": \"tiance-dev-tianzuo_indexresult-********\",\n                \"creation_date\":\"${date}\",\n                \"number_of_replicas\": \"0\",\n                \"uuid\": \"D_JnoT12Rhez-cscEngnlg\",\n                \"version\": {\n                    \"created\": \"7171199\"\n                }\n            }\n        }\n    }\n}",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "testFeatureSetDy",
        "dataSourceType": "SYNC",
        "documentTypeUuid": "318e0c112f7747dab7f740f0af3d445f",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "linxiao.cai(linxiao.cai)",
        "operator": "linxiao.cai(linxiao.cai)",
        "gmtCreate": "2024-05-23 12:09:09",
        "gmtModify": "2024-05-23 14:14:26",
        "uuid": "6e867bf1b698458b93909d67fd522c47",
        "name": "queryUser",
        "displayName": "queryUser",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheUnit": "DAYS",
        "methodType": "webservice",
        "contentType": "default",
        "url": "http://10.58.16.250:8082/ws/api?wsdl",
        "postEtlHandlerName": "json",
        "preEtlHandlerName": "TestWebService",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_MOBINO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"mobileCode\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_CHILDRENFLOWDECISION\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"1\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "ASYNC",
        "asyncMethodType": "webservice",
        "asyncPollTimeInterval": 1000,
        "asyncPollMaxCount": 10,
        "asyncContentType": "default",
        "asyncUrl": "http://10.58.16.250:8082/ws/api?wsdl",
        "asyncPostEtlHandlerName": "json",
        "asyncInputConfig": "[{\"displayName\":null,\"serviceParam\":\"mobileCode2\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_MOBINO\"}]",
        "asyncOutputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_CHILDRENFLOWDECISION\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"2\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "asyncProxy": 0,
        "asyncProxyInfo": "",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "admin(administrator)",
        "operator": "admin(administrator)",
        "gmtCreate": "2024-05-22 17:23:01",
        "gmtModify": "2024-05-22 17:23:01",
        "uuid": "4c68aae86d354fbf8f2b515cb6202839",
        "name": "add",
        "displayName": "add",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "webservice",
        "contentType": "default",
        "url": "http://127.0.0.1:8081/ws/api?wsdl",
        "postEtlHandlerName": "json",
        "preEtlHandlerName": "TestWebService",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_MOBINO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"mobileCode\",\"type\":\"variable\",\"uuid\":0},{\"dataType\":1,\"field\":\"S_S_CONT1NAME\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"userID\",\"type\":\"variable\",\"uuid\":1}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_CHILDRENFLOWDECISIONNAME\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"1\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "admin(administrator)",
        "operator": "admin(administrator)",
        "gmtCreate": "2024-05-22 16:09:42",
        "gmtModify": "2024-05-22 19:08:58",
        "uuid": "6a2e196eaf924911af5e9454986653f7",
        "name": "UserService",
        "displayName": "UserService",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "webservice",
        "contentType": "default",
        "url": "http://10.58.16.250:8081/ws/api?wsdl",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_MOBINO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"mobileCode\",\"type\":\"variable\",\"uuid\":0},{\"dataType\":1,\"field\":\"S_S_CONT1NAME\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"userID\",\"type\":\"variable\",\"uuid\":1}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_CHILDRENFLOWDECISIONNAME\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"1\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "admin(administrator)",
        "operator": "admin(administrator)",
        "gmtCreate": "2024-05-22 11:44:58",
        "gmtModify": "2024-05-22 15:36:26",
        "uuid": "6c39020825eb480fbcaa73423135baee",
        "name": "111",
        "displayName": "111",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "webservice",
        "contentType": "default",
        "url": "http://127.0.0.1:8081/ws/api?wsdl",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_MOBINO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"mobileCode\",\"type\":\"variable\",\"uuid\":0},{\"dataType\":1,\"field\":\"S_S_CONT1NAME\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"userID\",\"type\":\"variable\",\"uuid\":1}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_CHILDRENFLOWDECISIONNAME\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"1\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yangzhao(yangzhao)",
        "operator": "yangzhao(yangzhao)",
        "gmtCreate": "2024-05-16 13:21:45",
        "gmtModify": "2024-05-16 14:27:25",
        "uuid": "9cbfdc70e94b4a54801caab0234743f8",
        "name": "bugfix051502",
        "displayName": "测试051501",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 2,
        "costLevel": 2,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.59.207.54:8088/testApi/taiKang",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":2,\"field\":\"S_N_AGE\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"age\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":true,\"dataType\":2,\"displayName\":null,\"field\":\"S_N_AGE\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yangzhao(yangzhao)",
        "operator": "yangzhao(yangzhao)",
        "gmtCreate": "2024-05-16 11:30:21",
        "gmtModify": "2024-05-16 14:22:15",
        "uuid": "496332add8e34820aaf9b7e8e0fe44cc",
        "name": "bugfix051601",
        "displayName": "webservice接口异常测试",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "webservice",
        "contentType": "default",
        "url": "http://schemas.xmlsoap.org/wsdl/getMobileCodeInfo",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_MOBINO\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"mobileCode\",\"type\":\"variable\",\"uuid\":0},{\"dataType\":1,\"field\":\"S_S_CONT1NAME\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"userID\",\"type\":\"variable\",\"uuid\":1}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_CHILDRENFLOWDECISIONNAME\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"1\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 1,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "dy(dy)",
        "operator": "dy(dy)",
        "gmtCreate": "2024-05-15 17:56:00",
        "gmtModify": "2024-05-15 17:56:16",
        "uuid": "14452a7d07ae4149ba00d2aff8d57c90",
        "name": "testService",
        "displayName": "测试接口",
        "status": 2,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 2,
        "costLevel": 3,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "post",
        "contentType": "application/x-www-form-urlencoded",
        "url": "http://www.tongdun.cn",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"aa\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":false,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_FRZMATUREDATE\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"ssda\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_PADINCAP\"}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 1,
        "mockType": 1,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "yangzhao(yangzhao)",
        "operator": "yangzhao(yangzhao)",
        "gmtCreate": "2024-05-10 16:04:39",
        "gmtModify": "2024-05-15 15:45:28",
        "uuid": "64f94271f3054f2583e8420ce92c5f37",
        "name": "bugfix01",
        "displayName": "异步接口异常问题复现",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 2,
        "cacheUnit": "DAYS",
        "methodType": "get",
        "contentType": "default",
        "url": "www.baidu.com",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"phone\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_MOBINO\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"valadte\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_CREDITVALIDITY\"}]",
        "dataType": 2,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "ASYNC",
        "asyncMethodType": "get",
        "asyncPollTimeInterval": 1000,
        "asyncPollMaxCount": 10,
        "asyncContentType": "default",
        "asyncUrl": "www.sina.com",
        "asyncPostEtlHandlerName": "json",
        "asyncInputConfig": "[{\"displayName\":null,\"serviceParam\":\"phone\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"url\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_MOBINO\"}]",
        "asyncOutputConfig": "[{\"displayName\":null,\"serviceParam\":\"validate\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"field\":\"S_S_CREDITVALIDITY\"}]",
        "asyncProxy": 0,
        "asyncProxyInfo": "",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "zyy(zyy)",
        "operator": "yangzhao(yangzhao)",
        "gmtCreate": "2024-04-12 14:12:44",
        "gmtModify": "2024-05-15 17:20:35",
        "uuid": "5145e77d833b4ec2aa4b09bb5fc3cf25",
        "name": "NBATEST",
        "displayName": "NBATEST",
        "status": 1,
        "partnerId": "74338757d9084d0f8e1b634483a41dc8",
        "confidence": 1,
        "costLevel": 1,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "webservice",
        "contentType": "default",
        "url": "http://10.59.207.54:8088/testApi/nba",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"dataType\":1,\"field\":\"S_S_FRZMATUREDATE\",\"fromContract\":false,\"keyInclude\":true,\"mustInput\":true,\"paasDown\":false,\"sendSpace\":\"url\",\"serviceParam\":\"time\",\"type\":\"variable\",\"uuid\":0}]",
        "outputConfig": "[{\"checked\":false,\"dataType\":1,\"displayName\":null,\"field\":\"S_S_MPSUBID\",\"formatCheck\":null,\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"paasDown\":false,\"serviceParam\":\"cus\",\"type\":\"variable\",\"value\":null,\"uuid\":0}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "b8d283790d4f4f438694d2ab10648a30",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "NBA",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      },
      {
        "canWriter": true,
        "creator": "zyy(zyy)",
        "operator": "dy(dy)",
        "gmtCreate": "2024-04-10 10:23:05",
        "gmtModify": "2024-06-05 16:06:23",
        "uuid": "ee98abe8b2884f1db08c82d5f45ebfff",
        "name": "wyTest",
        "displayName": "wyTest",
        "status": 1,
        "partnerId": "a4ced98eb646438c96c1d38624f1e8e7",
        "confidence": 1,
        "costLevel": 1,
        "cacheUnit": "DAYS",
        "timeout": 20000,
        "methodType": "get",
        "contentType": "default",
        "url": "http://10.59.207.54:8088/testApi/taiKang",
        "postEtlHandlerName": "json",
        "inputConfig": "[{\"displayName\":null,\"serviceParam\":\"cutomerNo\",\"type\":\"variable\",\"value\":null,\"dataType\":1,\"mustInput\":true,\"sendSpace\":\"body\",\"keyInclude\":true,\"uuid\":0,\"field\":\"S_S_CUSTNO\"}]",
        "outputConfig": "[{\"displayName\":null,\"serviceParam\":\"cutomerNo\",\"type\":\"variable\",\"includeCheck\":{\"checkType\":1,\"checkValue\":null},\"checked\":false,\"uuid\":0,\"dataType\":1,\"field\":\"S_S_CUSTNO\"}]",
        "dataType": 1,
        "chargeMethod": 1,
        "contractId": "605041757b1646b8b893898ae4c2012a",
        "mockFlag": 0,
        "cacheOpen": 0,
        "limitConfig": "[]",
        "proxy": 0,
        "proxyInfo": "",
        "partnerDisplayName": "泰康",
        "invokePolicy": 0,
        "indexPackageName": "",
        "dataSourceType": "SYNC",
        "pagination": false
      }
    ]
  }
}