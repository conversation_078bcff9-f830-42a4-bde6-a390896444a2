module.exports = {
  "code": 200,
  "message": "成功",
  "success": true,
  "data": {
    "id": 23,
    "uuid": "a483313fcceb4f47961931f02f03fd8c",
    "gmtCreate": *************,
    "gmtModified": *************,
    "account": "byh",
    "userName": "byh",
    "salt": "44ebff8345e143d7ba61710b7572cc7f",
    "orgUuid": "a8202aea546f48979754bdd45c471b08",
    "roleUuids": "[\"ee8dbc99831b4a9cb17578b51bbb09e0\",\"cfb9c519fde64476bc6ca01ba324a6a6\"]",
    "roleUuidsSelected": "[\"ee8dbc99831b4a9cb17578b51bbb09e0\",\"cfb9c519fde64476bc6ca01ba324a6a6\"]",
    "avatar": "male1",
    "expiration": *************,
    "gender": 0,
    "appName": "[\"test_add\",\"bug404503\",\"init\",\"necoTestApp\",\"JYTest\"]",
    "status": 0,
    "updateBy": "fujun.cai",
    "lang": "cn",
    "theme": "default",
    "layout": "default",
    "simplified": 1,
    "tokenMD5": "EEBD7D2CA464DB860FDA437BBE8020EB",
    "verificationCode": "",
    "tryTime": 0,
    "tryDate": *************,
    "updatePwdTime": *************,
    "firstLogin": "1",
    "isDelete": 0,
    "portalInfo": "[]",
    "orgName": "同盾科技",
    "orgCode": "TongDun",
    "enOrgName": "同盾科技",
    "roles": [
      {
        "id": 1,
        "uuid": "ee8dbc99831b4a9cb17578b51bbb09e0",
        "gmtCreate": 1635130208000,
        "gmtModify": 1724141015000,
        "code": "superman",
        "name": "超级管理员",
        "enName": "SuperAdmin",
        "type": "default",
        "orgUuid": "a8202aea546f48979754bdd45c471b08",
        "createBy": "admin",
        "updateBy": "admin"
      },
      {
        "id": 11,
        "uuid": "cfb9c519fde64476bc6ca01ba324a6a6",
        "gmtCreate": 1720578182000,
        "gmtModify": 1721787144000,
        "code": "superadmin",
        "name": "超级管理",
        "type": "default",
        "orgUuid": "a8202aea546f48979754bdd45c471b08",
        "createBy": "wrm",
        "updateBy": "wrm"
      }
    ],
    "apps": [
      {
        "id": 2,
        "uuid": "6fba24597d814b74bcfff2513ad0aa05",
        "gmtCreate": 1712900681000,
        "gmtModified": 1722413332000,
        "name": "test_add",
        "displayName": "测试新增渠道",
        "partnerCode": "kratos",
        "have": true,
        "secretKey": "hhdAmlYUUXWTuwPxUVlnRoEGDszoKvXgCYCclsCx",
        "signForSwitch": false,
        "signForWrite": false
      },
      {
        "id": 3,
        "uuid": "e86784e65645477a862a64625c695b7b",
        "gmtCreate": 1713422494000,
        "gmtModified": 1722418077000,
        "name": "bug404503",
        "displayName": "bug404503",
        "partnerCode": "kratos",
        "have": true,
        "secretKey": "dlgEpbeQJlXaxGGIMOVayNJHilUMkeIgbbrWeITY",
        "signForSwitch": false,
        "signForWrite": false
      },
      {
        "id": 1,
        "uuid": "3daebd1f764911ee948efafd476b2800",
        "gmtCreate": 1635130473000,
        "gmtModified": 1635130497000,
        "name": "init",
        "displayName": "initApp",
        "enDisplayName": "initApp",
        "partnerCode": "kratos",
        "have": true,
        "description": "initApp"
      },
      {
        "id": 4,
        "uuid": "5efa424887c84c4798df4f9438cfcf81",
        "gmtCreate": 1718072251000,
        "gmtModified": 1722409977000,
        "name": "necoTestApp",
        "displayName": "necoTestApp",
        "partnerCode": "kratos",
        "have": true,
        "secretKey": "FpJsISPRmyqopMwNNPWfBtvuLxVkwOoNywDjKKKI",
        "signForSwitch": false,
        "signForWrite": false
      },
      {
        "id": 6,
        "uuid": "bad5b23350bb4895945f15afa120e75b",
        "gmtCreate": 1721183484000,
        "gmtModified": 1721183484000,
        "name": "JYTest",
        "displayName": "交易测试",
        "partnerCode": "kratos",
        "have": true,
        "secretKey": "GPLemqqzpNkbglGcxQhrAvBQkOPKyKRBwRzVOcrw",
        "signForSwitch": false,
        "signForWrite": false
      }
    ],
    "token": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI4MzdDNzJDMzhBRUZGMEU5RDcwMUExNUYyNTk2M0FCNiIsInN1YiI6ImJ5aCIsImlzcyI6ImZyYXVkbWV0cml4IiwiaWF0IjoxNzM1Mjg5NzU5LCJleHAiOjE3MzU4OTQ1NTl9.82OcqvzaLprwgB9rRIR_D_6rSqKL0gSKHQxztXdj-ZY",
    "loginTime": "2024-12-27 16:55:59",
    "ipAddr": "bridgeApi;************",
    "orgGroup": {
      "id": 1,
      "uuid": "a8202aea546f48979754bdd45c471b08",
      "gmtCreate": 1635130131000,
      "gmtModify": 1724141014000,
      "code": "TongDun",
      "level": 1,
      "name": "同盾科技",
      "enName": "tongdun",
      "createBy": "admin",
      "updateBy": "admin",
      "orgAttribute": 2,
      "children": [
        {
          "id": 3633,
          "uuid": "bda6fbe7526e409b9b8dd7b592f51289",
          "gmtCreate": 1734420028000,
          "gmtModify": 1734420028000,
          "code": "znjg",
          "level": 2,
          "parentUuid": "a8202aea546f48979754bdd45c471b08",
          "name": "销售管理部",
          "orgAttribute": 1,
          "children": []
        },
        {
          "id": 2,
          "uuid": "83a65825880b4479b43d2d103f1fdff2",
          "gmtCreate": 1712837786000,
          "gmtModify": 1724141014000,
          "code": "test",
          "level": 2,
          "parentUuid": "a8202aea546f48979754bdd45c471b08",
          "name": "测试机构",
          "orgAttribute": 2,
          "children": [
            {
              "id": 4,
              "uuid": "34008f7fd76548058f317994b3abdef4",
              "gmtCreate": 1715154951000,
              "gmtModify": 1724141014000,
              "code": "test3",
              "level": 3,
              "parentUuid": "83a65825880b4479b43d2d103f1fdff2",
              "name": "测试机构3",
              "orgAttribute": 2,
              "children": [
                {
                  "id": 3626,
                  "uuid": "8f24261b2b3b452a94e7992337f71845",
                  "gmtCreate": 1721982780000,
                  "gmtModify": 1724141014000,
                  "code": "0726",
                  "level": 4,
                  "parentUuid": "34008f7fd76548058f317994b3abdef4",
                  "name": "测试机构4",
                  "orgAttribute": 2,
                  "children": [
                    {
                      "id": 3627,
                      "uuid": "40932f01f8e24286ad64619feb8956ef",
                      "gmtCreate": 1722409939000,
                      "gmtModify": 1724141014000,
                      "code": "test_05",
                      "level": 5,
                      "parentUuid": "8f24261b2b3b452a94e7992337f71845",
                      "name": "测试机构5",
                      "orgAttribute": 2,
                      "children": []
                    }
                  ]
                }
              ]
            }
          ]
        },
        {
          "id": 3628,
          "uuid": "e07333e0458647d4baa80047724b674c",
          "gmtCreate": 1733194276000,
          "gmtModify": 1733194276000,
          "code": "testorg6",
          "level": 2,
          "parentUuid": "a8202aea546f48979754bdd45c471b08",
          "name": "测试机构6",
          "orgAttribute": 2,
          "children": [
            {
              "id": 3629,
              "uuid": "cb56e593b5c34c448e63f47b04aa3150",
              "gmtCreate": 1733194298000,
              "gmtModify": 1733194298000,
              "code": "testorg7",
              "level": 3,
              "parentUuid": "e07333e0458647d4baa80047724b674c",
              "name": "测试机构7",
              "orgAttribute": 2,
              "children": [
                {
                  "id": 3630,
                  "uuid": "328d8479e3464c0d90f0e9c5ffb6d76c",
                  "gmtCreate": 1733194328000,
                  "gmtModify": 1733194328000,
                  "code": "testorg8",
                  "level": 4,
                  "parentUuid": "cb56e593b5c34c448e63f47b04aa3150",
                  "name": "测试机构8",
                  "orgAttribute": 2,
                  "children": []
                }
              ]
            },
            {
              "id": 3635,
              "uuid": "9e1491ff16854db58c36d479d2dec81e",
              "gmtCreate": 1734939877000,
              "gmtModify": 1734939877000,
              "code": "testorg7_1",
              "level": 3,
              "parentUuid": "e07333e0458647d4baa80047724b674c",
              "name": "测试机构7_1",
              "orgAttribute": 2,
              "children": []
            }
          ]
        },
        {
          "id": 3631,
          "uuid": "626c537e123e462a84454e4e28425f6c",
          "gmtCreate": 1733279415000,
          "gmtModify": 1733279415000,
          "code": "testorg9",
          "level": 2,
          "parentUuid": "a8202aea546f48979754bdd45c471b08",
          "name": "测试机构9",
          "orgAttribute": 2,
          "children": [
            {
              "id": 3632,
              "uuid": "8773ead527804912bc26bce8206f1779",
              "gmtCreate": 1733279434000,
              "gmtModify": 1733279434000,
              "code": "testorg10",
              "level": 3,
              "parentUuid": "626c537e123e462a84454e4e28425f6c",
              "name": "测试机构10",
              "orgAttribute": 2,
              "children": []
            }
          ]
        }
      ]
    }
  }
}