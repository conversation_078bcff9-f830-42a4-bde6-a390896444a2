module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "total": 20,
    "changeNum": 0,
    "serviceRanks": [
      {
        "serviceCode": "222",
        "serviceName": "测试服务接口222",
        "count": 1
      },
      {
        "serviceCode": "741931",
        "serviceName": "测试数据比哦啊",
        "count": 1
      },
      {
        "serviceCode": "sctest00009",
        "serviceName": "测试服务接口授权",
        "count": 1
      },
      {
        "serviceCode": "sctestyc8",
        "serviceName": "20个真实接口限流",
        "count": 1
      },
      {
        "serviceCode": "sctestyc6",
        "serviceName": "单个真实接口限流",
        "count": 3
      },
      {
        "serviceCode": "12345678",
        "serviceName": "1234567",
        "count": 4
      },
      {
        "serviceCode": "asyncTest",
        "serviceName": "异步test",
        "count": 5
      },
      {
        "serviceCode": "sctest0123",
        "serviceName": "测试123",
        "count": 7
      },
      {
        "serviceCode": "sctestyc5",
        "serviceName": "单个mock接口限流",
        "count": 8
      },
      {
        "serviceCode": "sctest001",
        "serviceName": "sctest001",
        "count": 10
      },
      {
        "serviceCode": "1231",
        "serviceName": "测试",
        "count": 29
      },
      {
        "serviceCode": "sctest013456",
        "serviceName": "测试全组件",
        "count": 61
      },
      {
        "serviceCode": "sctestyc2",
        "serviceName": "单个真实接口不限流",
        "count": 63622
      },
      {
        "serviceCode": "sctestyc1",
        "serviceName": "单个mock接口不限流",
        "count": 319441
      }
    ],
    "channelRanks": [
      {
        "channelCode": "init",
        "channelName": "initApp",
        "count": 18,
        "rate": 0.9
      },
      {
        "channelCode": "necoTestApp",
        "channelName": "necoTestApp",
        "count": 1,
        "rate": 0.05
      },
      {
        "channelCode": "test_add",
        "channelName": "测试新增渠道",
        "count": 1,
        "rate": 0.05
      }
    ]
  }
}