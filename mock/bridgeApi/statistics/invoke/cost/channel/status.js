module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "code": "init",
      "name": "initApp",
      "total": 383193,
      "successCount": 383167,
      "successRate": 1,
      "failCount": 26,
      "failRate": 0,
      "succAvgCost": 101,
      "failAvgCost": 5645
    },
    {
      "code": "necoTestApp",
      "name": "necoTestApp",
      "total": 3,
      "successCount": 0,
      "successRate": 0,
      "failCount": 3,
      "failRate": 1,
      "succAvgCost": 0,
      "failAvgCost": 166
    },
    {
      "code": "test_add",
      "name": "测试新增渠道",
      "total": 2,
      "successCount": 2,
      "successRate": 1,
      "failCount": 0,
      "failRate": 0,
      "succAvgCost": 898,
      "failAvgCost": 0
    },
    {
      "code": "bug404503",
      "name": "bug404503",
      "total": 1,
      "successCount": 1,
      "successRate": 1,
      "failCount": 0,
      "failRate": 0,
      "succAvgCost": 274,
      "failAvgCost": 0
    }
  ]
}