module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "canWriter": true,
    "creator": "csb",
    "operator": "chen.shen",
    "uuid": "f87020d4f49c4135a97beb91f7495317",
    "code": "sctest012345_copy",
    "displayName": "测试全组件2222222",
    "status": 3,
    "version": 4,
    "graphJson": "{\"code\":\"sctest012345_copy\",\"flowNodeDefinitions\":[{\"outgoingFields\":[],\"x\":38,\"name\":\"开始\",\"y\":161,\"attributes\":{},\"incomingFields\":[],\"id\":\"bab1bcb0b61a11ef824b858fe026bdfb\",\"nodeType\":\"StartFlowNode\"},{\"outgoingFields\":[],\"x\":182,\"name\":\"判断开始\",\"y\":167,\"attributes\":{\"type\":\"start\"},\"incomingFields\":[],\"id\":\"bd86d5b0b61a11ef824b858fe026bdfb\",\"nodeType\":\"ExclusiveGateway\"},{\"outgoingFields\":[{\"mappingName\":\"age\",\"fieldName\":\"S_N_AGE\",\"displayName\":\"年龄\"},{\"mappingName\":\"code\",\"fieldName\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\"},{\"mappingName\":\"responseCode\",\"fieldName\":\"S_S_THIRDRESCODE\",\"displayName\":\"[系统]三方返回状态码\"}],\"x\":421,\"name\":\"真实接口限流\",\"y\":114,\"attributes\":{\"originInput\":{\"abc\":\"S_S_CUSTCRTDAT\"},\"async\":false,\"exceptionTerminate\":false,\"originOutput\":{\"code\":\"S_S_METRICRESULT\",\"age\":\"S_N_AGE\"},\"featureCodes\":[{\"name\":\"necoReportHeaderJavaIndex\",\"dName\":\"neco报文头信息Java指标\"}],\"thirdServiceCode\":\"45678\",\"thirdServiceName\":\"真实接口限流\"},\"incomingFields\":[{\"mappingName\":\"abc\",\"fieldName\":\"S_S_CUSTCRTDAT\",\"displayName\":\"客户开立日期\"}],\"id\":\"c203d250b61a11ef824b858fe026bdfb\",\"nodeType\":\"FeatureServiceNode\"},{\"outgoingFields\":[],\"x\":431,\"name\":\"智能路由\",\"y\":181,\"attributes\":{\"name\":\"智能路由\"},\"incomingFields\":[],\"id\":\"c8874d50b61a11ef824b858fe026bdfb\",\"nodeType\":\"RouteServiceNode\"},{\"outgoingFields\":[],\"flowNodeDefinitions\":[{\"outgoingFields\":[],\"x\":214,\"name\":\"开始\",\"y\":133,\"attributes\":{},\"incomingFields\":[],\"id\":\"aeaa83e095e311efa7343997b834dae7\",\"nodeType\":\"StartFlowNode\"},{\"outgoingFields\":[{\"mappingName\":\"dataset_json\",\"fieldName\":\"S_S_CUSTCRTDAT\"}],\"x\":419,\"name\":\"87687\",\"y\":140,\"attributes\":{\"limitNum\":\"5\",\"nodeName\":\"87687\",\"previewTables\":\"\",\"outTableType\":2,\"contextField\":{},\"sqlParams\":[{\"name\":\"S_N_WEEKLYTIME\",\"type\":\"variable\",\"value\":\"10\"},{\"name\":\"b\",\"type\":\"date\",\"value\":\"$[yyyy-MM-dd-1d]\"}],\"sqlContent\":\"select * from test_dy.for_test_2 a inner join test_dy.for_test b where a.date < '${b}' \\nand b.id < ${S_N_WEEKLYTIME}\",\"type\":\"sqlSet\",\"logColumns\":[{\"dataIndex\":\"date\",\"title\":\"date\",\"key\":\"date\"},{\"dataIndex\":\"name\",\"title\":\"name\",\"key\":\"name\"},{\"dataIndex\":\"id\",\"title\":\"id\",\"key\":\"id\"},{\"dataIndex\":\"age\",\"title\":\"age\",\"key\":\"age\"}],\"dataSourceId\":114,\"messages\":[],\"testResult\":true,\"dataSourceType\":\"mysql\",\"resData\":[],\"showLog\":true},\"incomingFields\":[],\"id\":\"afe6112095e311efa7343997b834dae7\",\"nodeType\":\"DataTableServiceNode\"},{\"outgoingFields\":[],\"x\":774,\"name\":\"结束\",\"y\":130,\"attributes\":{},\"incomingFields\":[],\"id\":\"b18e910095e311efa7343997b834dae7\",\"nodeType\":\"EndFlowNode\"},{\"outgoingFields\":[{\"mappingName\":\"age\",\"fieldName\":\"S_N_AGE\",\"displayName\":\"年龄\"},{\"mappingName\":\"code\",\"fieldName\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\"},{\"mappingName\":\"responseCode\",\"fieldName\":\"S_S_THIRDRESCODE\",\"displayName\":\"[系统]三方返回状态码\"}],\"x\":412,\"name\":\"真实接口限流\",\"y\":236,\"attributes\":{\"originInput\":{\"abc\":\"S_S_CUSTCRTDAT\"},\"async\":false,\"exceptionTerminate\":true,\"originOutput\":{\"code\":\"S_S_METRICRESULT\",\"age\":\"S_N_AGE\"},\"featureCodes\":[{\"name\":\"necoRRHReportCustIndex\",\"dName\":\"necoRRHReportCustIndex\"}],\"thirdServiceCode\":\"45678\",\"thirdServiceName\":\"真实接口限流\"},\"incomingFields\":[{\"mappingName\":\"abc\",\"fieldName\":\"S_S_CUSTCRTDAT\",\"displayName\":\"客户开立日期\"}],\"id\":\"9198fe00b2f111efb57295e89acd035a\",\"nodeType\":\"FeatureServiceNode\"}],\"flowLineDefinitions\":[{\"sourceNodeId\":\"aeaa83e095e311efa7343997b834dae7\",\"attributes\":{},\"id\":\"b31b4b80-95e3-11ef-a734-3997b834dae7\",\"targetNodeId\":\"afe6112095e311efa7343997b834dae7\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"afe6112095e311efa7343997b834dae7\",\"attributes\":{},\"id\":\"95c828c0-b2f1-11ef-b572-95e89acd035a\",\"targetNodeId\":\"9198fe00b2f111efb57295e89acd035a\",\"toPoint\":\"0\",\"fromPoint\":2},{\"sourceNodeId\":\"9198fe00b2f111efb57295e89acd035a\",\"attributes\":{},\"id\":\"99567fa0-b2f1-11ef-b572-95e89acd035a\",\"targetNodeId\":\"b18e910095e311efa7343997b834dae7\",\"toPoint\":\"3\",\"fromPoint\":1}],\"x\":417,\"name\":\"测试mysql数据表模版\",\"y\":252,\"attributes\":{\"flowNodeDefinitions\":[{\"outgoingFields\":[],\"x\":214,\"name\":\"开始\",\"y\":133,\"attributes\":{},\"incomingFields\":[],\"id\":\"aeaa83e095e311efa7343997b834dae7\",\"nodeType\":\"StartFlowNode\"},{\"outgoingFields\":[{\"mappingName\":\"dataset_json\",\"fieldName\":\"S_S_CUSTCRTDAT\"}],\"x\":419,\"name\":\"87687\",\"y\":140,\"attributes\":{\"limitNum\":\"5\",\"nodeName\":\"87687\",\"previewTables\":\"\",\"outTableType\":2,\"contextField\":{},\"sqlParams\":[{\"name\":\"S_N_WEEKLYTIME\",\"type\":\"variable\",\"value\":\"10\"},{\"name\":\"b\",\"type\":\"date\",\"value\":\"$[yyyy-MM-dd-1d]\"}],\"sqlContent\":\"select * from test_dy.for_test_2 a inner join test_dy.for_test b where a.date < '${b}' \\nand b.id < ${S_N_WEEKLYTIME}\",\"type\":\"sqlSet\",\"logColumns\":[{\"dataIndex\":\"date\",\"title\":\"date\",\"key\":\"date\"},{\"dataIndex\":\"name\",\"title\":\"name\",\"key\":\"name\"},{\"dataIndex\":\"id\",\"title\":\"id\",\"key\":\"id\"},{\"dataIndex\":\"age\",\"title\":\"age\",\"key\":\"age\"}],\"dataSourceId\":114,\"messages\":[],\"testResult\":true,\"dataSourceType\":\"mysql\",\"resData\":[],\"showLog\":true},\"incomingFields\":[],\"id\":\"afe6112095e311efa7343997b834dae7\",\"nodeType\":\"DataTableServiceNode\"},{\"outgoingFields\":[],\"x\":774,\"name\":\"结束\",\"y\":130,\"attributes\":{},\"incomingFields\":[],\"id\":\"b18e910095e311efa7343997b834dae7\",\"nodeType\":\"EndFlowNode\"},{\"outgoingFields\":[{\"mappingName\":\"age\",\"fieldName\":\"S_N_AGE\",\"displayName\":\"年龄\"},{\"mappingName\":\"code\",\"fieldName\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\"},{\"mappingName\":\"responseCode\",\"fieldName\":\"S_S_THIRDRESCODE\",\"displayName\":\"[系统]三方返回状态码\"}],\"x\":412,\"name\":\"真实接口限流\",\"y\":236,\"attributes\":{\"originInput\":{\"abc\":\"S_S_CUSTCRTDAT\"},\"async\":false,\"exceptionTerminate\":true,\"originOutput\":{\"code\":\"S_S_METRICRESULT\",\"age\":\"S_N_AGE\"},\"featureCodes\":[{\"name\":\"necoRRHReportCustIndex\",\"dName\":\"necoRRHReportCustIndex\"}],\"thirdServiceCode\":\"45678\",\"thirdServiceName\":\"真实接口限流\"},\"incomingFields\":[{\"mappingName\":\"abc\",\"fieldName\":\"S_S_CUSTCRTDAT\",\"displayName\":\"客户开立日期\"}],\"id\":\"9198fe00b2f111efb57295e89acd035a\",\"nodeType\":\"FeatureServiceNode\"}],\"flowLineDefinitions\":[{\"sourceNodeId\":\"aeaa83e095e311efa7343997b834dae7\",\"attributes\":{},\"id\":\"b31b4b80-95e3-11ef-a734-3997b834dae7\",\"targetNodeId\":\"afe6112095e311efa7343997b834dae7\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"afe6112095e311efa7343997b834dae7\",\"attributes\":{},\"id\":\"95c828c0-b2f1-11ef-b572-95e89acd035a\",\"targetNodeId\":\"9198fe00b2f111efb57295e89acd035a\",\"toPoint\":\"0\",\"fromPoint\":2},{\"sourceNodeId\":\"9198fe00b2f111efb57295e89acd035a\",\"attributes\":{},\"id\":\"99567fa0-b2f1-11ef-b572-95e89acd035a\",\"targetNodeId\":\"b18e910095e311efa7343997b834dae7\",\"toPoint\":\"3\",\"fromPoint\":1}],\"name\":\"测试mysql数据表模版\"},\"incomingFields\":[],\"id\":\"cc49ac80b61a11ef824b858fe026bdfb\",\"nodeType\":\"SubDecisionFlowNode\"},{\"outgoingFields\":[],\"x\":661,\"name\":\"数据补充\",\"y\":104,\"attributes\":{\"serviceCode\":\"111\"},\"incomingFields\":[],\"id\":\"cf2b48a0b61a11ef824b858fe026bdfb\",\"nodeType\":\"SuspendFlowNode\"},{\"outgoingFields\":[{\"mappingName\":\"C_S_TEST\",\"fieldName\":\"C_S_TEST\"}],\"x\":659,\"name\":\"[公式]sc_test1203\",\"y\":183,\"attributes\":{\"functionName\":\"[公式]sc_test1203\",\"functionUuid\":\"F7539884230\"},\"incomingFields\":[{\"mappingName\":\"C_F_PFK731\",\"fieldName\":\"C_F_PFK731\"}],\"id\":\"d6854d80b61a11ef824b858fe026bdfb\",\"nodeType\":\"FunctionServiceNode\"},{\"outgoingFields\":[{\"mappingName\":\"dataset_json\",\"fieldName\":\"S_S_METRICRESULT\"}],\"x\":662,\"name\":\"test_dy.for_test\",\"y\":255,\"attributes\":{\"limitNum\":\"3\",\"dataTableType\":\"zhuce\",\"outTableType\":2,\"contextField\":{},\"isBatch\":true,\"type\":\"filterSet\",\"logColumns\":[{\"dataIndex\":\"smallint\",\"title\":\"smallint\",\"key\":\"smallint\"},{\"dataIndex\":\"bool\",\"title\":\"bool\",\"key\":\"bool\"},{\"dataIndex\":\"name\",\"title\":\"name\",\"key\":\"name\"},{\"dataIndex\":\"tinyint\",\"title\":\"tinyint\",\"key\":\"tinyint\"},{\"dataIndex\":\"id\",\"title\":\"id\",\"key\":\"id\"},{\"dataIndex\":\"float\",\"title\":\"float\",\"key\":\"float\"},{\"dataIndex\":\"decimal\",\"title\":\"decimal\",\"key\":\"decimal\"},{\"dataIndex\":\"age\",\"title\":\"age\",\"key\":\"age\"}],\"dataSourceId\":114,\"dataTable\":\"test_dy.for_test\",\"filterCondition\":{\"logic\":\"AND\",\"group\":[{\"logic\":\"AND\",\"group\":[{\"isConstant\":\"true\",\"value\":\"1\",\"key\":\"age\",\"operator\":\">\"}]}]},\"dataTableId\":307,\"messages\":[],\"rowkey\":\"\",\"testResult\":true,\"dataSourceType\":\"mysql\",\"resData\":[],\"showLog\":true},\"incomingFields\":[],\"id\":\"d8e02d70b61a11ef824b858fe026bdfb\",\"nodeType\":\"DataTableServiceNode\"},{\"outgoingFields\":[{\"mappingName\":\"dataset_json\",\"fieldName\":\"C_F_PFK731\"}],\"x\":665,\"name\":\"sql\",\"y\":326,\"attributes\":{\"limitNum\":\"5\",\"nodeName\":\"sql\",\"previewTables\":\"test_dy.for_test_2\",\"outTableType\":2,\"contextField\":{},\"sqlParams\":[{\"name\":\"a\",\"type\":\"constant\",\"value\":\"1\"}],\"sqlContent\":\"select * from test_dy.for_test_2 where id >${a}\",\"type\":\"sqlSet\",\"logColumns\":[{\"dataIndex\":\"date\",\"title\":\"date\",\"key\":\"date\"},{\"dataIndex\":\"name\",\"title\":\"name\",\"key\":\"name\"},{\"dataIndex\":\"id\",\"title\":\"id\",\"key\":\"id\"},{\"dataIndex\":\"age\",\"title\":\"age\",\"key\":\"age\"}],\"dataSourceId\":114,\"messages\":[],\"testResult\":true,\"dataSourceType\":\"mysql\",\"resData\":[],\"showLog\":false},\"incomingFields\":[],\"id\":\"dc0e14d0b61a11ef824b858fe026bdfb\",\"nodeType\":\"DataTableServiceNode\"},{\"outgoingFields\":[],\"x\":885,\"name\":\"结束\",\"y\":90,\"attributes\":{},\"incomingFields\":[],\"id\":\"e82a35a0b61a11ef824b858fe026bdfb\",\"nodeType\":\"EndFlowNode\"},{\"outgoingFields\":[],\"x\":883,\"name\":\"结束\",\"y\":166,\"attributes\":{},\"incomingFields\":[],\"id\":\"e9cb8990b61a11ef824b858fe026bdfb\",\"nodeType\":\"EndFlowNode\"},{\"outgoingFields\":[],\"x\":894,\"name\":\"结束\",\"y\":254,\"attributes\":{},\"incomingFields\":[],\"id\":\"ec085a30b61a11ef824b858fe026bdfb\",\"nodeType\":\"EndFlowNode\"},{\"outgoingFields\":[],\"x\":904,\"name\":\"结束\",\"y\":323,\"attributes\":{},\"incomingFields\":[],\"id\":\"ed920770b61a11ef824b858fe026bdfb\",\"nodeType\":\"EndFlowNode\"}],\"flowLineDefinitions\":[{\"sourceNodeId\":\"bab1bcb0b61a11ef824b858fe026bdfb\",\"attributes\":{},\"id\":\"bef1d760-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"bd86d5b0b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"bd86d5b0b61a11ef824b858fe026bdfb\",\"lineType\":\"ExclusiveConditionLine\",\"name\":\"为空\",\"attributes\":{\"condition\":\"{\\\"logicOperator\\\":\\\"&&\\\",\\\"type\\\":\\\"context\\\",\\\"children\\\":[{\\\"property\\\":\\\"S_S_CUSTCRTDAT\\\",\\\"op\\\":\\\"isnull\\\",\\\"propertyDataType\\\":\\\"STRING\\\",\\\"type\\\":\\\"context\\\",\\\"rightValueType\\\":\\\"context\\\",\\\"priority\\\":1}]}\",\"conditionName\":\"为空\"},\"id\":\"de218b80-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"c203d250b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"bd86d5b0b61a11ef824b858fe026bdfb\",\"lineType\":\"ExclusiveConditionLine\",\"name\":\"默认\",\"attributes\":{\"isDefault\":true,\"conditionName\":\"默认\"},\"id\":\"e0e4a320-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"cc49ac80b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"c203d250b61a11ef824b858fe026bdfb\",\"attributes\":{},\"id\":\"e243e500-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"cf2b48a0b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"c8874d50b61a11ef824b858fe026bdfb\",\"lineType\":\"RouteConditionLine\",\"name\":\"50\",\"attributes\":{\"conditionName\":\"50\",\"ratio\":50},\"id\":\"e36310a0-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"d6854d80b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"cc49ac80b61a11ef824b858fe026bdfb\",\"attributes\":{},\"id\":\"e5fd6a90-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"dc0e14d0b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"cf2b48a0b61a11ef824b858fe026bdfb\",\"attributes\":{},\"id\":\"ef475890-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"e82a35a0b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"c8874d50b61a11ef824b858fe026bdfb\",\"lineType\":\"RouteConditionLine\",\"name\":\"50\",\"attributes\":{\"conditionName\":\"50\",\"ratio\":50},\"id\":\"fabe5fc0-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"d8e02d70b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"d6854d80b61a11ef824b858fe026bdfb\",\"attributes\":{},\"id\":\"fcb3ee30-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"e9cb8990b61a11ef824b858fe026bdfb\",\"toPoint\":\"2\",\"fromPoint\":1},{\"sourceNodeId\":\"d8e02d70b61a11ef824b858fe026bdfb\",\"attributes\":{},\"id\":\"fef50490-b61a-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"ec085a30b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"dc0e14d0b61a11ef824b858fe026bdfb\",\"attributes\":{},\"id\":\"01e39900-b61b-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"ed920770b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1},{\"sourceNodeId\":\"bd86d5b0b61a11ef824b858fe026bdfb\",\"lineType\":\"ExclusiveConditionLine\",\"name\":\"开立日期前缀2024\",\"attributes\":{\"condition\":\"{\\\"logicOperator\\\":\\\"&&\\\",\\\"type\\\":\\\"context\\\",\\\"children\\\":[{\\\"property\\\":\\\"S_S_CUSTCRTDAT\\\",\\\"op\\\":\\\"prefix\\\",\\\"value\\\":\\\"2024\\\",\\\"propertyDataType\\\":\\\"STRING\\\",\\\"type\\\":\\\"context\\\",\\\"rightValueType\\\":\\\"input\\\",\\\"priority\\\":1}]}\",\"conditionName\":\"开立日期前缀2024\"},\"id\":\"079e5050-b61c-11ef-824b-858fe026bdfb\",\"targetNodeId\":\"c8874d50b61a11ef824b858fe026bdfb\",\"toPoint\":\"3\",\"fromPoint\":1}],\"name\":\"测试全组件2222222\",\"id\":\"f87020d4f49c4135a97beb91f7495317\",\"version\":4}"
  }
}