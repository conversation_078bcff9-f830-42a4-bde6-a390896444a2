module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "name": "FIELD_SYSTEM",
      "dName": "系统字段",
      "selectType": "FIELD",
      "prefix": "[\"C_\",\"S_\"]",
      "bizType": "field",
      "data": [
        {
          "name": "S_S_CUSTCRTDAT",
          "dName": "客户开立日期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_NODERESULTLIST",
          "dName": "策略运行节点详情",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MPSUBID",
          "dName": "微信子商户号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACTCNTRCERTNO",
          "dName": "实际控制人证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_METRICRESULT",
          "dName": "指标结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK731",
          "dName": "评分卡输出字段731",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK730",
          "dName": "评分卡输出字段730",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK733",
          "dName": "评分卡输出字段733",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK732",
          "dName": "评分卡输出字段732",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK735",
          "dName": "评分卡输出字段735",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_TEST",
          "dName": "测试新增系统字段",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK734",
          "dName": "评分卡输出字段734",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK737",
          "dName": "评分卡输出字段737",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_WEEKLYTIME",
          "dName": "每周限笔",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK736",
          "dName": "评分卡输出字段736",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK739",
          "dName": "评分卡输出字段739",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK738",
          "dName": "评分卡输出字段738",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERMANUALACCESSLEVEL",
          "dName": "商户准入手动评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CHILDRENPOLICYBREAKUP",
          "dName": "子策略主动终止",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTSTS",
          "dName": "客户有效状态",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_HANGYETOUXIANG",
          "dName": "行业投向",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERREGDATE",
          "dName": "商户入网日期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REGISTERADDRESS",
          "dName": "商户注册地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_AGENTCERTTYPE",
          "dName": "代理人证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LOANCONTRACT",
          "dName": "借款合同",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_EMPLOYMENT",
          "dName": "就业情况",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK720",
          "dName": "评分卡输出字段720",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYEEBANKCODE",
          "dName": "收款方开户行号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK722",
          "dName": "评分卡输出字段722",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK721",
          "dName": "评分卡输出字段721",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK724",
          "dName": "评分卡输出字段724",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CFOIDCODE",
          "dName": "财务负责人证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK723",
          "dName": "评分卡输出字段723",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK726",
          "dName": "评分卡输出字段726",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK725",
          "dName": "评分卡输出字段725",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISIPABROAD",
          "dName": "是否国外IP",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_D_LOANDATE",
          "dName": "放款日期",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK728",
          "dName": "评分卡输出字段728",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK727",
          "dName": "评分卡输出字段727",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK729",
          "dName": "评分卡输出字段729",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTEXPDAT",
          "dName": "证件到期日期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT2MOBI",
          "dName": "第二联系人手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT4NAME",
          "dName": "第四联系人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_APPTYPE",
          "dName": "app类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_ISUPDACCOUNTINFO",
          "dName": "是否更新账户信息",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_BATCHTRANSNO",
          "dName": "批量决策任务编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CHILDRENFLOWDECISIONNAME",
          "dName": "子策略结果名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK751",
          "dName": "评分卡输出字段751",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK750",
          "dName": "评分卡输出字段750",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK511",
          "dName": "评分卡输出字段511",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK753",
          "dName": "评分卡输出字段753",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK510",
          "dName": "评分卡输出字段510",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK752",
          "dName": "评分卡输出字段752",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK513",
          "dName": "评分卡输出字段513",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK755",
          "dName": "评分卡输出字段755",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BUSINESSTERM",
          "dName": "营业期限",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK512",
          "dName": "评分卡输出字段512",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK754",
          "dName": "评分卡输出字段754",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK515",
          "dName": "评分卡输出字段515",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK757",
          "dName": "评分卡输出字段757",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MOBINOISP",
          "dName": "手机号运营商",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK514",
          "dName": "评分卡输出字段514",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK756",
          "dName": "评分卡输出字段756",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_XAFLTCUSTACTNUM",
          "dName": "附属客户账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK517",
          "dName": "评分卡输出字段517",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK759",
          "dName": "评分卡输出字段759",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK516",
          "dName": "评分卡输出字段516",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK758",
          "dName": "评分卡输出字段758",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK519",
          "dName": "评分卡输出字段519",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK518",
          "dName": "评分卡输出字段518",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTIFICATEOFTTHP",
          "dName": "房产证",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_GJJSTATUS",
          "dName": "公积金缴存状态",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_NATIONALITY",
          "dName": "国籍",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_SINGLEMIN",
          "dName": "单笔最低",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK740",
          "dName": "评分卡输出字段740",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK500",
          "dName": "评分卡输出字段500",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK742",
          "dName": "评分卡输出字段742",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_VCHRKIND",
          "dName": "凭证种类",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK741",
          "dName": "评分卡输出字段741",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYMTTYP",
          "dName": "支付方式",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_CUSTGRADE",
          "dName": "客群等级评分",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK502",
          "dName": "评分卡输出字段502",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK744",
          "dName": "评分卡输出字段744",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK501",
          "dName": "评分卡输出字段501",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK743",
          "dName": "评分卡输出字段743",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK504",
          "dName": "评分卡输出字段504",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK746",
          "dName": "评分卡输出字段746",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK503",
          "dName": "评分卡输出字段503",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK745",
          "dName": "评分卡输出字段745",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO4NAM",
          "dName": "受益所有人4名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK506",
          "dName": "评分卡输出字段506",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK748",
          "dName": "评分卡输出字段748",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK505",
          "dName": "评分卡输出字段505",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK747",
          "dName": "评分卡输出字段747",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK508",
          "dName": "评分卡输出字段508",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_BSCORE",
          "dName": "行为评分卡分值",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK507",
          "dName": "评分卡输出字段507",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK749",
          "dName": "评分卡输出字段749",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK509",
          "dName": "评分卡输出字段509",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_ABNORMALTAXACT",
          "dName": "是否纳税非正常户",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_BRCHCLOSETIME",
          "dName": "机构关机时间",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDCITY",
          "dName": "银行卡归属城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK531",
          "dName": "评分卡输出字段531",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK530",
          "dName": "评分卡输出字段530",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK533",
          "dName": "评分卡输出字段533",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK532",
          "dName": "评分卡输出字段532",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BASICACCTBNK",
          "dName": "基本户开户行",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK535",
          "dName": "评分卡输出字段535",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK534",
          "dName": "评分卡输出字段534",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERZFBKHYYRESULT",
          "dName": "商户支付宝开户意愿调用结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DIRECTOR",
          "dName": "董事名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK537",
          "dName": "评分卡输出字段537",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_WORKUNIT",
          "dName": "工作单位名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK536",
          "dName": "评分卡输出字段536",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK539",
          "dName": "评分卡输出字段539",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK538",
          "dName": "评分卡输出字段538",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO4CERTTYP",
          "dName": "受益所有人4证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MOBINOPROV",
          "dName": "手机归属省份",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYERCUSTNO",
          "dName": "付款方客户号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BRATING",
          "dName": "行为评分卡风险等级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CORELATION",
          "dName": "共同借款人社会关系",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK760",
          "dName": "评分卡输出字段760",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK520",
          "dName": "评分卡输出字段520",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK762",
          "dName": "评分卡输出字段762",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK761",
          "dName": "评分卡输出字段761",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_LASTPBOCGUARANTCOMPBAL",
          "dName": "测试问题单956961",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK522",
          "dName": "评分卡输出字段522",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK521",
          "dName": "评分卡输出字段521",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK763",
          "dName": "评分卡输出字段763",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK524",
          "dName": "评分卡输出字段524",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK523",
          "dName": "评分卡输出字段523",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSCITY",
          "dName": "交易地区码市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK526",
          "dName": "评分卡输出字段526",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK525",
          "dName": "评分卡输出字段525",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK528",
          "dName": "评分卡输出字段528",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO2NAM",
          "dName": "受益所有人2名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK527",
          "dName": "评分卡输出字段527",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK529",
          "dName": "评分卡输出字段529",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACTCNTRPHONE",
          "dName": "实际控制人联系电话",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_USERNO",
          "dName": "用户编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO4CERTNO",
          "dName": "受益所有人4证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_INTLACCTNUM",
          "dName": "内部账户账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_FINALDEALTYPE",
          "dName": "处置方式",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TEMPLIMIT",
          "dName": "临时额度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISINNERACCTNO",
          "dName": "收款方是否是本行账号",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "本行"
            },
            {
              "value": "0",
              "description": "非本行"
            }
          ]
        },
        {
          "name": "S_S_IPADDR",
          "dName": "IP地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSBANKNO",
          "dName": "交易银行行号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK551",
          "dName": "评分卡输出字段551",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK550",
          "dName": "评分卡输出字段550",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK311",
          "dName": "评分卡输出字段311",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK553",
          "dName": "评分卡输出字段553",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK310",
          "dName": "评分卡输出字段310",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK552",
          "dName": "评分卡输出字段552",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REALINFOITGRFLAG",
          "dName": "信息完整性标志",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SUREID",
          "dName": "担保人身份证",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK313",
          "dName": "评分卡输出字段313",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK555",
          "dName": "评分卡输出字段555",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK312",
          "dName": "评分卡输出字段312",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK554",
          "dName": "评分卡输出字段554",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK315",
          "dName": "评分卡输出字段315",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK557",
          "dName": "评分卡输出字段557",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK314",
          "dName": "评分卡输出字段314",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK556",
          "dName": "评分卡输出字段556",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK317",
          "dName": "评分卡输出字段317",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK559",
          "dName": "评分卡输出字段559",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK316",
          "dName": "评分卡输出字段316",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK558",
          "dName": "评分卡输出字段558",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK319",
          "dName": "评分卡输出字段319",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK318",
          "dName": "评分卡输出字段318",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MOBILEPHONE",
          "dName": "商户联系人手机号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_PENSIONRECEIVE",
          "dName": "是否领取养老金",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTNO",
          "dName": "客户编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_AUTHPERNAME",
          "dName": "授权经办人名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MACADDR",
          "dName": "MAC地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSCOUNTRY",
          "dName": "交易地区码国家",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERZONECODE",
          "dName": "商户地区代码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK540",
          "dName": "评分卡输出字段540",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_NECOCLIENTNAME",
          "dName": "necoClientName",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK300",
          "dName": "评分卡输出字段300",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK542",
          "dName": "评分卡输出字段542",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DETOOLRESCODE",
          "dName": "决策工具返回状态码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK541",
          "dName": "评分卡输出字段541",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK302",
          "dName": "评分卡输出字段302",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK544",
          "dName": "评分卡输出字段544",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TERMNO",
          "dName": "交易终端编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK301",
          "dName": "评分卡输出字段301",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK543",
          "dName": "评分卡输出字段543",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT3MOBI",
          "dName": "第三联系人手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK304",
          "dName": "评分卡输出字段304",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK546",
          "dName": "评分卡输出字段546",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK303",
          "dName": "评分卡输出字段303",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK545",
          "dName": "评分卡输出字段545",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_ISNEWACCOUNT",
          "dName": "是否新增账户",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK306",
          "dName": "评分卡输出字段306",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK548",
          "dName": "评分卡输出字段548",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MEID",
          "dName": "MEID",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK305",
          "dName": "评分卡输出字段305",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK547",
          "dName": "评分卡输出字段547",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK308",
          "dName": "评分卡输出字段308",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK307",
          "dName": "评分卡输出字段307",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK549",
          "dName": "评分卡输出字段549",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK309",
          "dName": "评分卡输出字段309",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CARDMEDIATYPE",
          "dName": "介质类型",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "IC卡"
            },
            {
              "value": "2",
              "description": "磁条卡"
            },
            {
              "value": "3",
              "description": "IC磁条卡"
            },
            {
              "value": "4",
              "description": "存折"
            }
          ]
        },
        {
          "name": "C_S_ACCTCURRRATING",
          "dName": "账户当前风险评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_EVENTTYPE",
          "dName": "事件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDBIN",
          "dName": "银行卡卡bin",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PERMPAYEFFTDATE",
          "dName": "可支付生效日",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CORPPHONE",
          "dName": "法人联系电话",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK571",
          "dName": "评分卡输出字段571",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK570",
          "dName": "评分卡输出字段570",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BUSDRFTIDN",
          "dName": "票据编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_COOPERATION",
          "dName": "合作方式",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "0",
              "description": "商城待注册"
            },
            {
              "value": "1",
              "description": "直联商户"
            },
            {
              "value": "2",
              "description": "消费商户"
            },
            {
              "value": "3",
              "description": "缴费商户"
            },
            {
              "value": "4",
              "description": "产业商户"
            },
            {
              "value": "5",
              "description": "扶贫商户"
            }
          ]
        },
        {
          "name": "C_F_PFK331",
          "dName": "评分卡输出字段331",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK573",
          "dName": "评分卡输出字段573",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK330",
          "dName": "评分卡输出字段330",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK572",
          "dName": "评分卡输出字段572",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK333",
          "dName": "评分卡输出字段333",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK575",
          "dName": "评分卡输出字段575",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK332",
          "dName": "评分卡输出字段332",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK574",
          "dName": "评分卡输出字段574",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK335",
          "dName": "评分卡输出字段335",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK577",
          "dName": "评分卡输出字段577",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK334",
          "dName": "评分卡输出字段334",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK576",
          "dName": "评分卡输出字段576",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK337",
          "dName": "评分卡输出字段337",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK579",
          "dName": "评分卡输出字段579",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK336",
          "dName": "评分卡输出字段336",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK578",
          "dName": "评分卡输出字段578",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SURECOMPAD",
          "dName": "担保企业经营地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK339",
          "dName": "评分卡输出字段339",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK338",
          "dName": "评分卡输出字段338",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_POLICYCODE",
          "dName": "策略编码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSGPSCITY",
          "dName": "交易GPS归属市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TOKENID",
          "dName": "风控唯一标识",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_MERTYPE",
          "dName": "商户类型",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "企业"
            },
            {
              "value": "2",
              "description": "个体户"
            },
            {
              "value": "3",
              "description": "小微商户"
            },
            {
              "value": "4",
              "description": "事业单位"
            }
          ]
        },
        {
          "name": "S_D_BILLINGDATE",
          "dName": "账单日",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_MONTHLYTIME",
          "dName": "每月限笔",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_MONTHLYAMT",
          "dName": "每月限额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IPPROV",
          "dName": "IP归属省份",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LOGINMOBI",
          "dName": "登录手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK560",
          "dName": "评分卡输出字段560",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_APPCODE",
          "dName": "渠道标识",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK320",
          "dName": "评分卡输出字段320",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK562",
          "dName": "评分卡输出字段562",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK561",
          "dName": "评分卡输出字段561",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK322",
          "dName": "评分卡输出字段322",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK564",
          "dName": "评分卡输出字段564",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK321",
          "dName": "评分卡输出字段321",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK563",
          "dName": "评分卡输出字段563",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK324",
          "dName": "评分卡输出字段324",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK566",
          "dName": "评分卡输出字段566",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_AGENTCERTPHONE",
          "dName": "代理人手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK323",
          "dName": "评分卡输出字段323",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK565",
          "dName": "评分卡输出字段565",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK326",
          "dName": "评分卡输出字段326",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK568",
          "dName": "评分卡输出字段568",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK325",
          "dName": "评分卡输出字段325",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK567",
          "dName": "评分卡输出字段567",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK328",
          "dName": "评分卡输出字段328",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK327",
          "dName": "评分卡输出字段327",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK569",
          "dName": "评分卡输出字段569",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK329",
          "dName": "评分卡输出字段329",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CUSTTYPE",
          "dName": "客户类型",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "个人"
            },
            {
              "value": "2",
              "description": "企业"
            },
            {
              "value": "3",
              "description": "同业"
            }
          ]
        },
        {
          "name": "S_S_REPNTTLRNO",
          "dName": "接替柜员号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK591",
          "dName": "评分卡输出字段591",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PRODUCTNAME",
          "dName": "产品名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK590",
          "dName": "评分卡输出字段590",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK351",
          "dName": "评分卡输出字段351",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK593",
          "dName": "评分卡输出字段593",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK350",
          "dName": "评分卡输出字段350",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK592",
          "dName": "评分卡输出字段592",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK111",
          "dName": "评分卡输出字段111",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK353",
          "dName": "评分卡输出字段353",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK595",
          "dName": "评分卡输出字段595",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK110",
          "dName": "评分卡输出字段110",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK352",
          "dName": "评分卡输出字段352",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK594",
          "dName": "评分卡输出字段594",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK113",
          "dName": "评分卡输出字段113",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK355",
          "dName": "评分卡输出字段355",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK597",
          "dName": "评分卡输出字段597",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK112",
          "dName": "评分卡输出字段112",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK354",
          "dName": "评分卡输出字段354",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK596",
          "dName": "评分卡输出字段596",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK115",
          "dName": "评分卡输出字段115",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK357",
          "dName": "评分卡输出字段357",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK599",
          "dName": "评分卡输出字段599",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK114",
          "dName": "评分卡输出字段114",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK356",
          "dName": "评分卡输出字段356",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK598",
          "dName": "评分卡输出字段598",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK117",
          "dName": "评分卡输出字段117",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK359",
          "dName": "评分卡输出字段359",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK116",
          "dName": "评分卡输出字段116",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK358",
          "dName": "评分卡输出字段358",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_MERCHANTSLOGO",
          "dName": "是否为谨慎发展商户标志",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_F_PFK119",
          "dName": "评分卡输出字段119",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_AMOUNT",
          "dName": "交易金额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK118",
          "dName": "评分卡输出字段118",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT4MOBI",
          "dName": "第四联系人手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_GENERALMANAGER",
          "dName": "总经理名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT4RELATION",
          "dName": "第四联系人关系",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK580",
          "dName": "评分卡输出字段580",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_OCCU",
          "dName": "职业",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK340",
          "dName": "评分卡输出字段340",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK582",
          "dName": "评分卡输出字段582",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK581",
          "dName": "评分卡输出字段581",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK100",
          "dName": "评分卡输出字段100",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK342",
          "dName": "评分卡输出字段342",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK584",
          "dName": "评分卡输出字段584",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK341",
          "dName": "评分卡输出字段341",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK583",
          "dName": "评分卡输出字段583",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK102",
          "dName": "评分卡输出字段102",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK344",
          "dName": "评分卡输出字段344",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK586",
          "dName": "评分卡输出字段586",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK101",
          "dName": "评分卡输出字段101",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK343",
          "dName": "评分卡输出字段343",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK585",
          "dName": "评分卡输出字段585",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK104",
          "dName": "评分卡输出字段104",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK346",
          "dName": "评分卡输出字段346",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK588",
          "dName": "评分卡输出字段588",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_AUTHPERPHONE",
          "dName": "授权经办人联系电话",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK103",
          "dName": "评分卡输出字段103",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK345",
          "dName": "评分卡输出字段345",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK587",
          "dName": "评分卡输出字段587",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_MERACCESSSCORE",
          "dName": "商户准入分",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK106",
          "dName": "评分卡输出字段106",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK348",
          "dName": "评分卡输出字段348",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK105",
          "dName": "评分卡输出字段105",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK347",
          "dName": "评分卡输出字段347",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK589",
          "dName": "评分卡输出字段589",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK108",
          "dName": "评分卡输出字段108",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_PAYROLLACT",
          "dName": "是否代发工资户",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK107",
          "dName": "评分卡输出字段107",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK349",
          "dName": "评分卡输出字段349",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK109",
          "dName": "评分卡输出字段109",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PROVINCE",
          "dName": "申请省份",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TERMLONGITUDE",
          "dName": "终端经度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_GJJPRIVATE",
          "dName": "公积金个人缴存基数",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_XAFLTACCTNTR",
          "dName": "附属账户属性",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LIVINGPHOTO",
          "dName": "活体照片",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK371",
          "dName": "评分卡输出字段371",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK370",
          "dName": "评分卡输出字段370",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK131",
          "dName": "评分卡输出字段131",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK373",
          "dName": "评分卡输出字段373",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SERVICENAME",
          "dName": "服务标识",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_GJJCONTINUITYMONTH",
          "dName": "连续缴存月数_公积金",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK130",
          "dName": "评分卡输出字段130",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK372",
          "dName": "评分卡输出字段372",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK133",
          "dName": "评分卡输出字段133",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK375",
          "dName": "评分卡输出字段375",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK132",
          "dName": "评分卡输出字段132",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK374",
          "dName": "评分卡输出字段374",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_LASTPBOCGUARANTCOM",
          "dName": "测试问题单95696",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK135",
          "dName": "评分卡输出字段135",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK377",
          "dName": "评分卡输出字段377",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK134",
          "dName": "评分卡输出字段134",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK376",
          "dName": "评分卡输出字段376",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SECURITYCODE",
          "dName": "交易认证方式代码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK137",
          "dName": "评分卡输出字段137",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK379",
          "dName": "评分卡输出字段379",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_D_MERUPDATETIME",
          "dName": "商户更新时间",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK136",
          "dName": "评分卡输出字段136",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK378",
          "dName": "评分卡输出字段378",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DEVICETOKENID",
          "dName": "设备TokenId",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK139",
          "dName": "评分卡输出字段139",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK138",
          "dName": "评分卡输出字段138",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSAREA",
          "dName": "交易地区码区",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BILLPROP",
          "dName": "票据属性",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERZFBBBRESULT",
          "dName": "商户支付宝报备调用结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_CSCORE",
          "dName": "催收评分卡分值New",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDNAME",
          "dName": "银行卡名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK360",
          "dName": "评分卡输出字段360",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TOTDUEAMT",
          "dName": "最小还款额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK120",
          "dName": "评分卡输出字段120",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK362",
          "dName": "评分卡输出字段362",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK361",
          "dName": "评分卡输出字段361",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK122",
          "dName": "评分卡输出字段122",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK364",
          "dName": "评分卡输出字段364",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK121",
          "dName": "评分卡输出字段121",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK363",
          "dName": "评分卡输出字段363",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK124",
          "dName": "评分卡输出字段124",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK366",
          "dName": "评分卡输出字段366",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK123",
          "dName": "评分卡输出字段123",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK365",
          "dName": "评分卡输出字段365",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK126",
          "dName": "评分卡输出字段126",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK368",
          "dName": "评分卡输出字段368",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK125",
          "dName": "评分卡输出字段125",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK367",
          "dName": "评分卡输出字段367",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK128",
          "dName": "评分卡输出字段128",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK127",
          "dName": "评分卡输出字段127",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK369",
          "dName": "评分卡输出字段369",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RELIGION",
          "dName": "宗教信仰",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK129",
          "dName": "评分卡输出字段129",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTISUENATYCOD",
          "dName": "发证机关国家",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_BENEFOWNERFLAG",
          "dName": "是否识别受益所有人",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_POLITICALSTATUS",
          "dName": "政治面貌",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_N_TESTDY",
          "dName": "测试dy",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK391",
          "dName": "评分卡输出字段391",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK390",
          "dName": "评分卡输出字段390",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK151",
          "dName": "评分卡输出字段151",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK393",
          "dName": "评分卡输出字段393",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK150",
          "dName": "评分卡输出字段150",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK392",
          "dName": "评分卡输出字段392",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK153",
          "dName": "评分卡输出字段153",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK395",
          "dName": "评分卡输出字段395",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK152",
          "dName": "评分卡输出字段152",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK394",
          "dName": "评分卡输出字段394",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK155",
          "dName": "评分卡输出字段155",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK397",
          "dName": "评分卡输出字段397",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK154",
          "dName": "评分卡输出字段154",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK396",
          "dName": "评分卡输出字段396",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK157",
          "dName": "评分卡输出字段157",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK399",
          "dName": "评分卡输出字段399",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_PMTDUEDATE",
          "dName": "到期还款日期",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK156",
          "dName": "评分卡输出字段156",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK398",
          "dName": "评分卡输出字段398",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK159",
          "dName": "评分卡输出字段159",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK158",
          "dName": "评分卡输出字段158",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SGNTYP",
          "dName": "签约类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK380",
          "dName": "评分卡输出字段380",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK140",
          "dName": "评分卡输出字段140",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK382",
          "dName": "评分卡输出字段382",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK381",
          "dName": "评分卡输出字段381",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK142",
          "dName": "评分卡输出字段142",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK384",
          "dName": "评分卡输出字段384",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_VALDATE",
          "dName": "起息日期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK141",
          "dName": "评分卡输出字段141",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK383",
          "dName": "评分卡输出字段383",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK144",
          "dName": "评分卡输出字段144",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK386",
          "dName": "评分卡输出字段386",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK143",
          "dName": "评分卡输出字段143",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK385",
          "dName": "评分卡输出字段385",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK146",
          "dName": "评分卡输出字段146",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK388",
          "dName": "评分卡输出字段388",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK145",
          "dName": "评分卡输出字段145",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK387",
          "dName": "评分卡输出字段387",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK148",
          "dName": "评分卡输出字段148",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK147",
          "dName": "评分卡输出字段147",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK389",
          "dName": "评分卡输出字段389",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK149",
          "dName": "评分卡输出字段149",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERNO",
          "dName": "商户编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_ALARMLEVEL",
          "dName": "预警等级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LGLREPTNAM",
          "dName": "法定代表人名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RECEADDR",
          "dName": "收货地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK290",
          "dName": "评分卡输出字段290",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYERCUSTNAME",
          "dName": "付款方名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK292",
          "dName": "评分卡输出字段292",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK291",
          "dName": "评分卡输出字段291",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK294",
          "dName": "评分卡输出字段294",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK293",
          "dName": "评分卡输出字段293",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK296",
          "dName": "评分卡输出字段296",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK295",
          "dName": "评分卡输出字段295",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK298",
          "dName": "评分卡输出字段298",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK297",
          "dName": "评分卡输出字段297",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_BANKSTAFF",
          "dName": "是否本行员工",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK299",
          "dName": "评分卡输出字段299",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TASKACCT",
          "dName": "任务批次",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IDCARDFRONT",
          "dName": "身份证正面",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_HOMEINCOMEPERM",
          "dName": "家庭月收入",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_GRACEDAYSFULLIND",
          "dName": "是否已全额还款",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SUBJECTID",
          "dName": "多主体进件ID",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_IMPTEST",
          "dName": "IMPCYY_5L_SType_S1096_Test_aaaaaa",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REGPLCECTRYCODE",
          "dName": "注册地国别",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SUREMOBI",
          "dName": "担保人手机",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK281",
          "dName": "评分卡输出字段281",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK280",
          "dName": "评分卡输出字段280",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_BRIBE",
          "dName": "是否行贿违法",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK283",
          "dName": "评分卡输出字段283",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK282",
          "dName": "评分卡输出字段282",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK285",
          "dName": "评分卡输出字段285",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK284",
          "dName": "评分卡输出字段284",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK287",
          "dName": "评分卡输出字段287",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK286",
          "dName": "评分卡输出字段286",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK289",
          "dName": "评分卡输出字段289",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYEEBANKADDR",
          "dName": "收款方开户行归属地",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DRFTHLDRNM",
          "dName": "持票人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK288",
          "dName": "评分卡输出字段288",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_ZHIMASCORE",
          "dName": "芝麻信用分",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DEGREE",
          "dName": "学位",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERREGCOUNTY",
          "dName": "商户经营所在区县",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CHAIRMAN",
          "dName": "董事长名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_SBLATESTDEOPOSITDATE",
          "dName": "最近缴存年月_社保",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTNAME",
          "dName": "客户名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SMARTID",
          "dName": "智能ID",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_PAYMENTCODE",
          "dName": "收款码编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_EQUITYFREEZE",
          "dName": "是否股权冻结",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RISKLABEL",
          "dName": "风险标签",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COMPWEBADDR",
          "dName": "公司网址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MOBINOCITY",
          "dName": "手机归属城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_ACCTBAL",
          "dName": "账户余额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REGCCY",
          "dName": "注册资本币种",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_TRANSSTAT",
          "dName": "交易状态",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "成功"
            },
            {
              "value": "0",
              "description": "失败"
            }
          ]
        },
        {
          "name": "S_F_APPLYLOANRATE",
          "dName": "授信利率",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TOTALCARVALUE",
          "dName": "车辆总价值",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_NETACCTNO",
          "dName": "互联网账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CTFLAG",
          "dName": "现金/转账标志",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "现金"
            },
            {
              "value": "2",
              "description": "转账"
            }
          ]
        },
        {
          "name": "S_S_CHILDRENPOLICYRESCODE",
          "dName": "子策略执行状态码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_QUALGRACEBAL",
          "dName": "全部应还款额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MAILADDRESS",
          "dName": "通讯地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COMPANYADDRESSPROVINCE",
          "dName": "单位地址省",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISAGENT",
          "dName": "是否代理人办理",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_DEPSCETFTYP",
          "dName": "存款证明类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PREVIOUSSCORE",
          "dName": "上期系统评级分数",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BASICACCTNUM",
          "dName": "基本户账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_HOLDNGTYP",
          "dName": "控股类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_ASCORE",
          "dName": "申请评分卡分值",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTCHAN",
          "dName": "获客渠道",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_INDUSTRIALINJURY",
          "dName": "是否参加工伤保险",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_PAYEE2BFLAG",
          "dName": "收款客户对公标识",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "企业"
            },
            {
              "value": "0",
              "description": "个人"
            }
          ]
        },
        {
          "name": "S_S_BATCHDISPATCHNO",
          "dName": "批量决策任务调度编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BOXNO",
          "dName": "尾箱号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SBPAYER",
          "dName": "社保缴纳单位",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSCHAN",
          "dName": "交易渠道",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO2CERTTYP",
          "dName": "受益所有人2证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO3CERTNO",
          "dName": "受益所有人3证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_PVTEFLAG",
          "dName": "民营标志",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_S_MERWXKHYYRESULT",
          "dName": "商户微信开户意愿调用结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SERIALNUM",
          "dName": "业务方流水号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_UNEMPLOYMENT",
          "dName": "是否参加失业保险",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSPROV",
          "dName": "交易地区码省",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CHILDRENFLOWDECISION",
          "dName": "子策略结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERMCC",
          "dName": "商户MCC码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DRIVERLICENCE",
          "dName": "驾照",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CREDITLEVEL",
          "dName": "征信等级",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "A",
              "description": "A级"
            },
            {
              "value": "B",
              "description": "B级"
            },
            {
              "value": "C",
              "description": "C级"
            },
            {
              "value": "D",
              "description": "D级"
            },
            {
              "value": "E",
              "description": "E级"
            },
            {
              "value": "-999",
              "description": "未知"
            }
          ]
        },
        {
          "name": "S_S_EBLIC",
          "dName": "企业营业执照",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTMANAGER",
          "dName": "客户经理",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDTYPE",
          "dName": "银行卡类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_LOANAMOUNT",
          "dName": "放款金额_元",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_LISTDCOMPIND",
          "dName": "上市公司标志",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_E_STAFIND",
          "dName": "本行员工标识",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_SUBMERTYP",
          "dName": "二级商户类别",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_ACCTCURRPERIODTIME",
          "dName": "账户本期评级时间",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RECOMOBILE",
          "dName": "推荐手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERACCURATEADDR",
          "dName": "商户详细地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BANKORGPROV",
          "dName": "银行机构归属省份",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_SEX",
          "dName": "性别",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "男"
            },
            {
              "value": "2",
              "description": "女"
            }
          ]
        },
        {
          "name": "S_D_EVENTOCCURTIME",
          "dName": "事件发生时间",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_CUSTFIRSTNAME",
          "dName": "客户姓氏",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT2RELATION",
          "dName": "第二联系人关系",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ETRPSIZE",
          "dName": "企业规模",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REGDATE",
          "dName": "注册日期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_CURRENTRATINGRESULT",
          "dName": "本期系统评级结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_PAYERACCTTYPE",
          "dName": "付款方账户类型",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "Ⅰ类账户"
            },
            {
              "value": "2",
              "description": "Ⅱ类账户"
            },
            {
              "value": "3",
              "description": "Ⅲ类账户"
            },
            {
              "value": "4",
              "description": "基本账户"
            },
            {
              "value": "5",
              "description": "一般账户"
            },
            {
              "value": "6",
              "description": "临时账户"
            }
          ]
        },
        {
          "name": "S_N_SBCONTINUITYMONTH",
          "dName": "连续缴存月数_社保",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SCRTYOPENBANK",
          "dName": "证券账户开户行名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACCTSTATUS",
          "dName": "账户状态",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_PREVIOUSRESULT",
          "dName": "上期系统评级结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT1NAME",
          "dName": "第一联系人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_FUDACCTNO",
          "dName": "基金账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CFOIDTYP",
          "dName": "财务负责人证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SURENAME",
          "dName": "担保人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REGPLCEPROVCODE",
          "dName": "注册地省别",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_RELPARTYCUSTIND",
          "dName": "是否关联方",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_ENDGPS",
          "dName": "终点GPS",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_HOMEADDRESSPCITY",
          "dName": "居住地址市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO2CERTNO",
          "dName": "受益所有人2证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BILLAMT",
          "dName": "票面金额",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_SBTOTALMONTH",
          "dName": "累计缴存月数_社保",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACCOUNTLOGIN",
          "dName": "登录账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TITLE",
          "dName": "职务",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_OPERTYP",
          "dName": "操作类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_DEBUGMODEL",
          "dName": "是否debug模式",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TDZHICHAFENG",
          "dName": "同盾智察分",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_REGCAPAMT",
          "dName": "注册资本",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DDIND",
          "dName": "约定还款类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COCOMPANY",
          "dName": "共同借款人工作单位",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_CURRENTRESULT",
          "dName": "账户当前评级结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_IFINSTORE",
          "dName": "是否归档",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_ETRPHOLDNGTYP",
          "dName": "企业控股类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACCTSQNUM",
          "dName": "账号序号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO1CERTNO",
          "dName": "受益所有人1证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_HANDUSERNM",
          "dName": "经办人名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTCRTOG",
          "dName": "客户开立机构",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_FUDCOD",
          "dName": "基金代码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UNIFSOCLCRCOD",
          "dName": "统一社会信用代码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_STAFNUM",
          "dName": "员工总数",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT2NAME",
          "dName": "第二联系人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SPOUSEIDCARD",
          "dName": "配偶身份证",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_VCHRNO",
          "dName": "凭证编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RISKSCORE",
          "dName": "风险分数",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_FARMRIND",
          "dName": "是否农户",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_F_CASHBAL",
          "dName": "取现余额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISWORKDAY",
          "dName": "是否工作日",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_E_CURCODE",
          "dName": "币种",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "人民币"
            },
            {
              "value": "2",
              "description": "美元"
            }
          ]
        },
        {
          "name": "S_S_VEHICLELICENCE",
          "dName": "行驶证",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERBEHAVIORLEVEL",
          "dName": "商户行为自动评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_APPLYLOANAMOUNT",
          "dName": "借款申请金额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_NEWCARDNUM",
          "dName": "新卡卡号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PADINCAPCCY",
          "dName": "实收资本币种",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_RISKLIBRARYMARK",
          "dName": "是否加入风险黑名单",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_ACCTNO",
          "dName": "交易账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_GOVFNCPLTFCUSTFLAG",
          "dName": "是否政府融资平台客户",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_TRANSNAME",
          "dName": "交易名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BUSINESSSCOPE",
          "dName": "经营范围",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CELLLOCATION",
          "dName": "基站信息",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RETMSG",
          "dName": "交易返回信息",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK2",
          "dName": "评分卡输出字段2",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK3",
          "dName": "评分卡输出字段3",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK1",
          "dName": "评分卡输出字段1",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK6",
          "dName": "评分卡输出字段6",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_GUARDNIDNO",
          "dName": "监护人证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_ACCTLATESTRATING",
          "dName": "账户开户最近一次尽调结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK7",
          "dName": "评分卡输出字段7",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK4",
          "dName": "评分卡输出字段4",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK5",
          "dName": "评分卡输出字段5",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COREID",
          "dName": "核心流水号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK8",
          "dName": "评分卡输出字段8",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK9",
          "dName": "评分卡输出字段9",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SMSMOBI",
          "dName": "签约短信手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_VCHRBATNO",
          "dName": "凭证批次",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDAREA",
          "dName": "银行卡归属地区编码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_SBPAYMENTBASE",
          "dName": "社保缴纳基数",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_VICEGENERALMANAGER",
          "dName": "副总经理名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONAME",
          "dName": "共同借款人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_AGE",
          "dName": "年龄",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK709",
          "dName": "评分卡输出字段709",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ACCTTYPE",
          "dName": "账户类型",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "Ⅰ类账户"
            },
            {
              "value": "2",
              "description": "Ⅱ类账户"
            },
            {
              "value": "3",
              "description": "Ⅲ类账户"
            },
            {
              "value": "4",
              "description": "基本账户"
            },
            {
              "value": "5",
              "description": "一般账户"
            },
            {
              "value": "6",
              "description": "临时账户"
            }
          ]
        },
        {
          "name": "S_S_SUBMERNAME",
          "dName": "二级商户名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BRAND",
          "dName": "贷借卡卡品牌",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SGNTYPNUM",
          "dName": "签约类型编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACCTBANKNO",
          "dName": "账户开户行号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACTCNTRCERTTYP",
          "dName": "实际控制人证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_N_CS",
          "dName": "测试导入字段",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT3NAME",
          "dName": "第三联系人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_EMAIL",
          "dName": "邮箱信息",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK711",
          "dName": "评分卡输出字段711",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK710",
          "dName": "评分卡输出字段710",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT1MOBI",
          "dName": "第一联系人手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK713",
          "dName": "评分卡输出字段713",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK712",
          "dName": "评分卡输出字段712",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK715",
          "dName": "评分卡输出字段715",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LOANINSTITUTION",
          "dName": "贷款机构",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK714",
          "dName": "评分卡输出字段714",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK717",
          "dName": "评分卡输出字段717",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_LASTYEARTURNOVER",
          "dName": "企业上年营业额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK716",
          "dName": "评分卡输出字段716",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK719",
          "dName": "评分卡输出字段719",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_GUARDNNM",
          "dName": "监护人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK718",
          "dName": "评分卡输出字段718",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERACCESSAUDITAPPLYNO",
          "dName": "商户准入审核申请单号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACCTNAM",
          "dName": "账户名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARTYPE",
          "dName": "私家车型号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYERBANKNAME",
          "dName": "付款方开户行名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SUBTRANSTYPE",
          "dName": "子交易码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_FOUNDDATE",
          "dName": "成立日期",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_EMPLOYEE",
          "dName": "员工人数",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REGGPS",
          "dName": "商户注册GPS",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK700",
          "dName": "评分卡输出字段700",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK702",
          "dName": "评分卡输出字段702",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_INTEREST",
          "dName": "授信额度利息",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK701",
          "dName": "评分卡输出字段701",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_APPLYLOANTERM",
          "dName": "借款期限",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK704",
          "dName": "评分卡输出字段704",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_SHRHLDERIND",
          "dName": "是否本行股东",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_CARDLEN",
          "dName": "银行卡号长度",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK703",
          "dName": "评分卡输出字段703",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CFONAM",
          "dName": "财务负责人名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK706",
          "dName": "评分卡输出字段706",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK705",
          "dName": "评分卡输出字段705",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK708",
          "dName": "评分卡输出字段708",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK707",
          "dName": "评分卡输出字段707",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_FRZMATUREDATE",
          "dName": "冻结到期日期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PADINCAP",
          "dName": "实收资本",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CREDITRESULT",
          "dName": "征信结果",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "结果1"
            },
            {
              "value": "2",
              "description": "结果2"
            }
          ]
        },
        {
          "name": "S_S_REMARKS",
          "dName": "交易摘要",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CREDITVALIDITY",
          "dName": "授信有效期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_USEROPERTM",
          "dName": "用户操作时间",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BASICACCTNAM",
          "dName": "基本账户户名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK610",
          "dName": "评分卡输出字段610",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERADDR",
          "dName": "商户通讯地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK612",
          "dName": "评分卡输出字段612",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK611",
          "dName": "评分卡输出字段611",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_RULESETSCORE",
          "dName": "规则集分数",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK614",
          "dName": "评分卡输出字段614",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK613",
          "dName": "评分卡输出字段613",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK616",
          "dName": "评分卡输出字段616",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK615",
          "dName": "评分卡输出字段615",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COMPANY",
          "dName": "单位名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK618",
          "dName": "评分卡输出字段618",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK617",
          "dName": "评分卡输出字段617",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK619",
          "dName": "评分卡输出字段619",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RISKRATING",
          "dName": "风险评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSAREACODE",
          "dName": "交易地区码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REPLDTLRNO",
          "dName": "被接替柜员号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTISSUEAUTH",
          "dName": "发证机关名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_ORIGINALAMOUNT",
          "dName": "交易金额（原币种）",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TAXRATING",
          "dName": "税务评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PRODUCTCODE",
          "dName": "产品编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_BATCHDISPATCHNO",
          "dName": "批量决策任务调度编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISNOCARD",
          "dName": "是否无卡无折交易",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_F_PFK601",
          "dName": "评分卡输出字段601",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CALENDARTYPE",
          "dName": "节假日日历类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK600",
          "dName": "评分卡输出字段600",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK603",
          "dName": "评分卡输出字段603",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK602",
          "dName": "评分卡输出字段602",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK605",
          "dName": "评分卡输出字段605",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK604",
          "dName": "评分卡输出字段604",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK607",
          "dName": "评分卡输出字段607",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK606",
          "dName": "评分卡输出字段606",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_GUARANTEELETTER",
          "dName": "担保函",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK609",
          "dName": "评分卡输出字段609",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK608",
          "dName": "评分卡输出字段608",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MOBINO",
          "dName": "手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BILLTYP",
          "dName": "票据种类",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COPHONE",
          "dName": "共同借款人座机",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYERBANKCODE",
          "dName": "付款方开户行号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK630",
          "dName": "评分卡输出字段630",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_JIANYIZHUXIAO",
          "dName": "是否企业简易注销",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK632",
          "dName": "评分卡输出字段632",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK631",
          "dName": "评分卡输出字段631",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK634",
          "dName": "评分卡输出字段634",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK633",
          "dName": "评分卡输出字段633",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CNPYBANKNAME",
          "dName": "对方行名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK636",
          "dName": "评分卡输出字段636",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK635",
          "dName": "评分卡输出字段635",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYNOPROV",
          "dName": "缴费号码归属省份",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_AVLBAL",
          "dName": "可用余额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK638",
          "dName": "评分卡输出字段638",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK637",
          "dName": "评分卡输出字段637",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK639",
          "dName": "评分卡输出字段639",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYEEACCTNO",
          "dName": "收款方账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_WEEKLYAMT",
          "dName": "每周限额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BRC",
          "dName": "网点机构号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_NATLOGCODNOC",
          "dName": "组织机构代码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COMPANYPHONE",
          "dName": "单位电话",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTCRTTELR",
          "dName": "客户开立柜员",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTIFICATEOFEMPLY",
          "dName": "工作证明",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACCTBANKNAME",
          "dName": "账户开户行名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_DAILYAMT",
          "dName": "单日限额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK621",
          "dName": "评分卡输出字段621",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK620",
          "dName": "评分卡输出字段620",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK623",
          "dName": "评分卡输出字段623",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK622",
          "dName": "评分卡输出字段622",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK625",
          "dName": "评分卡输出字段625",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK624",
          "dName": "评分卡输出字段624",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK627",
          "dName": "评分卡输出字段627",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK626",
          "dName": "评分卡输出字段626",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK629",
          "dName": "评分卡输出字段629",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK628",
          "dName": "评分卡输出字段628",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SURECOMP",
          "dName": "担保人工作单位",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COMPANYADDRESSCITY",
          "dName": "单位地址市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK650",
          "dName": "评分卡输出字段650",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BIZTP",
          "dName": "业务种类",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK410",
          "dName": "评分卡输出字段410",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK652",
          "dName": "评分卡输出字段652",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK651",
          "dName": "评分卡输出字段651",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK412",
          "dName": "评分卡输出字段412",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK654",
          "dName": "评分卡输出字段654",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BLACKBOX",
          "dName": "设备黑盒",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MNC",
          "dName": "移动网络号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK411",
          "dName": "评分卡输出字段411",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK653",
          "dName": "评分卡输出字段653",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYEEACCTNAME",
          "dName": "收款方名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK414",
          "dName": "评分卡输出字段414",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK656",
          "dName": "评分卡输出字段656",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_EMPEID",
          "dName": "员工编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK413",
          "dName": "评分卡输出字段413",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK655",
          "dName": "评分卡输出字段655",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK416",
          "dName": "评分卡输出字段416",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK658",
          "dName": "评分卡输出字段658",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TELEPHONE",
          "dName": "商户联系人座机号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK415",
          "dName": "评分卡输出字段415",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK657",
          "dName": "评分卡输出字段657",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK418",
          "dName": "评分卡输出字段418",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK417",
          "dName": "评分卡输出字段417",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK659",
          "dName": "评分卡输出字段659",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK419",
          "dName": "评分卡输出字段419",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_AGENTCERTID",
          "dName": "代理人证件号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_THIRDRESCODE",
          "dName": "三方返回状态码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COCOMADDR",
          "dName": "共同借款人公司地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK10",
          "dName": "评分卡输出字段10",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SUBMERNO",
          "dName": "二级商户编码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DYNAMICTABLENAME",
          "dName": "动态表名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_ENVIROMENTPENALTY",
          "dName": "是否环保处罚",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK641",
          "dName": "评分卡输出字段641",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK640",
          "dName": "评分卡输出字段640",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK401",
          "dName": "评分卡输出字段401",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK643",
          "dName": "评分卡输出字段643",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK400",
          "dName": "评分卡输出字段400",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK642",
          "dName": "评分卡输出字段642",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK403",
          "dName": "评分卡输出字段403",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK645",
          "dName": "评分卡输出字段645",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_ISNEWMERCHANT",
          "dName": "是否新增商户",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK402",
          "dName": "评分卡输出字段402",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK644",
          "dName": "评分卡输出字段644",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK405",
          "dName": "评分卡输出字段405",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK647",
          "dName": "评分卡输出字段647",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_CASHLIMIT",
          "dName": "取现额度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK404",
          "dName": "评分卡输出字段404",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK646",
          "dName": "评分卡输出字段646",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK407",
          "dName": "评分卡输出字段407",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK649",
          "dName": "评分卡输出字段649",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK406",
          "dName": "评分卡输出字段406",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK648",
          "dName": "评分卡输出字段648",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_DAILYTIME",
          "dName": "单日限笔",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK409",
          "dName": "评分卡输出字段409",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK408",
          "dName": "评分卡输出字段408",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_GJJAMOUNTPERSON",
          "dName": "公积金个人每月缴存金额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SPOUSEPHONE",
          "dName": "配偶联系电话",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TDZHIXINFENG",
          "dName": "同盾智信分",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_TAXLOGOFF",
          "dName": "是否企业税务注销",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SECSFUDACCTNO",
          "dName": "资金台账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DRFTHLDRACCT",
          "dName": "持票人账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SPOUSENAME",
          "dName": "配偶姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MARRIAGE",
          "dName": "婚姻状况",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_CURRPERIODRATING",
          "dName": "账户本期风险评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARBRAND",
          "dName": "私家车品牌",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK21",
          "dName": "评分卡输出字段21",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK670",
          "dName": "评分卡输出字段670",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK20",
          "dName": "评分卡输出字段20",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IMEI",
          "dName": "IMEI",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TERMADDR",
          "dName": "终端地理位置中文地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK12",
          "dName": "评分卡输出字段12",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK430",
          "dName": "评分卡输出字段430",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK672",
          "dName": "评分卡输出字段672",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK11",
          "dName": "评分卡输出字段11",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK671",
          "dName": "评分卡输出字段671",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK14",
          "dName": "评分卡输出字段14",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK432",
          "dName": "评分卡输出字段432",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK674",
          "dName": "评分卡输出字段674",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BANKORGCITY",
          "dName": "银行机构归属城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK13",
          "dName": "评分卡输出字段13",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK431",
          "dName": "评分卡输出字段431",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK673",
          "dName": "评分卡输出字段673",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK16",
          "dName": "评分卡输出字段16",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK434",
          "dName": "评分卡输出字段434",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK676",
          "dName": "评分卡输出字段676",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK15",
          "dName": "评分卡输出字段15",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK433",
          "dName": "评分卡输出字段433",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK675",
          "dName": "评分卡输出字段675",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK18",
          "dName": "评分卡输出字段18",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK436",
          "dName": "评分卡输出字段436",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK678",
          "dName": "评分卡输出字段678",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SGNNO",
          "dName": "签约协议号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK17",
          "dName": "评分卡输出字段17",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK435",
          "dName": "评分卡输出字段435",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK677",
          "dName": "评分卡输出字段677",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO3NAM",
          "dName": "受益所有人3名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK438",
          "dName": "评分卡输出字段438",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK19",
          "dName": "评分卡输出字段19",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK437",
          "dName": "评分卡输出字段437",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK679",
          "dName": "评分卡输出字段679",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_EVENTID",
          "dName": "事件标识",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK439",
          "dName": "评分卡输出字段439",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_LGLENTYFLAG",
          "dName": "是否法人单位",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_F_PFK30",
          "dName": "评分卡输出字段30",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_OGCUSTTYP",
          "dName": "对公客户类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK32",
          "dName": "评分卡输出字段32",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK31",
          "dName": "评分卡输出字段31",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK23",
          "dName": "评分卡输出字段23",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK661",
          "dName": "评分卡输出字段661",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PARTNERCODE",
          "dName": "合作方",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_MEDICAL",
          "dName": "是否参加医疗保险",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK22",
          "dName": "评分卡输出字段22",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK660",
          "dName": "评分卡输出字段660",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK25",
          "dName": "评分卡输出字段25",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK421",
          "dName": "评分卡输出字段421",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK663",
          "dName": "评分卡输出字段663",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SBACTNO",
          "dName": "社保账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK24",
          "dName": "评分卡输出字段24",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK420",
          "dName": "评分卡输出字段420",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK662",
          "dName": "评分卡输出字段662",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK27",
          "dName": "评分卡输出字段27",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK423",
          "dName": "评分卡输出字段423",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK665",
          "dName": "评分卡输出字段665",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_SMLLOANCOMPFLAG",
          "dName": "是否小额贷款公司",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_F_PFK26",
          "dName": "评分卡输出字段26",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK422",
          "dName": "评分卡输出字段422",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK664",
          "dName": "评分卡输出字段664",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK29",
          "dName": "评分卡输出字段29",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK425",
          "dName": "评分卡输出字段425",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK667",
          "dName": "评分卡输出字段667",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK28",
          "dName": "评分卡输出字段28",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK424",
          "dName": "评分卡输出字段424",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK666",
          "dName": "评分卡输出字段666",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK427",
          "dName": "评分卡输出字段427",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK669",
          "dName": "评分卡输出字段669",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK426",
          "dName": "评分卡输出字段426",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK668",
          "dName": "评分卡输出字段668",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTID",
          "dName": "身份证号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK429",
          "dName": "评分卡输出字段429",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_APPLYLIMIT",
          "dName": "授信额度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK428",
          "dName": "评分卡输出字段428",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PLARFORMSVNO",
          "dName": "印鉴编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CUSTNATR",
          "dName": "客户性质",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_E_ISDLAY",
          "dName": "是否延时",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_F_PFK41",
          "dName": "评分卡输出字段41",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK690",
          "dName": "评分卡输出字段690",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK40",
          "dName": "评分卡输出字段40",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK43",
          "dName": "评分卡输出字段43",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK450",
          "dName": "评分卡输出字段450",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK692",
          "dName": "评分卡输出字段692",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK42",
          "dName": "评分卡输出字段42",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK691",
          "dName": "评分卡输出字段691",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK34",
          "dName": "评分卡输出字段34",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK210",
          "dName": "评分卡输出字段210",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK452",
          "dName": "评分卡输出字段452",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK694",
          "dName": "评分卡输出字段694",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_SINGLELIMIT",
          "dName": "单笔限额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK33",
          "dName": "评分卡输出字段33",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK451",
          "dName": "评分卡输出字段451",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK693",
          "dName": "评分卡输出字段693",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK36",
          "dName": "评分卡输出字段36",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK212",
          "dName": "评分卡输出字段212",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK454",
          "dName": "评分卡输出字段454",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK696",
          "dName": "评分卡输出字段696",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK35",
          "dName": "评分卡输出字段35",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK211",
          "dName": "评分卡输出字段211",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK453",
          "dName": "评分卡输出字段453",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK695",
          "dName": "评分卡输出字段695",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK38",
          "dName": "评分卡输出字段38",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK214",
          "dName": "评分卡输出字段214",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK456",
          "dName": "评分卡输出字段456",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK698",
          "dName": "评分卡输出字段698",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IDAREA",
          "dName": "身份证归属地地区县级市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK37",
          "dName": "评分卡输出字段37",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK213",
          "dName": "评分卡输出字段213",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK455",
          "dName": "评分卡输出字段455",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK697",
          "dName": "评分卡输出字段697",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK216",
          "dName": "评分卡输出字段216",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK458",
          "dName": "评分卡输出字段458",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK39",
          "dName": "评分卡输出字段39",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK215",
          "dName": "评分卡输出字段215",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK457",
          "dName": "评分卡输出字段457",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK699",
          "dName": "评分卡输出字段699",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RGTYP",
          "dName": "组织机构类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDORGABBR",
          "dName": "银行卡发卡机构简称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK218",
          "dName": "评分卡输出字段218",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK217",
          "dName": "评分卡输出字段217",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK459",
          "dName": "评分卡输出字段459",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO1NAM",
          "dName": "受益所有人1名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK219",
          "dName": "评分卡输出字段219",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYERSTAFIND",
          "dName": "付款方是否本行员工",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CARDCLASS",
          "dName": "贷借卡卡等级",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BKRGCODE",
          "dName": "券商代码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CURCODE",
          "dName": "币种",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_PROVINCE",
          "dName": "客户申请省份",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "西藏"
            },
            {
              "value": "2",
              "description": "青海"
            },
            {
              "value": "3",
              "description": "宁夏"
            },
            {
              "value": "4",
              "description": "海南"
            },
            {
              "value": "5",
              "description": "甘肃"
            },
            {
              "value": "6",
              "description": "贵州"
            },
            {
              "value": "7",
              "description": "新疆"
            },
            {
              "value": "8",
              "description": "云南"
            },
            {
              "value": "9",
              "description": "重庆"
            },
            {
              "value": "10",
              "description": "吉林"
            },
            {
              "value": "11",
              "description": "山西"
            },
            {
              "value": "12",
              "description": "天津"
            },
            {
              "value": "13",
              "description": "江西"
            },
            {
              "value": "14",
              "description": "广西"
            },
            {
              "value": "15",
              "description": "陕西"
            },
            {
              "value": "16",
              "description": "黑龙江"
            },
            {
              "value": "17",
              "description": "内蒙古"
            },
            {
              "value": "18",
              "description": "安徽"
            },
            {
              "value": "19",
              "description": "北京"
            },
            {
              "value": "20",
              "description": "福建"
            },
            {
              "value": "21",
              "description": "上海"
            },
            {
              "value": "22",
              "description": "湖北"
            },
            {
              "value": "23",
              "description": "湖南"
            },
            {
              "value": "24",
              "description": "四川"
            },
            {
              "value": "25",
              "description": "辽宁"
            },
            {
              "value": "26",
              "description": "河北"
            },
            {
              "value": "27",
              "description": "河南"
            },
            {
              "value": "28",
              "description": "浙江"
            },
            {
              "value": "29",
              "description": "山东"
            },
            {
              "value": "30",
              "description": "江苏"
            },
            {
              "value": "31",
              "description": "广东"
            },
            {
              "value": "32",
              "description": "台湾"
            },
            {
              "value": "33",
              "description": "澳门"
            },
            {
              "value": "34",
              "description": "香港"
            }
          ]
        },
        {
          "name": "C_F_PFK50",
          "dName": "评分卡输出字段50",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_AVAILABLEOTB",
          "dName": "综合可用额度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK52",
          "dName": "评分卡输出字段52",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK51",
          "dName": "评分卡输出字段51",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK54",
          "dName": "评分卡输出字段54",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK681",
          "dName": "评分卡输出字段681",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK53",
          "dName": "评分卡输出字段53",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK680",
          "dName": "评分卡输出字段680",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SCRTYCMYADDR",
          "dName": "证券基金开户地",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK45",
          "dName": "评分卡输出字段45",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK441",
          "dName": "评分卡输出字段441",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK683",
          "dName": "评分卡输出字段683",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK44",
          "dName": "评分卡输出字段44",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK440",
          "dName": "评分卡输出字段440",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK682",
          "dName": "评分卡输出字段682",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK47",
          "dName": "评分卡输出字段47",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK201",
          "dName": "评分卡输出字段201",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK443",
          "dName": "评分卡输出字段443",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK685",
          "dName": "评分卡输出字段685",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BIZID",
          "dName": "业务流水号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK46",
          "dName": "评分卡输出字段46",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK200",
          "dName": "评分卡输出字段200",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK442",
          "dName": "评分卡输出字段442",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK684",
          "dName": "评分卡输出字段684",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK49",
          "dName": "评分卡输出字段49",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK203",
          "dName": "评分卡输出字段203",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK445",
          "dName": "评分卡输出字段445",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK687",
          "dName": "评分卡输出字段687",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SECURITYNAME",
          "dName": "交易认证方式名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_ACCTCASHOTB",
          "dName": "账户层取现可用额度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK48",
          "dName": "评分卡输出字段48",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK202",
          "dName": "评分卡输出字段202",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK444",
          "dName": "评分卡输出字段444",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK686",
          "dName": "评分卡输出字段686",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK205",
          "dName": "评分卡输出字段205",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK447",
          "dName": "评分卡输出字段447",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK689",
          "dName": "评分卡输出字段689",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_LOANBALANCE",
          "dName": "贷款余额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK204",
          "dName": "评分卡输出字段204",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK446",
          "dName": "评分卡输出字段446",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK688",
          "dName": "评分卡输出字段688",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK207",
          "dName": "评分卡输出字段207",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK449",
          "dName": "评分卡输出字段449",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK206",
          "dName": "评分卡输出字段206",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK448",
          "dName": "评分卡输出字段448",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK209",
          "dName": "评分卡输出字段209",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK208",
          "dName": "评分卡输出字段208",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDANALYSISTYPE",
          "dName": "银行卡解析类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_HOMEADDRESS",
          "dName": "居住地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYEEBANKNAME",
          "dName": "收款方开户行名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_ACCTCURRTIME",
          "dName": "账户当前评级更新时间",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TERMLATITUDE",
          "dName": "终端纬度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK61",
          "dName": "评分卡输出字段61",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK60",
          "dName": "评分卡输出字段60",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK63",
          "dName": "评分卡输出字段63",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK470",
          "dName": "评分卡输出字段470",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK62",
          "dName": "评分卡输出字段62",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_QUARTERLYTIME",
          "dName": "每季限笔",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK65",
          "dName": "评分卡输出字段65",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK230",
          "dName": "评分卡输出字段230",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK472",
          "dName": "评分卡输出字段472",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK64",
          "dName": "评分卡输出字段64",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK471",
          "dName": "评分卡输出字段471",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_PAYER2BFLAG",
          "dName": "付款客户对公标识",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "企业"
            },
            {
              "value": "0",
              "description": "个人"
            }
          ]
        },
        {
          "name": "S_S_INDUSTRY",
          "dName": "所属行业",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK56",
          "dName": "评分卡输出字段56",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK232",
          "dName": "评分卡输出字段232",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK474",
          "dName": "评分卡输出字段474",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK55",
          "dName": "评分卡输出字段55",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK231",
          "dName": "评分卡输出字段231",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK473",
          "dName": "评分卡输出字段473",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERACCESSFAILDREASON",
          "dName": "商户准入失败原因",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK58",
          "dName": "评分卡输出字段58",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK234",
          "dName": "评分卡输出字段234",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK476",
          "dName": "评分卡输出字段476",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK57",
          "dName": "评分卡输出字段57",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK233",
          "dName": "评分卡输出字段233",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK475",
          "dName": "评分卡输出字段475",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK236",
          "dName": "评分卡输出字段236",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK478",
          "dName": "评分卡输出字段478",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_RESTRICTIONONENTRYANDEXIT",
          "dName": "是否限制出入境",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK59",
          "dName": "评分卡输出字段59",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK235",
          "dName": "评分卡输出字段235",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK477",
          "dName": "评分卡输出字段477",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK238",
          "dName": "评分卡输出字段238",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK237",
          "dName": "评分卡输出字段237",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK479",
          "dName": "评分卡输出字段479",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERPREVIOUSRESULT",
          "dName": "上期商户评级结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK239",
          "dName": "评分卡输出字段239",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISTRANSINTERSTAFF",
          "dName": "是否与内部员工交易",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_F_QUARTERLYAMT",
          "dName": "每季限额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_GUARDNIDTYP",
          "dName": "监护人证件种类",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_BILLINGCYCLE",
          "dName": "账单周期",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BATCHTRANSNO",
          "dName": "批量决策任务编号\t",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_APLTIDTYP",
          "dName": "经办人证件种类",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DPSTTERM",
          "dName": "存期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK70",
          "dName": "评分卡输出字段70",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK72",
          "dName": "评分卡输出字段72",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK71",
          "dName": "评分卡输出字段71",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYBNKCODE",
          "dName": "支付行号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_GJJOPENDATE",
          "dName": "公积金开户日期",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_GJJTOTALMONTH",
          "dName": "累计缴存月数_公积金",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK74",
          "dName": "评分卡输出字段74",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK73",
          "dName": "评分卡输出字段73",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_LOANSTATUS",
          "dName": "分期状态",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "I",
              "description": "注册但未活动"
            },
            {
              "value": "A",
              "description": "活动状态"
            },
            {
              "value": "T",
              "description": "终止"
            },
            {
              "value": "F",
              "description": "完成"
            },
            {
              "value": "R",
              "description": "展期"
            },
            {
              "value": "S",
              "description": "缩期"
            }
          ]
        },
        {
          "name": "C_F_PFK76",
          "dName": "评分卡输出字段76",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK461",
          "dName": "评分卡输出字段461",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK75",
          "dName": "评分卡输出字段75",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK460",
          "dName": "评分卡输出字段460",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSGPS",
          "dName": "交易GPS",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BOXTYP",
          "dName": "尾箱类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK67",
          "dName": "评分卡输出字段67",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK221",
          "dName": "评分卡输出字段221",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK463",
          "dName": "评分卡输出字段463",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK66",
          "dName": "评分卡输出字段66",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK220",
          "dName": "评分卡输出字段220",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK462",
          "dName": "评分卡输出字段462",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK69",
          "dName": "评分卡输出字段69",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK223",
          "dName": "评分卡输出字段223",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK465",
          "dName": "评分卡输出字段465",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_PAYEEACCTTYPE",
          "dName": "收款方账户类型",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "Ⅰ类账户"
            },
            {
              "value": "2",
              "description": "Ⅱ类账户"
            },
            {
              "value": "3",
              "description": "Ⅲ类账户"
            },
            {
              "value": "4",
              "description": "基本账户"
            },
            {
              "value": "5",
              "description": "一般账户"
            },
            {
              "value": "6",
              "description": "临时账户"
            }
          ]
        },
        {
          "name": "C_F_PFK68",
          "dName": "评分卡输出字段68",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK222",
          "dName": "评分卡输出字段222",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK464",
          "dName": "评分卡输出字段464",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK225",
          "dName": "评分卡输出字段225",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK467",
          "dName": "评分卡输出字段467",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_BIZTIME",
          "dName": "业务发生时间",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RETCODE",
          "dName": "交易返回代码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_FRZAMT",
          "dName": "冻结金额",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK224",
          "dName": "评分卡输出字段224",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK466",
          "dName": "评分卡输出字段466",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYNO",
          "dName": "缴费号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK227",
          "dName": "评分卡输出字段227",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK469",
          "dName": "评分卡输出字段469",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERCHANTRETURNCODE",
          "dName": "商户返回码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK226",
          "dName": "评分卡输出字段226",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK468",
          "dName": "评分卡输出字段468",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK229",
          "dName": "评分卡输出字段229",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK228",
          "dName": "评分卡输出字段228",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IPTYPE",
          "dName": "IP类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISCORPSAMECONTACT",
          "dName": "是否与单位联系方式相同",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "2",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_E_HANDACCTCLSS",
          "dName": "挂账类别",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "C",
              "description": "贷方挂账"
            },
            {
              "value": "D",
              "description": "借方挂账"
            },
            {
              "value": "N",
              "description": "不挂账"
            }
          ]
        },
        {
          "name": "S_S_MERREGPROV",
          "dName": "商户经营所在省",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK81",
          "dName": "评分卡输出字段81",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RECEMOBI",
          "dName": "收货手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CURWORKINDS",
          "dName": "从事行业类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK80",
          "dName": "评分卡输出字段80",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK83",
          "dName": "评分卡输出字段83",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK490",
          "dName": "评分卡输出字段490",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK82",
          "dName": "评分卡输出字段82",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK85",
          "dName": "评分卡输出字段85",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK250",
          "dName": "评分卡输出字段250",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK492",
          "dName": "评分卡输出字段492",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK84",
          "dName": "评分卡输出字段84",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK491",
          "dName": "评分卡输出字段491",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK87",
          "dName": "评分卡输出字段87",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK252",
          "dName": "评分卡输出字段252",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK494",
          "dName": "评分卡输出字段494",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK86",
          "dName": "评分卡输出字段86",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK251",
          "dName": "评分卡输出字段251",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK493",
          "dName": "评分卡输出字段493",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK78",
          "dName": "评分卡输出字段78",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK254",
          "dName": "评分卡输出字段254",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK496",
          "dName": "评分卡输出字段496",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERMANUALBEHAVIORLEVEL",
          "dName": "商户行为手动评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK77",
          "dName": "评分卡输出字段77",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK253",
          "dName": "评分卡输出字段253",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK495",
          "dName": "评分卡输出字段495",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK256",
          "dName": "评分卡输出字段256",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK498",
          "dName": "评分卡输出字段498",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_LONGITUDE",
          "dName": "经度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK79",
          "dName": "评分卡输出字段79",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK255",
          "dName": "评分卡输出字段255",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK497",
          "dName": "评分卡输出字段497",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_PUBETRPIND",
          "dName": "公用企业标识",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_F_PFK258",
          "dName": "评分卡输出字段258",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK257",
          "dName": "评分卡输出字段257",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK499",
          "dName": "评分卡输出字段499",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK259",
          "dName": "评分卡输出字段259",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_AGECD",
          "dName": "账龄",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_LAND",
          "dName": "营业用地性质",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "租有"
            },
            {
              "value": "0",
              "description": "自用"
            },
            {
              "value": "-999",
              "description": "未知"
            }
          ]
        },
        {
          "name": "S_S_CONT3RELATION",
          "dName": "第三联系人关系",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERNAME",
          "dName": "商户名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYNOCITY",
          "dName": "缴费号码归属城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK90",
          "dName": "评分卡输出字段90",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK92",
          "dName": "评分卡输出字段92",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SUBCHANNELTYPE",
          "dName": "子渠道码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK91",
          "dName": "评分卡输出字段91",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK94",
          "dName": "评分卡输出字段94",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_CHILDBIRTH",
          "dName": "是否参加生育保险",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK93",
          "dName": "评分卡输出字段93",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK96",
          "dName": "评分卡输出字段96",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK481",
          "dName": "评分卡输出字段481",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK95",
          "dName": "评分卡输出字段95",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK480",
          "dName": "评分卡输出字段480",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK98",
          "dName": "评分卡输出字段98",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK241",
          "dName": "评分卡输出字段241",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK483",
          "dName": "评分卡输出字段483",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK97",
          "dName": "评分卡输出字段97",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK240",
          "dName": "评分卡输出字段240",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK482",
          "dName": "评分卡输出字段482",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK89",
          "dName": "评分卡输出字段89",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK243",
          "dName": "评分卡输出字段243",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK485",
          "dName": "评分卡输出字段485",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK88",
          "dName": "评分卡输出字段88",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK242",
          "dName": "评分卡输出字段242",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK484",
          "dName": "评分卡输出字段484",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK245",
          "dName": "评分卡输出字段245",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK487",
          "dName": "评分卡输出字段487",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK244",
          "dName": "评分卡输出字段244",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK486",
          "dName": "评分卡输出字段486",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK247",
          "dName": "评分卡输出字段247",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK489",
          "dName": "评分卡输出字段489",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK246",
          "dName": "评分卡输出字段246",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK488",
          "dName": "评分卡输出字段488",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK249",
          "dName": "评分卡输出字段249",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK248",
          "dName": "评分卡输出字段248",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_PRINCIPALBAL",
          "dName": "本金余额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACPTNTYP",
          "dName": "票据类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_AUTHPERID",
          "dName": "授权经办人身份证号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONTPHONE",
          "dName": "联系电话",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PHONENO",
          "dName": "电话号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_BIRTHDATE",
          "dName": "出生日期",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK270",
          "dName": "评分卡输出字段270",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IDCITY",
          "dName": "身份证归属城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK272",
          "dName": "评分卡输出字段272",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK271",
          "dName": "评分卡输出字段271",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK274",
          "dName": "评分卡输出字段274",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DETOOLRESULT",
          "dName": "决策工具返回结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK273",
          "dName": "评分卡输出字段273",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_BATCHTIMES",
          "dName": "执行序号",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK276",
          "dName": "评分卡输出字段276",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK99",
          "dName": "评分卡输出字段99",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK275",
          "dName": "评分卡输出字段275",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK278",
          "dName": "评分卡输出字段278",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK277",
          "dName": "评分卡输出字段277",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK279",
          "dName": "评分卡输出字段279",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSGPSDIST",
          "dName": "交易GPS归属区",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_SALARY",
          "dName": "个人月收入",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_INSTALLADDR",
          "dName": "装机地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_UBO3CERTTYP",
          "dName": "受益所有人3证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_LOANTERM",
          "dName": "放款期限",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACCTEXPDT",
          "dName": "账户有效期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MCC",
          "dName": "移动国家码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK261",
          "dName": "评分卡输出字段261",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_LASTYEARFLUIDCAPITAL",
          "dName": "企业上年流动资金金额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK260",
          "dName": "评分卡输出字段260",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK263",
          "dName": "评分卡输出字段263",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK262",
          "dName": "评分卡输出字段262",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK265",
          "dName": "评分卡输出字段265",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CUSTOMERSIZE",
          "dName": "客户规模",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "客户规模1"
            },
            {
              "value": "2",
              "description": "客户规模2"
            },
            {
              "value": "3",
              "description": "客户规模3"
            }
          ]
        },
        {
          "name": "C_F_PFK264",
          "dName": "评分卡输出字段264",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK267",
          "dName": "评分卡输出字段267",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK266",
          "dName": "评分卡输出字段266",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK269",
          "dName": "评分卡输出字段269",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK268",
          "dName": "评分卡输出字段268",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LIVECITY",
          "dName": "生活城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_FUNDSRATE",
          "dName": "资金利率",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_D_OPENACCTBEGINTIME",
          "dName": "开户开始时间",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_ISUPDMERCHANTINFO",
          "dName": "是否更新商户信息",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ALIPAYSUBID",
          "dName": "支付宝子商户号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDBANK",
          "dName": "银行卡开卡银行",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_IDENVERFFLAG",
          "dName": "身份核实标志",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "已核实"
            },
            {
              "value": "0",
              "description": "未核实"
            }
          ]
        },
        {
          "name": "S_S_DIPLOMA",
          "dName": "学历",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_INSURANCECONTRACT",
          "dName": "保险合同",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SURECOMPHO",
          "dName": "担保人单位座机",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_GJJLATESTDEPOSITDATE",
          "dName": "最近缴存年月_公积金",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ENTERPRISETYPE",
          "dName": "企业类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_HOUSELOANAMOUNT",
          "dName": "房贷合同金额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ERRREASON",
          "dName": "交易失败原因",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DRFTHLDRBNKNO",
          "dName": "持票人开户行号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK171",
          "dName": "评分卡输出字段171",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK170",
          "dName": "评分卡输出字段170",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK173",
          "dName": "评分卡输出字段173",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYEECUSTNO",
          "dName": "收款方客户号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK172",
          "dName": "评分卡输出字段172",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK175",
          "dName": "评分卡输出字段175",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK174",
          "dName": "评分卡输出字段174",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK177",
          "dName": "评分卡输出字段177",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BORNCITY",
          "dName": "出生城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK176",
          "dName": "评分卡输出字段176",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BLOCKEDTYP",
          "dName": "冻结类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK179",
          "dName": "评分卡输出字段179",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK178",
          "dName": "评分卡输出字段178",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LOGINID",
          "dName": "登录用户名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTOMSRATING",
          "dName": "海关评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_GRTECOMPCUSTIND",
          "dName": "是否融资性担保公司",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_S_CHILDRENFLOWR1",
          "dName": "子策略出参1",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_APPLYDATE",
          "dName": "借款申请日期",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_HOMEADDRESSPROVINCE",
          "dName": "居住地址省",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDNO",
          "dName": "银行卡号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_DCFLAG",
          "dName": "借贷标志",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "借"
            },
            {
              "value": "2",
              "description": "贷"
            }
          ]
        },
        {
          "name": "S_S_POSTYP",
          "dName": "POS类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK160",
          "dName": "评分卡输出字段160",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISBARCODE",
          "dName": "是否磁条卡交易",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_MERACCESSLEVEL",
          "dName": "商户准入自动评级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK162",
          "dName": "评分卡输出字段162",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK161",
          "dName": "评分卡输出字段161",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK164",
          "dName": "评分卡输出字段164",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK163",
          "dName": "评分卡输出字段163",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK166",
          "dName": "评分卡输出字段166",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COID",
          "dName": "共同借款人身份证",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK165",
          "dName": "评分卡输出字段165",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_CURRBAL",
          "dName": "当前余额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK168",
          "dName": "评分卡输出字段168",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK167",
          "dName": "评分卡输出字段167",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK169",
          "dName": "评分卡输出字段169",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_BEHAVIORSCORE",
          "dName": "商户行为分",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_RECENTCHECKRES",
          "dName": "最近核查结果",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "normal",
              "description": "正常"
            },
            {
              "value": "uncertain",
              "description": "待定"
            }
          ]
        },
        {
          "name": "C_F_PFK191",
          "dName": "评分卡输出字段191",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK190",
          "dName": "评分卡输出字段190",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK193",
          "dName": "评分卡输出字段193",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK192",
          "dName": "评分卡输出字段192",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK195",
          "dName": "评分卡输出字段195",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK194",
          "dName": "评分卡输出字段194",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK197",
          "dName": "评分卡输出字段197",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK196",
          "dName": "评分卡输出字段196",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK199",
          "dName": "评分卡输出字段199",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK198",
          "dName": "评分卡输出字段198",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_NEWPERMPAYEFFTDATE",
          "dName": "新可支付生效日",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SUBTRANSNAME",
          "dName": "子交易名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CNPYBANKNO",
          "dName": "对方行号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_CHILDRENFLOWP1",
          "dName": "子策略入参1",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_SUPPPOORBUSFLAG",
          "dName": "扶贫业务客户标志",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_COMOBILE",
          "dName": "共同借款人手机",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_CARLOANAMOUNT",
          "dName": "车贷合同金额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BINDCARDNO",
          "dName": "绑定账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK180",
          "dName": "评分卡输出字段180",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK182",
          "dName": "评分卡输出字段182",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SVTYP",
          "dName": "业务类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK181",
          "dName": "评分卡输出字段181",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK184",
          "dName": "评分卡输出字段184",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_CUTTENTRATINGSCORE",
          "dName": "本期系统评级分数",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK183",
          "dName": "评分卡输出字段183",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK186",
          "dName": "评分卡输出字段186",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK185",
          "dName": "评分卡输出字段185",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK188",
          "dName": "评分卡输出字段188",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK187",
          "dName": "评分卡输出字段187",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_RESERVEDMOBILE",
          "dName": "预留手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_LATITUDE",
          "dName": "纬度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_PFK189",
          "dName": "评分卡输出字段189",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTISUEAREACOD",
          "dName": "发证机关所在地区",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_N_YEARLYTIME",
          "dName": "每年限笔",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_TESTINNERSERVICETEMPLATEFIELD1",
          "dName": "测试内置服务模版字段1",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_D_OPENACCTENDTIME",
          "dName": "开户结束时间",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_CHILDRENFLOWSCORE",
          "dName": "子策略分数",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONTADDR",
          "dName": "联系地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_REMARK",
          "dName": "交易备注",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LEGAPPERSONPHONE",
          "dName": "法人联系方式",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_RUNTYPE",
          "dName": "运行模式",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "同步"
            },
            {
              "value": "2",
              "description": "异步"
            }
          ]
        },
        {
          "name": "S_S_UBO1CERTTYP",
          "dName": "受益所有人1证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYDOMAIN",
          "dName": "支付域名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REGPLCEAREACODE",
          "dName": "注册地区代码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERCURRENTRESULT",
          "dName": "商户当前评级结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DEVICEID",
          "dName": "设备ID",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDPROV",
          "dName": "银行卡归属省份",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TDZHIRONGFEN",
          "dName": "同盾智融分",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTADDR",
          "dName": "证件地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERRISKLEVEL",
          "dName": "商户风险等级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TOTALHOUSEVALUE",
          "dName": "房产总价值",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ISHOLIDAY",
          "dName": "是否是节假日",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RULESETDECISION",
          "dName": "规则集结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACTCNTRNAM",
          "dName": "实际控制人名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COMPADDR",
          "dName": "公司地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_XAFLTACCTNM",
          "dName": "附属账户名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_APIMODEL",
          "dName": "调用类型",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "10",
              "description": "实时"
            },
            {
              "value": "11",
              "description": "更新"
            },
            {
              "value": "20",
              "description": "准实时"
            },
            {
              "value": "30",
              "description": "批量决策"
            }
          ]
        },
        {
          "name": "S_S_RELATIONSHIP",
          "dName": "与持卡人关系",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISCALLAWAY",
          "dName": "是否提前赎回",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_BLACKLABEL",
          "dName": "黑标签",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_MERCUTTENTRATINGSCORE",
          "dName": "本期商户评级分数",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_CERTTYPE",
          "dName": "客户证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "身份证"
            },
            {
              "value": "2",
              "description": "户口簿"
            },
            {
              "value": "3",
              "description": "临时身份证"
            },
            {
              "value": "4",
              "description": "组织机构代码证"
            },
            {
              "value": "5",
              "description": "其他证件"
            }
          ]
        },
        {
          "name": "S_S_ANTIMNEYLAURSKCLASS",
          "dName": "反洗钱客户风险等级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BATNO",
          "dName": "批次号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CREDITREPORT",
          "dName": "征信报告",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CERTISUEDAT",
          "dName": "证件签发日期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_INDUSTRY",
          "dName": "所属行业大类",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "一类"
            },
            {
              "value": "2",
              "description": "二类"
            }
          ]
        },
        {
          "name": "S_S_HOMEPHONE",
          "dName": "住宅电话",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ORIGVCHRNO",
          "dName": "原凭证号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SPOUCECOMPANY",
          "dName": "配偶工作单位",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CREDITAGGREMENT",
          "dName": "授信协议",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_REPORTCONTENT",
          "dName": "入网报备网站内容",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_BASEINTRATE",
          "dName": "基准利率",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYNOMERNAME",
          "dName": "缴费商户名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CLIENTTYPE",
          "dName": "贷借卡客户类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ORGCODE",
          "dName": "机构编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_EDUDGR",
          "dName": "教育程度",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_PENSION",
          "dName": "是否参加养老保险",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_QQ",
          "dName": "QQ号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_USAGE",
          "dName": "贷款用途",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_METRICCODE",
          "dName": "指标编码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_REGCAPAMT",
          "dName": "企业注册资本",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IPCOUNTRY",
          "dName": "IP归属国家",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CORPCERTTYPE",
          "dName": "法人证件类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERCURRENTRATINGRESULT",
          "dName": "本期商户评级结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_INCARDBACK",
          "dName": "身份证反面",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONTNAME",
          "dName": "联系人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_E_MERACCESSSTATUS",
          "dName": "商户准入状态",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "-1",
              "description": "失败"
            },
            {
              "value": "0",
              "description": "审核中"
            },
            {
              "value": "1",
              "description": "审核拒绝"
            },
            {
              "value": "2",
              "description": "审核通过"
            },
            {
              "value": "3",
              "description": "成功"
            }
          ]
        },
        {
          "name": "C_S_TESTAND98018",
          "dName": "测试且98018",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ACCTOPNDAT",
          "dName": "开户日期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_OCPSTSCOD",
          "dName": "就业状态",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_DISHONESTYEXECUTION",
          "dName": "是否失信被执行",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRANSGPSPROV",
          "dName": "交易GPS归属省",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTMAGRBELNGBRCHID",
          "dName": "客户经理所属机构",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_STARTGPS",
          "dName": "起点GPS",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ENGAGREALESTATEDVLP",
          "dName": "是否从事房地产开发",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_B_HIGHCONSUMPTIONLIMITED",
          "dName": "是否限制高消费",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_COMPANYADDRESS",
          "dName": "单位地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_SBPAYMENTAMOUNT",
          "dName": "社保缴纳金额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_E_MERCHANTSTATUS",
          "dName": "商户状态",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "NORMAL",
              "description": "正常"
            },
            {
              "value": "FREEZE",
              "description": "冻结"
            },
            {
              "value": "UNFREEZE",
              "description": "解冻"
            },
            {
              "value": "LOGOUT",
              "description": "注销"
            }
          ]
        },
        {
          "name": "S_E_ISIPPROXY",
          "dName": "是否代理IP",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_USERRISKLEVEL",
          "dName": "用户风险等级",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DDCTCHRGSACCT",
          "dName": "扣费账户",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CTRTACCTMAGRIDN",
          "dName": "客户经理编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_N_ADDMODE",
          "dName": "可疑账户添加方式",
          "selectType": "FIELD_SYSTEM",
          "type": "INTEGER",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IPCITY",
          "dName": "IP归属城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_B_PREISHITROSTER",
          "dName": "上期评级是否命中名单",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_APLTIDNO",
          "dName": "经办人证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_TESTOR98018",
          "dName": "测试或98018",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYERACCTNO",
          "dName": "付款方账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_YEARLYAMT",
          "dName": "每年限额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CONT1RELATION",
          "dName": "第一联系人关系",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_DOMICILEADDRESS",
          "dName": "户籍地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_ACCTOTB",
          "dName": "账户可用额度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_POORHSHLDFLAG",
          "dName": "是否建档立卡贫困户",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_COMPANYNAME",
          "dName": "企业名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_CONTROLMEASURE",
          "dName": "账户管控措施",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IDNO",
          "dName": "身份证号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_PAYNOMERACCTNO",
          "dName": "缴费商户账号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CORPNPROP",
          "dName": "单位性质",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CUSTECNMCTYP",
          "dName": "经济类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDISSUER",
          "dName": "银行卡发卡机构",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_TRANSFERLIMIT",
          "dName": "非同名转账日累计限额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TERMTYPE",
          "dName": "终端类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERREGCITY",
          "dName": "商户经营所在城市",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_INCOMECERTIFICATE",
          "dName": "收入证明",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_B_ENTERPRISEEXECUTION",
          "dName": "是否企业被执行",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_OPERNO",
          "dName": "操作员编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SURECOMPANY",
          "dName": "担保企业名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_MERNATURE",
          "dName": "商户性质",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ORIGCARDNUM",
          "dName": "原卡卡号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TRUEIP",
          "dName": "真实IP",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISABROAD",
          "dName": "是否境外",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_E_ONDUTYFLAG",
          "dName": "是否当班员工",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_CUSTREGLOCT",
          "dName": "注册地址",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_GJJAMOUNTCORP",
          "dName": "公积金单位每月缴存金额",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_S_MERZFBKXRESULT",
          "dName": "商户支付宝可信调用结果",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_PAYEESTAFIND",
          "dName": "收款方是否本行员工",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "C_B_ISHITROSTER",
          "dName": "评级是否命中名单",
          "selectType": "FIELD_SYSTEM",
          "type": "BOOLEAN",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TELRTYP",
          "dName": "用户类型",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_RISKREMARK",
          "dName": "风险备注",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BELNGBRCHID",
          "dName": "客户所属机构",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SCRTYOPENMOBI ",
          "dName": "证券机构手机号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_IDPROV",
          "dName": "身份证归属省份",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDEXPDT",
          "dName": "银行卡有效期",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "C_F_MERPREVIOUSSCORE",
          "dName": "上期商户评级分数",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_D_DUEDATE",
          "dName": "营业执照到期日",
          "selectType": "FIELD_SYSTEM",
          "type": "DATE",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_ENTERPRISEPHONE",
          "dName": "企业电话",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_CARDORGID",
          "dName": "银行卡发卡机构编码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_LEGALPERSONNAME",
          "dName": "法人姓名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_IDENVERFRST",
          "dName": "身份核实结果",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "01",
              "description": "未核实"
            },
            {
              "value": "02",
              "description": "真实"
            },
            {
              "value": "03",
              "description": "假名"
            },
            {
              "value": "04",
              "description": "匿名"
            },
            {
              "value": "05",
              "description": "无法核实"
            }
          ]
        },
        {
          "name": "S_S_CORPCERTID",
          "dName": "法人证件号码",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_E_ISSAMENAME",
          "dName": "是否同名转账",
          "selectType": "FIELD_SYSTEM",
          "type": "ENUM",
          "systemField": true,
          "enumTypeValues": [
            {
              "value": "1",
              "description": "是"
            },
            {
              "value": "0",
              "description": "否"
            }
          ]
        },
        {
          "name": "S_S_VCHSTS",
          "dName": "凭证状态",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_BSNSLICENCE",
          "dName": "营业执照",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_TELLERCODE",
          "dName": "柜员编号",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_INTACCTNM",
          "dName": "内部账户户名",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_S_SUPERVISOR",
          "dName": "监事名称",
          "selectType": "FIELD_SYSTEM",
          "type": "STRING",
          "systemField": true,
          "enumTypeValues": []
        },
        {
          "name": "S_F_CREDITLIMIT",
          "dName": "信用额度",
          "selectType": "FIELD_SYSTEM",
          "type": "DOUBLE",
          "systemField": true,
          "enumTypeValues": []
        }
      ]
    },
    {
      "name": "INDEX_DATASOURCE",
      "dName": "指标",
      "selectType": "INDEX",
      "prefix": "tianzuo_",
      "bizType": "index",
      "data": [
        {
          "name": "unsettle_loanamount_ratio4_557435361173700612",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833852555270",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361228226563",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369655685123",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369693433856",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369613742084",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833881915394",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833869332480",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369634713602",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833919664132",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833928052740",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361198866432",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "per_resi_info_0003",
          "dName": "居住状况为按揭、共有住宅、自有、自置的个数",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369647296516",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369680850945",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833865138179",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369676656640",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369701822464",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369664073729",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361211449344",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369647296514",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833940635652",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369617936388",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369655685120",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361152729093",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361165312002",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369638907907",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833881915393",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369697628162",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369613742081",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369706016768",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369617936387",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833860943875",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369651490819",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361232420866",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833869332481",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369651490820",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833936441344",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361249198081",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369685045249",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369701822467",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369655685121",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369634713603",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369651490821",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833928052739",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369668268032",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369672462341",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "corporate_identities_num",
          "dName": "企业身份标识个数",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361186283523",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361245003776",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369655685122",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369672462338",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369689239553",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361236615171",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369617936385",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369706016773",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833919664131",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833860943876",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361156923396",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369638907906",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833902886912",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369638907908",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833894498306",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369664073733",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369638907905",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833932247044",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369647296512",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369697628160",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361232420867",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369634713605",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369672462340",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833844166656",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833890304001",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "jstestcba",
          "dName": "jstestcba",
          "selectType": "INDEX",
          "type": "STRING",
          "systemField": false
        },
        {
          "name": "necoPRHValueIndex",
          "dName": "necoPRH值指标",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369693433860",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361152729092",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361152729090",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833873526787",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361207255040",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361156923392",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833886109699",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369651490817",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "1312423",
          "dName": "3242342",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "necoRRHReportCustIndex",
          "dName": "necoRRHReportCustIndex",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369668268037",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833907081217",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833940635649",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369668268036",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833856749572",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833940635648",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369693433861",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361161117700",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833848360962",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369659879429",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833856749570",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361224032258",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369630519297",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369706016772",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833890304003",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361257586689",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833898692609",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833881915392",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369668268033",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833856749569",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833873526785",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369630519300",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369680850946",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369689239557",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833915469826",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369626324996",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369613742083",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369664073731",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "Corporate_identity_type",
          "dName": "企业身份标识类型",
          "selectType": "INDEX",
          "type": "ENUM",
          "systemField": false,
          "enumTypeValues": [
            {
              "value": "中征码（原贷款卡编码）",
              "description": "10"
            },
            {
              "value": "统一社会信用代码",
              "description": "20"
            },
            {
              "value": "组织机构代码",
              "description": "30"
            },
            {
              "value": "工商注册号",
              "description": "01"
            },
            {
              "value": "事业单位证书号",
              "description": "02"
            },
            {
              "value": "社会团队登记号",
              "description": "03"
            },
            {
              "value": "民办非企业登记号",
              "description": "04"
            },
            {
              "value": "基金会登记号",
              "description": "05"
            },
            {
              "value": "宗教证书登记号",
              "description": "06"
            },
            {
              "value": "纳税人识别号（国税）",
              "description": "41"
            },
            {
              "value": "纳税人识别号（地税）",
              "description": "42"
            },
            {
              "value": "机构信用代码",
              "description": "11"
            }
          ]
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361228226562",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369672462337",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833877721091",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833911275521",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369622130692",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369689239555",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369685045251",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "Economic_type",
          "dName": "经济类型",
          "selectType": "INDEX",
          "type": "ENUM",
          "systemField": false,
          "enumTypeValues": [
            {
              "value": "内资",
              "description": "100"
            },
            {
              "value": "国有全资",
              "description": "110"
            },
            {
              "value": "集体全资",
              "description": "120"
            },
            {
              "value": "股份合作",
              "description": "130"
            },
            {
              "value": "联营",
              "description": "140"
            },
            {
              "value": "国有联营",
              "description": "141"
            },
            {
              "value": "集体联营",
              "description": "142"
            },
            {
              "value": "国有与集体联营",
              "description": "143"
            },
            {
              "value": "其他联营",
              "description": "149"
            },
            {
              "value": "有限责任（公司）",
              "description": "150"
            },
            {
              "value": "其他有限责任（公司）",
              "description": "159"
            },
            {
              "value": "股份有限（公司）",
              "description": "160"
            },
            {
              "value": "私有",
              "description": "170"
            },
            {
              "value": "私有独资",
              "description": "171"
            },
            {
              "value": "私有合伙",
              "description": "172"
            },
            {
              "value": "私营有限责任（公司）",
              "description": "173"
            },
            {
              "value": "私营股份有限（公司）",
              "description": "174"
            },
            {
              "value": "个体经营",
              "description": "175"
            },
            {
              "value": "其他私有",
              "description": "179"
            },
            {
              "value": "其他内资",
              "description": "190"
            },
            {
              "value": "港、澳、台投资",
              "description": "200"
            },
            {
              "value": "内地和港、澳或台合资",
              "description": "210"
            },
            {
              "value": "内地和港、澳或台合作",
              "description": "220"
            },
            {
              "value": "港、澳或台独资",
              "description": "230"
            },
            {
              "value": "港、澳或台投资股份有限（公司）",
              "description": "240"
            },
            {
              "value": "其他港澳台投资",
              "description": "290"
            },
            {
              "value": "国外投资",
              "description": "300"
            },
            {
              "value": "中外合资",
              "description": "310"
            },
            {
              "value": "中外合作",
              "description": "320"
            },
            {
              "value": "外资",
              "description": "330"
            },
            {
              "value": "国外投资股份有限（公司）",
              "description": "340"
            },
            {
              "value": "其他国外投资",
              "description": "390"
            },
            {
              "value": "其他",
              "description": "900"
            }
          ]
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833915469824",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833898692608",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369697628165",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369706016769",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833848360965",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833852555264",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369676656645",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833936441347",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833869332484",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361224032259",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "Reason_for_query",
          "dName": "本次查询原因",
          "selectType": "INDEX",
          "type": "STRING",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833869332482",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "Organization_type",
          "dName": "组织机构类型",
          "selectType": "INDEX",
          "type": "ENUM",
          "systemField": false,
          "enumTypeValues": [
            {
              "value": "企业",
              "description": "1"
            },
            {
              "value": "公司",
              "description": "11"
            },
            {
              "value": "非公司制企业法人",
              "description": "13"
            },
            {
              "value": "企业分支机构",
              "description": "15"
            },
            {
              "value": "个人独资企业、合伙企业",
              "description": "17"
            },
            {
              "value": "其他企业",
              "description": "19"
            },
            {
              "value": "机关",
              "description": "3"
            },
            {
              "value": "中国共产党",
              "description": "31"
            },
            {
              "value": "国家权利机关法人",
              "description": "32"
            },
            {
              "value": "国家行政机关法人",
              "description": "33"
            },
            {
              "value": "国家司法机关法人",
              "description": "34"
            },
            {
              "value": "政协组织",
              "description": "35"
            },
            {
              "value": "民主党派",
              "description": "36"
            },
            {
              "value": "人民解放军、武警部队",
              "description": "37"
            },
            {
              "value": "其他机关",
              "description": "39"
            },
            {
              "value": "事业单位",
              "description": "5"
            },
            {
              "value": "事业单位法人",
              "description": "51"
            },
            {
              "value": "事业单位分支、派出机构",
              "description": "53"
            },
            {
              "value": "其他事业单位",
              "description": "59"
            },
            {
              "value": "社会团体",
              "description": "7"
            },
            {
              "value": "社会团体法人",
              "description": "71"
            },
            {
              "value": "社会团体分支、代表机构",
              "description": "73"
            },
            {
              "value": "其他社会团体",
              "description": "79"
            },
            {
              "value": "其他组织机构",
              "description": "9"
            },
            {
              "value": "民办非企业单位",
              "description": "91"
            },
            {
              "value": "基金会",
              "description": "93"
            },
            {
              "value": "宗教活动场所",
              "description": "94"
            },
            {
              "value": "农村村民居委会",
              "description": "95"
            },
            {
              "value": "城市居民委员会",
              "description": "96"
            },
            {
              "value": "自定义区",
              "description": "97"
            },
            {
              "value": "其他未列明的组织机构",
              "description": "99"
            },
            {
              "value": "个体工商户",
              "description": "A"
            }
          ]
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833932247041",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369664073728",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369651490818",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369647296517",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369680850949",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833902886915",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "necoReportHeaderInfoIndex",
          "dName": "neco报文头信息模版指标",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "sctest00111",
          "dName": "sctest00101",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833936441345",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "Company_Name",
          "dName": "企业名称",
          "selectType": "INDEX",
          "type": "STRING",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361173700609",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833890304004",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "objections_num",
          "dName": "异议标注数目",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833902886916",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "2342342",
          "dName": "2342342342",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833936441350",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369643102211",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369701822465",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833919664130",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361219837955",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833852555265",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833915469825",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369664073732",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833907081216",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833898692611",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833932247043",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833911275522",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361186283520",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833852555268",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369643102212",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369647296515",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833848360964",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "sctest1205",
          "dName": "测试外数指标加工",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369622130690",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369685045253",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369630519296",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833886109701",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369697628163",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833865138180",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "necoReportHeaderJavaIndex",
          "dName": "neco报文头信息Java指标",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369613742085",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369697628164",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361165312001",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361219837953",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369630519298",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833873526784",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369638907909",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361215643651",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833944829952",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369706016771",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361245003778",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361161117699",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369685045248",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369680850947",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "prhInteger_b53ea0a1b7844dd0ab8e1bfe648a3a7e",
          "dName": "(个人缴存比例大于等于 6)余额最小值 +",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361203060736",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369685045252",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369630519299",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369668268035",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369680850948",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833890304002",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369701822470",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833873526786",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361207255042",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369626324995",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "jstestPython",
          "dName": "jstestPython",
          "selectType": "INDEX",
          "type": "STRING",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833919664129",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833907081218",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369701822466",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833856749571",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369668268034",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833894498309",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361219837954",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369680850944",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "sctest01218",
          "dName": "sctest1218",
          "selectType": "INDEX",
          "type": "INTEGER",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369622130689",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833881915395",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833928052737",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369651490816",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369609547780",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361198866434",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369701822469",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "Query_org_code1",
          "dName": "查询机构代码",
          "selectType": "INDEX",
          "type": "STRING",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369672462336",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833860943874",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833848360961",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369689239556",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833907081220",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369689239552",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369706016770",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "testStringFeatureDy",
          "dName": "测试外数指标字符取值dy",
          "selectType": "INDEX",
          "type": "STRING",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369638907904",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361194672129",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "jstest11a",
          "dName": "jstest11a",
          "selectType": "INDEX",
          "type": "STRING",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833932247040",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369676656644",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369617936384",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369689239554",
          "dName": "企业未结清、开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833860943872",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361194672130",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361257586688",
          "dName": "企业未结清、开户日期在近24个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833898692610",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361169506309",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "testBooleanFeatureDy",
          "dName": "测试外数指标布尔取值dy",
          "selectType": "INDEX",
          "type": "BOOLEAN",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833886109700",
          "dName": "企业未结清、开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833915469827",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833928052738",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio4_557435361156923397",
          "dName": "企业未结清、开户日期在近1个月内、五级分类为损失&违约的借贷交易借款余额平均与企业开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369664073730",
          "dName": "企业未结清、开户日期在近6个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833873526788",
          "dName": "企业未结清、开户日期在近2个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近24个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio5_557436833923858434",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均与企业开户日期在近18个月内、五级分类为全部的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        },
        {
          "name": "unsettle_loanamount_ratio6_557438369672462339",
          "dName": "企业未结清、开户日期在近12个月内、五级分类为全部的借贷交易借款余额平均与企业开户日期在近3个月内、五级分类为关注&次级&可疑&损失&违约的借贷交易借款余额平均的比例",
          "selectType": "INDEX",
          "type": "DOUBLE",
          "systemField": false
        }
      ]
    }
  ]
}