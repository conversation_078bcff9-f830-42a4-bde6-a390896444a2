module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "total": 6,
    "curPage": 1,
    "pageSize": 1000,
    "pages": 1,
    "size": 6,
    "contents": [
      {
        "canWriter": true,
        "creator": "csb(csb)",
        "operator": "csb(csb)",
        "gmtCreate": "2024-12-11 16:24:57",
        "gmtModify": "2024-12-11 16:24:57",
        "uuid": "8952dc99668f4b4f810e9bbedba24e94",
        "displayName": "同盾",
        "status": 1
      },
      {
        "canWriter": true,
        "creator": "wty(wty)",
        "operator": "wty(wty)",
        "gmtCreate": "2024-08-16 14:58:33",
        "gmtModify": "2024-08-16 14:58:33",
        "uuid": "682fcc1b5a944685a33bf928926b2661",
        "displayName": "泰康",
        "status": 1
      },
      {
        "canWriter": true,
        "creator": "yujiang.ye.mock(yujiang.ye.mock)",
        "operator": "yujiang.ye.mock(yujiang.ye.mock)",
        "gmtCreate": "2024-07-10 21:07:48",
        "gmtModify": "2024-07-10 21:07:48",
        "uuid": "5094bc5bf1f34f7e9a3a82f3359e9a13",
        "displayName": "necoMock",
        "status": 1
      },
      {
        "canWriter": true,
        "creator": "yujiang.ye(yujiang.ye)",
        "operator": "byh(byh)",
        "gmtCreate": "2024-06-17 19:29:44",
        "gmtModify": "2024-12-16 10:05:23",
        "uuid": "5a36a98863b948bf9db56dbba55175f3",
        "displayName": "neco",
        "status": 1
      },
      {
        "canWriter": true,
        "creator": "zyy(zyy)",
        "operator": "zyy(zyy)",
        "gmtCreate": "2024-04-12 14:10:13",
        "gmtModify": "2024-04-12 14:10:13",
        "uuid": "74338757d9084d0f8e1b634483a41dc8",
        "displayName": "NBA",
        "status": 1
      },
      {
        "canWriter": true,
        "creator": "zyy(zyy)",
        "operator": "zyy(zyy)",
        "gmtCreate": "2024-04-10 10:11:53",
        "gmtModify": "2024-04-10 10:11:53",
        "uuid": "a4ced98eb646438c96c1d38624f1e8e7",
        "displayName": "泰康",
        "status": 1
      }
    ]
  }
}