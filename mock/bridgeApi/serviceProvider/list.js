module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "total": 4,
    "curPage": 1,
    "pageSize": 1000,
    "pages": 1,
    "size": 4,
    "contents": [
      {
        "creator": "hjt(hjt)",
        "operator": "hjt(hjt)",
        "gmtCreate": "2024-11-19 13:55:05",
        "gmtModify": "2024-11-19 13:55:05",
        "uuid": "01f20cb9e18f4ea993868b07bab5f927",
        "displayName": "合作方_自动化测试_数据沉淀",
        "status": 1
      },
      {
        "creator": "youyue.chang(youyue.chang)",
        "operator": "youyue.chang(youyue.chang)",
        "gmtCreate": "2024-11-18 15:12:40",
        "gmtModify": "2024-11-18 15:12:40",
        "uuid": "c0aeeee9400d41a7a142a33acd105916",
        "displayName": "CYY供应商",
        "status": 1
      },
      {
        "creator": "zzf(zzf)",
        "operator": "zzf(zzf)",
        "gmtCreate": "2024-11-13 10:43:19",
        "gmtModify": "2024-11-13 10:43:19",
        "uuid": "1eec4ec3d7584b70832f8d3838781d70",
        "displayName": "zzf_test_合作方1",
        "status": 1
      },
      {
        "creator": "hjt(hjt)",
        "operator": "admin(administrator)",
        "gmtCreate": "2024-11-05 19:44:46",
        "gmtModify": "2024-11-12 21:16:02",
        "uuid": "be4a3db45f214e4ab12390a84d3300af",
        "displayName": "测试合作方",
        "status": 1
      }
    ]
  }
}