module.exports = {
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "uuid": "2ab406d5939a460dad6b5335a23f2b85",
    "functionCode": "F2958335375",
    "functionName": "sc_test12031",
    "type": 1,
    "appCode": "init",
    "orgCode": "testorg6",
    "description": null,
    "status": 1,
    "isDeleted": 0,
    "createBy": "chen.shen",
    "updateBy": "chen.shen",
    "gmtCreate": "2024-12-03 14:43:10",
    "gmtModified": "2024-12-03 14:43:10",
    "extension": null,
    "extensionMap": {},
    "functionVersionDTOList": [
      {
        "uuid": "501279516c0340a4a015b1e4180d2a0b",
        "functionUuid": "2ab406d5939a460dad6b5335a23f2b85",
        "status": 2,
        "version": 1,
        "isDeleted": 0,
        "createBy": "chen.shen",
        "updateBy": "chen.shen",
        "gmtCreate": "2024-12-03 14:43:10",
        "gmtModified": "2024-12-03 14:43:10",
        "extension": null,
        "script": "αDOUBLE@C_F_PFK730=#sum(2,3);",
        "functionInfo": "{\"params\":{\"output\":[\"C_F_PFK730\"],\"input\":[]}}",
        "description": null,
        "extensionMap": {}
      }
    ],
    "onlineVersion": null,
    "viewVersion": null,
    "auditInfo": null,
    "authorizationApps": [],
    "authorizationOrgs": [],
    "canWriter": true
  }
}