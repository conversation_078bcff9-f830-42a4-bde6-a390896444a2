module.exports = {
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": {
    "total": 8,
    "curPage": 1,
    "pageSize": 10,
    "pages": 1,
    "size": 8,
    "contents": [
      {
        "uuid": "9e79178dfa3f48aba3c6e81d5a10f3ce",
        "functionCode": "F1931136745",
        "functionName": "测试指标结果",
        "type": 1,
        "appCode": "test_add",
        "orgCode": "TongDun",
        "description": null,
        "status": 4,
        "isDeleted": null,
        "createBy": "chen.shen(申晨)",
        "updateBy": "chen.shen(申晨)",
        "gmtCreate": "2024-12-06 15:30:17",
        "gmtModified": "2024-12-06 15:30:25",
        "extension": null,
        "extensionMap": {},
        "functionVersionDTOList": [
          {
            "uuid": "ad3ae36dfa36452d96a85e741f4152e2",
            "functionUuid": "9e79178dfa3f48aba3c6e81d5a10f3ce",
            "status": 4,
            "version": 1,
            "isDeleted": 0,
            "createBy": "chen.shen",
            "updateBy": "chen.shen",
            "gmtCreate": "2024-12-06 15:30:17",
            "gmtModified": "2024-12-06 15:30:25",
            "extension": null,
            "script": "αSTRING@S_S_CUSTCRTDAT=#concat(αSTRING@S_S_METRICRESULT,αSTRING@C_S_NODERESULTLIST);",
            "functionInfo": "{\"params\":{\"output\":[\"S_S_CUSTCRTDAT\"],\"input\":[\"S_S_METRICRESULT\",\"C_S_NODERESULTLIST\"]}}",
            "description": null,
            "extensionMap": {}
          }
        ],
        "onlineVersion": null,
        "viewVersion": {
          "uuid": "ad3ae36dfa36452d96a85e741f4152e2",
          "functionUuid": "9e79178dfa3f48aba3c6e81d5a10f3ce",
          "status": 4,
          "version": 1,
          "isDeleted": 0,
          "createBy": "chen.shen",
          "updateBy": "chen.shen",
          "gmtCreate": "2024-12-06 15:30:17",
          "gmtModified": "2024-12-06 15:30:25",
          "extension": null,
          "script": "αSTRING@S_S_CUSTCRTDAT=#concat(αSTRING@S_S_METRICRESULT,αSTRING@C_S_NODERESULTLIST);",
          "functionInfo": "{\"params\":{\"output\":[\"S_S_CUSTCRTDAT\"],\"input\":[\"S_S_METRICRESULT\",\"C_S_NODERESULTLIST\"]}}",
          "description": null,
          "extensionMap": {}
        },
        "auditInfo": null,
        "authorizationApps": [],
        "authorizationOrgs": [],
        "canWriter": true
      },
      {
        "uuid": "539268e1de7247a1b73652970fceec63",
        "functionCode": "F4142050744",
        "functionName": "1234",
        "type": 1,
        "appCode": "bug404503",
        "orgCode": "testorg8",
        "description": null,
        "status": 4,
        "isDeleted": null,
        "createBy": "sc1(sc1)",
        "updateBy": "sc1(sc1)",
        "gmtCreate": "2024-12-03 16:40:05",
        "gmtModified": "2024-12-03 16:40:23",
        "extension": null,
        "extensionMap": {},
        "functionVersionDTOList": [
          {
            "uuid": "15e011a23e4c4bb584706248b4f49b63",
            "functionUuid": "539268e1de7247a1b73652970fceec63",
            "status": 4,
            "version": 1,
            "isDeleted": 0,
            "createBy": "sc1",
            "updateBy": "sc1",
            "gmtCreate": "2024-12-03 16:40:05",
            "gmtModified": "2024-12-03 16:40:23",
            "extension": null,
            "script": "αDOUBLE@C_F_PFK724=1+2;",
            "functionInfo": "{\"params\":{\"output\":[\"C_F_PFK724\"],\"input\":[]}}",
            "description": null,
            "extensionMap": {}
          }
        ],
        "onlineVersion": null,
        "viewVersion": {
          "uuid": "15e011a23e4c4bb584706248b4f49b63",
          "functionUuid": "539268e1de7247a1b73652970fceec63",
          "status": 4,
          "version": 1,
          "isDeleted": 0,
          "createBy": "sc1",
          "updateBy": "sc1",
          "gmtCreate": "2024-12-03 16:40:05",
          "gmtModified": "2024-12-03 16:40:23",
          "extension": null,
          "script": "αDOUBLE@C_F_PFK724=1+2;",
          "functionInfo": "{\"params\":{\"output\":[\"C_F_PFK724\"],\"input\":[]}}",
          "description": null,
          "extensionMap": {}
        },
        "auditInfo": null,
        "authorizationApps": [],
        "authorizationOrgs": [],
        "canWriter": true
      },
      {
        "uuid": "2ab406d5939a460dad6b5335a23f2b85",
        "functionCode": "F2958335375",
        "functionName": "sc_test12031",
        "type": 1,
        "appCode": "init",
        "orgCode": "testorg6",
        "description": "",
        "status": 1,
        "isDeleted": null,
        "createBy": "chen.shen(申晨)",
        "updateBy": "byh(byh)",
        "gmtCreate": "2024-12-03 14:43:10",
        "gmtModified": "2024-12-27 16:56:48",
        "extension": null,
        "extensionMap": {},
        "functionVersionDTOList": [
          {
            "uuid": "501279516c0340a4a015b1e4180d2a0b",
            "functionUuid": "2ab406d5939a460dad6b5335a23f2b85",
            "status": 2,
            "version": 1,
            "isDeleted": 0,
            "createBy": "chen.shen",
            "updateBy": "byh",
            "gmtCreate": "2024-12-03 14:43:10",
            "gmtModified": "2024-12-27 16:56:48",
            "extension": null,
            "script": "αDOUBLE@C_F_PFK730=#sum(2,3);",
            "functionInfo": "{\"params\":{\"output\":[\"C_F_PFK730\"],\"input\":[]}}",
            "description": null,
            "extensionMap": {}
          }
        ],
        "onlineVersion": null,
        "viewVersion": {
          "uuid": "501279516c0340a4a015b1e4180d2a0b",
          "functionUuid": "2ab406d5939a460dad6b5335a23f2b85",
          "status": 2,
          "version": 1,
          "isDeleted": 0,
          "createBy": "chen.shen",
          "updateBy": "byh",
          "gmtCreate": "2024-12-03 14:43:10",
          "gmtModified": "2024-12-27 16:56:48",
          "extension": null,
          "script": "αDOUBLE@C_F_PFK730=#sum(2,3);",
          "functionInfo": "{\"params\":{\"output\":[\"C_F_PFK730\"],\"input\":[]}}",
          "description": null,
          "extensionMap": {}
        },
        "auditInfo": null,
        "authorizationApps": [],
        "authorizationOrgs": [],
        "canWriter": true
      },
      {
        "uuid": "d53f223fa52a4dbf9ff702d739c98143",
        "functionCode": "F1736677481",
        "functionName": "sc0311test函数库",
        "type": 1,
        "appCode": "init",
        "orgCode": "testorg7",
        "description": null,
        "status": 4,
        "isDeleted": null,
        "createBy": "chen.shen(申晨)",
        "updateBy": "chen.shen(申晨)",
        "gmtCreate": "2024-12-03 14:41:48",
        "gmtModified": "2024-12-03 14:42:24",
        "extension": null,
        "extensionMap": {},
        "functionVersionDTOList": [
          {
            "uuid": "b7c9228d0e504e83878dd4ed7cee064b",
            "functionUuid": "d53f223fa52a4dbf9ff702d739c98143",
            "status": 4,
            "version": 1,
            "isDeleted": 0,
            "createBy": "chen.shen",
            "updateBy": "chen.shen",
            "gmtCreate": "2024-12-03 14:41:48",
            "gmtModified": "2024-12-03 14:42:24",
            "extension": null,
            "script": "αSTRING@S_S_CUSTCRTDAT=#toString(αDOUBLE@C_F_PFK731);",
            "functionInfo": "{\"params\":{\"output\":[\"S_S_CUSTCRTDAT\"],\"input\":[\"C_F_PFK731\"]}}",
            "description": null,
            "extensionMap": {}
          }
        ],
        "onlineVersion": null,
        "viewVersion": {
          "uuid": "b7c9228d0e504e83878dd4ed7cee064b",
          "functionUuid": "d53f223fa52a4dbf9ff702d739c98143",
          "status": 4,
          "version": 1,
          "isDeleted": 0,
          "createBy": "chen.shen",
          "updateBy": "chen.shen",
          "gmtCreate": "2024-12-03 14:41:48",
          "gmtModified": "2024-12-03 14:42:24",
          "extension": null,
          "script": "αSTRING@S_S_CUSTCRTDAT=#toString(αDOUBLE@C_F_PFK731);",
          "functionInfo": "{\"params\":{\"output\":[\"S_S_CUSTCRTDAT\"],\"input\":[\"C_F_PFK731\"]}}",
          "description": null,
          "extensionMap": {}
        },
        "auditInfo": null,
        "authorizationApps": [],
        "authorizationOrgs": [],
        "canWriter": true
      },
      {
        "uuid": "997ecaaea07046918461c8f0f026732f",
        "functionCode": "F1386689889",
        "functionName": "12",
        "type": 1,
        "appCode": "init",
        "orgCode": "testorg7",
        "description": null,
        "status": 4,
        "isDeleted": null,
        "createBy": "sc1(sc1)",
        "updateBy": "sc1(sc1)",
        "gmtCreate": "2024-12-03 14:38:46",
        "gmtModified": "2024-12-03 14:39:05",
        "extension": null,
        "extensionMap": {},
        "functionVersionDTOList": [
          {
            "uuid": "dd00fc280fe6433da90dfba318ad51f6",
            "functionUuid": "997ecaaea07046918461c8f0f026732f",
            "status": 4,
            "version": 1,
            "isDeleted": 0,
            "createBy": "sc1",
            "updateBy": "sc1",
            "gmtCreate": "2024-12-03 14:38:47",
            "gmtModified": "2024-12-03 14:39:05",
            "extension": null,
            "script": "αDOUBLE@C_F_PFK731=#sum(1,2);",
            "functionInfo": "{\"params\":{\"output\":[\"C_F_PFK731\"],\"input\":[]}}",
            "description": null,
            "extensionMap": {}
          }
        ],
        "onlineVersion": null,
        "viewVersion": {
          "uuid": "dd00fc280fe6433da90dfba318ad51f6",
          "functionUuid": "997ecaaea07046918461c8f0f026732f",
          "status": 4,
          "version": 1,
          "isDeleted": 0,
          "createBy": "sc1",
          "updateBy": "sc1",
          "gmtCreate": "2024-12-03 14:38:47",
          "gmtModified": "2024-12-03 14:39:05",
          "extension": null,
          "script": "αDOUBLE@C_F_PFK731=#sum(1,2);",
          "functionInfo": "{\"params\":{\"output\":[\"C_F_PFK731\"],\"input\":[]}}",
          "description": null,
          "extensionMap": {}
        },
        "auditInfo": null,
        "authorizationApps": [],
        "authorizationOrgs": [],
        "canWriter": true
      },
      {
        "uuid": "d1716960bc6241a48190be78cf7d153f",
        "functionCode": "F7539884230",
        "functionName": "sc_test1203",
        "type": 1,
        "appCode": "init",
        "orgCode": "TongDun",
        "description": null,
        "status": 4,
        "isDeleted": null,
        "createBy": "chen.shen(申晨)",
        "updateBy": "chen.shen(申晨)",
        "gmtCreate": "2024-12-03 11:15:24",
        "gmtModified": "2024-12-03 13:36:45",
        "extension": null,
        "extensionMap": {},
        "functionVersionDTOList": [
          {
            "uuid": "a279b7c386414279926ae3307d5c38a4",
            "functionUuid": "d1716960bc6241a48190be78cf7d153f",
            "status": 4,
            "version": 1,
            "isDeleted": 0,
            "createBy": "chen.shen",
            "updateBy": "chen.shen",
            "gmtCreate": "2024-12-03 11:15:24",
            "gmtModified": "2024-12-03 13:36:45",
            "extension": null,
            "script": "αSTRING@C_S_TEST=#toString(αDOUBLE@C_F_PFK731);",
            "functionInfo": "{\"params\":{\"output\":[\"C_S_TEST\"],\"input\":[\"C_F_PFK731\"]}}",
            "description": null,
            "extensionMap": {}
          }
        ],
        "onlineVersion": null,
        "viewVersion": {
          "uuid": "a279b7c386414279926ae3307d5c38a4",
          "functionUuid": "d1716960bc6241a48190be78cf7d153f",
          "status": 4,
          "version": 1,
          "isDeleted": 0,
          "createBy": "chen.shen",
          "updateBy": "chen.shen",
          "gmtCreate": "2024-12-03 11:15:24",
          "gmtModified": "2024-12-03 13:36:45",
          "extension": null,
          "script": "αSTRING@C_S_TEST=#toString(αDOUBLE@C_F_PFK731);",
          "functionInfo": "{\"params\":{\"output\":[\"C_S_TEST\"],\"input\":[\"C_F_PFK731\"]}}",
          "description": null,
          "extensionMap": {}
        },
        "auditInfo": null,
        "authorizationApps": [],
        "authorizationOrgs": [],
        "canWriter": true
      },
      {
        "uuid": "0e83b8dc2ed04f07960b9332d8db067d",
        "functionCode": "F6390713288",
        "functionName": "byh_test",
        "type": 1,
        "appCode": "init",
        "orgCode": "TongDun",
        "description": "byh_test",
        "status": 1,
        "isDeleted": null,
        "createBy": "byh(byh)",
        "updateBy": "byh(byh)",
        "gmtCreate": "2024-10-17 15:28:55",
        "gmtModified": "2024-12-03 09:28:18",
        "extension": null,
        "extensionMap": {},
        "functionVersionDTOList": [
          {
            "uuid": "2ed1790726c2462589acfc455fa5b8ae",
            "functionUuid": "0e83b8dc2ed04f07960b9332d8db067d",
            "status": 2,
            "version": 1,
            "isDeleted": 0,
            "createBy": "byh",
            "updateBy": "byh",
            "gmtCreate": "2024-10-17 15:28:55",
            "gmtModified": "2024-10-17 15:28:55",
            "extension": null,
            "script": "αDATETIME@S_D_LOANDATE=#toDateTime(\"2024-01-15 11:10:12\");",
            "functionInfo": "{\"params\":{\"output\":[\"S_D_LOANDATE\"],\"input\":[]}}",
            "description": null,
            "extensionMap": {}
          }
        ],
        "onlineVersion": null,
        "viewVersion": {
          "uuid": "2ed1790726c2462589acfc455fa5b8ae",
          "functionUuid": "0e83b8dc2ed04f07960b9332d8db067d",
          "status": 2,
          "version": 1,
          "isDeleted": 0,
          "createBy": "byh",
          "updateBy": "byh",
          "gmtCreate": "2024-10-17 15:28:55",
          "gmtModified": "2024-10-17 15:28:55",
          "extension": null,
          "script": "αDATETIME@S_D_LOANDATE=#toDateTime(\"2024-01-15 11:10:12\");",
          "functionInfo": "{\"params\":{\"output\":[\"S_D_LOANDATE\"],\"input\":[]}}",
          "description": null,
          "extensionMap": {}
        },
        "auditInfo": null,
        "authorizationApps": [],
        "authorizationOrgs": [],
        "canWriter": true
      },
      {
        "uuid": "ed79d971a2364ca4bfa96306a3d31ed7",
        "functionCode": "F2905994825",
        "functionName": "func",
        "type": 1,
        "appCode": "init",
        "orgCode": "TongDun",
        "description": "",
        "status": 4,
        "isDeleted": null,
        "createBy": "fujun.cai(蔡福君)",
        "updateBy": "chen.shen(申晨)",
        "gmtCreate": "2024-10-11 16:16:25",
        "gmtModified": "2024-12-03 20:03:58",
        "extension": null,
        "extensionMap": {},
        "functionVersionDTOList": [
          {
            "uuid": "82d5b57035014a2882702d5ceaffe24c",
            "functionUuid": "ed79d971a2364ca4bfa96306a3d31ed7",
            "status": 4,
            "version": 2,
            "isDeleted": 0,
            "createBy": "fujun.cai",
            "updateBy": "chen.shen",
            "gmtCreate": "2024-10-22 11:21:18",
            "gmtModified": "2024-12-03 20:03:58",
            "extension": null,
            "script": "αSTRING@C_S_NODERESULTLIST=#replace(\"Hello, World\",\"Hello\",\"Hi\");\nαSTRING@C_S_TEST=#toString(123);\nαSTRING@S_S_CUSTCRTDAT=#toJsonMerging(\"{\\\"name\\\": \\\"John\\\"}\",\"{\\\"age\\\": \\\"123\\\"}\");\nαSTRING@S_S_MERREGDATE=#SformatDate(\"20190422 04:57:49\",\"yyyyMMdd HH:mm:ss\",\"MM/dd/yyyy\");\nαDOUBLE@C_F_PFK731=#toDouble(\"123131.11544\");\nαDATETIME@S_D_LOANDATE=#toDateTime(\"2024-01-15 11:10:12\");\nαSTRING@S_S_MPSUBID=#substring(\"Hello\",1,2);",
            "functionInfo": "{\"params\":{\"output\":[\"C_S_NODERESULTLIST\",\"C_S_TEST\",\"S_S_CUSTCRTDAT\",\"S_S_MERREGDATE\",\"C_F_PFK731\",\"S_D_LOANDATE\",\"S_S_MPSUBID\"],\"input\":[]}}",
            "description": null,
            "extensionMap": {}
          },
          {
            "uuid": "c278edfca6f24709af6d1bade60f17a4",
            "functionUuid": "ed79d971a2364ca4bfa96306a3d31ed7",
            "status": 5,
            "version": 1,
            "isDeleted": 0,
            "createBy": "fujun.cai",
            "updateBy": "fujun.cai",
            "gmtCreate": "2024-10-11 16:16:25",
            "gmtModified": "2024-10-22 11:21:38",
            "extension": null,
            "script": "αSTRING@C_S_NODERESULTLIST=#replace(\"Hello, World\",\"Hello\",\"Hi\");\nαSTRING@C_S_TEST=#toString(123);\nαSTRING@S_S_CUSTCRTDAT=#toJsonMerging(\"{\\\"name\\\": \\\"John\\\"}\",\"{\\\"age\\\": \\\"123\\\"}\");\nαSTRING@S_S_MERREGDATE=#SformatDate(\"20190422 04:57:49\",\"yyyyMMdd HH:mm:ss\",\"MM/dd/yyyy\");\nαDOUBLE@C_F_PFK731=#toDouble(\"123131.11544\");\nαDATETIME@S_D_LOANDATE=#toDateTime(\"2024-01-15 11:10:12\");",
            "functionInfo": "{\"params\":{\"output\":[\"C_S_NODERESULTLIST\",\"C_S_TEST\",\"S_S_CUSTCRTDAT\",\"S_S_MERREGDATE\",\"C_F_PFK731\",\"S_D_LOANDATE\"],\"input\":[]}}",
            "description": null,
            "extensionMap": {}
          }
        ],
        "onlineVersion": null,
        "viewVersion": {
          "uuid": "82d5b57035014a2882702d5ceaffe24c",
          "functionUuid": "ed79d971a2364ca4bfa96306a3d31ed7",
          "status": 4,
          "version": 2,
          "isDeleted": 0,
          "createBy": "fujun.cai",
          "updateBy": "chen.shen",
          "gmtCreate": "2024-10-22 11:21:18",
          "gmtModified": "2024-12-03 20:03:58",
          "extension": null,
          "script": "αSTRING@C_S_NODERESULTLIST=#replace(\"Hello, World\",\"Hello\",\"Hi\");\nαSTRING@C_S_TEST=#toString(123);\nαSTRING@S_S_CUSTCRTDAT=#toJsonMerging(\"{\\\"name\\\": \\\"John\\\"}\",\"{\\\"age\\\": \\\"123\\\"}\");\nαSTRING@S_S_MERREGDATE=#SformatDate(\"20190422 04:57:49\",\"yyyyMMdd HH:mm:ss\",\"MM/dd/yyyy\");\nαDOUBLE@C_F_PFK731=#toDouble(\"123131.11544\");\nαDATETIME@S_D_LOANDATE=#toDateTime(\"2024-01-15 11:10:12\");\nαSTRING@S_S_MPSUBID=#substring(\"Hello\",1,2);",
          "functionInfo": "{\"params\":{\"output\":[\"C_S_NODERESULTLIST\",\"C_S_TEST\",\"S_S_CUSTCRTDAT\",\"S_S_MERREGDATE\",\"C_F_PFK731\",\"S_D_LOANDATE\",\"S_S_MPSUBID\"],\"input\":[]}}",
          "description": null,
          "extensionMap": {}
        },
        "auditInfo": null,
        "authorizationApps": [],
        "authorizationOrgs": [],
        "canWriter": true
      }
    ]
  }
}