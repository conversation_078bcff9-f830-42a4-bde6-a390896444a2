module.exports = {
  "success": true,
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "uuid": "43e68f92-8ae1-4d7f-9eba-056d84438ef2",
      "displayName": "策略运行节点详情",
      "i18nDisplayName": {
        "en_US": "Policy run node detail",
        "zh_CN": "策略运行节点详情"
      },
      "name": "C_S_NODERESULTLIST",
      "dataType": 1,
      "funcType": 1,
      "groupDisplayName": "系统内置",
      "groupUuid": "5e43f5f4-2264-45e6-a403-f458b5199bb6",
      "enDisplayName": "C_S_NODERESULTLIST",
      "fieldInfo": "",
      "extend": [],
      "systemField": true
    },
    {
      "uuid": "006afdc9dfe311eca54cf8f21edaf900",
      "displayName": "指标结果",
      "i18nDisplayName": {
        "en_US": "Performance Results",
        "zh_CN": "指标结果"
      },
      "name": "S_S_METRICRESULT",
      "dataType": 1,
      "funcType": 1,
      "groupDisplayName": "服务模板",
      "groupUuid": "006afb35dfe311eca54cf8f21edaf900",
      "enDisplayName": "S_S_METRICRESULT",
      "fieldInfo": "",
      "extend": [],
      "systemField": true
    }
  ]
}