module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "channelServiceName": "23456",
    "channelServiceDisplayName": "真实接口不限流",
    "inputMapping": [
      {
        "serviceParam": "abc",
        "displayName": "客户开立日期",
        "type": "variable",
        "field": "S_S_CUSTCRTDAT",
        "paasDown": false,
        "order": 0,
        "dataType": 1,
        "mustInput": true,
        "sendSpace": "url",
        "keyInclude": true,
        "fromContract": false
      },
      {
        "serviceParam": "fileName",
        "value": "1",
        "type": "constant",
        "paasDown": false,
        "order": 1,
        "mustInput": false,
        "sendSpace": "url",
        "keyInclude": false,
        "fromContract": false
      }
    ],
    "outputMapping": [
      {
        "serviceParam": "age",
        "displayName": "年龄",
        "type": "variable",
        "field": "S_N_AGE",
        "paasDown": false,
        "order": 0,
        "includeCheck": {
          "checkType": 1
        },
        "checked": false,
        "dataType": 2,
        "defaultValue": "default_age"
      },
      {
        "serviceParam": "code",
        "displayName": "指标结果",
        "type": "variable",
        "field": "S_S_METRICRESULT",
        "paasDown": false,
        "order": 1,
        "includeCheck": {
          "checkType": 1
        },
        "checked": false,
        "dataType": 1,
        "defaultValue": "default_code"
      },
      {
        "serviceParam": "responseCode",
        "displayName": "[系统]三方返回状态码",
        "paasDown": false,
        "checked": false
      }
    ]
  }
}