module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "channelServiceName": "necoSalaxySceneList",
      "channelServiceDisplayName": "necoSalaxySceneList",
      "async": false
    },
    {
      "channelServiceName": "necoBifrostI18n",
      "channelServiceDisplayName": "necoBifrostI18n",
      "async": false
    },
    {
      "channelServiceName": "necoBifrostOk",
      "channelServiceDisplayName": "necoBifrostOk",
      "async": false
    },
    {
      "channelServiceName": "necoXml",
      "channelServiceDisplayName": "necoXml",
      "documentTypeUuid": "2db2facd984f42ceabeeefeb4f0b9e44",
      "async": false
    },
    {
      "channelServiceName": "BYH",
      "channelServiceDisplayName": "BYH",
      "async": false
    },
    {
      "channelServiceName": "bugfix051601",
      "channelServiceDisplayName": "webservice接口异常测试",
      "async": false
    },
    {
      "channelServiceName": "NBATEST",
      "channelServiceDisplayName": "NBATEST",
      "async": false
    },
    {
      "channelServiceName": "testFeatureDy",
      "channelServiceDisplayName": "测试外数指标dy",
      "documentTypeUuid": "318e0c112f7747dab7f740f0af3d445f",
      "async": false
    },
    {
      "channelServiceName": "cc_cp",
      "channelServiceDisplayName": "cc_cp",
      "async": false
    },
    {
      "channelServiceName": "bugfix01",
      "channelServiceDisplayName": "异步接口异常问题复现",
      "async": true
    },
    {
      "channelServiceName": "UserService",
      "channelServiceDisplayName": "UserService",
      "async": false
    },
    {
      "channelServiceName": "45678",
      "channelServiceDisplayName": "真实接口限流",
      "documentTypeUuid": "2db2facd984f42ceabeeefeb4f0b9e44",
      "async": false
    },
    {
      "channelServiceName": "queryUser",
      "channelServiceDisplayName": "queryUser",
      "async": true
    },
    {
      "channelServiceName": "sctest3",
      "channelServiceDisplayName": "测试缓存1小时同步",
      "async": false
    },
    {
      "channelServiceName": "sctest4",
      "channelServiceDisplayName": "测试缓存当天异步",
      "async": true
    },
    {
      "channelServiceName": "bugfix051502",
      "channelServiceDisplayName": "测试051501",
      "async": false
    },
    {
      "channelServiceName": "ccc_test",
      "channelServiceDisplayName": "ccc_test",
      "async": false
    },
    {
      "channelServiceName": "12345",
      "channelServiceDisplayName": "mock接口不限流",
      "async": false
    },
    {
      "channelServiceName": "23456",
      "channelServiceDisplayName": "真实接口不限流",
      "documentTypeUuid": "2db2facd984f42ceabeeefeb4f0b9e44",
      "async": false
    },
    {
      "channelServiceName": "TDZXF001",
      "channelServiceDisplayName": "同盾智信分_演示接口_直走接口",
      "async": false
    },
    {
      "channelServiceName": "34567",
      "channelServiceDisplayName": "mock接口限流",
      "async": false
    },
    {
      "channelServiceName": "wyTest",
      "channelServiceDisplayName": "wyTest",
      "async": false
    }
  ]
}