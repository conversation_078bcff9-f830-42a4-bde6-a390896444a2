module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "default": [
      {
        "iconText": "开始",
        "name": "开始",
        "iconColor": "#9E6FBF",
        "type": "StartFlowNode"
      },
      {
        "iconText": "结束",
        "name": "结束",
        "iconColor": "#9E6FBF",
        "type": "EndFlowNode"
      },
      {
        "iconText": "判断",
        "name": "判断",
        "iconColor": "#9E6FBF",
        "type": "ExclusiveGateway"
      },
      {
        "iconText": "并行",
        "name": "并行",
        "iconColor": "#9E6FBF",
        "type": "ParallelGateway"
      },
      {
        "iconText": "接",
        "name": "API接口",
        "iconColor": "#CFAB67",
        "type": "FeatureServiceNode"
      },
      {
        "iconText": "路",
        "name": "智能路由",
        "iconColor": "#003366",
        "type": "RouteServiceNode"
      },
      {
        "iconText": "继",
        "name": "继续补充",
        "iconColor": "#9367c6",
        "type": "SuspendFlowNode"
      },
      {
        "iconText": "模",
        "name": "流程模版",
        "iconColor": "#628fe4",
        "type": "SubDecisionFlowNode"
      },
      {
        "iconText": "函",
        "name": "脚本函数",
        "iconColor": "#238E23",
        "type": "FunctionServiceNode"
      },
      {
        "iconText": "表",
        "name": "数据表",
        "iconColor": "#3366FF",
        "type": "DataTableServiceNode"
      }
    ],
    "child": [
      {
        "iconText": "开始",
        "name": "开始",
        "iconColor": "#9E6FBF",
        "type": "StartFlowNode"
      },
      {
        "iconText": "结束",
        "name": "结束",
        "iconColor": "#9E6FBF",
        "type": "EndFlowNode"
      },
      {
        "iconText": "判断",
        "name": "判断",
        "iconColor": "#9E6FBF",
        "type": "ExclusiveGateway"
      },
      {
        "iconText": "并行",
        "name": "并行",
        "iconColor": "#9E6FBF",
        "type": "ParallelGateway"
      },
      {
        "iconText": "路",
        "name": "智能路由",
        "iconColor": "#003366",
        "type": "RouteServiceNode"
      },
      {
        "iconText": "接",
        "name": "API接口",
        "iconColor": "#CFAB67",
        "type": "FeatureServiceNode"
      },
      {
        "iconText": "继",
        "name": "继续补充",
        "iconColor": "#9367c6",
        "type": "SuspendFlowNode"
      },
      {
        "iconText": "函",
        "name": "脚本函数",
        "iconColor": "#238E23",
        "type": "FunctionServiceNode"
      },
      {
        "iconText": "表",
        "name": "数据表",
        "iconColor": "#3366FF",
        "type": "DataTableServiceNode"
      }
    ]
  }
}