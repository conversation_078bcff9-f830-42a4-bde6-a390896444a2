module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "total": 2,
    "curPage": 1,
    "pageSize": 10,
    "pages": 1,
    "size": 2,
    "contents": [
      {
        "orgCode": "testorg7",
        "appCode": "init",
        "canWriter": true,
        "creator": "sc4(sc4)",
        "operator": "byh(byh)",
        "gmtCreate": "2024-12-12 19:35:22",
        "gmtModify": "2024-12-13 18:02:00",
        "uuid": "e78967413ad14c94bb6b5d4faf3c0392",
        "code": "sctest123456",
        "displayName": "测试授权123456"
      },
      {
        "orgCode": "TongDun",
        "appCode": "init",
        "canWriter": true,
        "creator": "chen.shen(申晨)",
        "operator": "byh(byh)",
        "gmtCreate": "2024-10-29 18:50:55",
        "gmtModify": "2024-12-16 10:47:01",
        "uuid": "736c95462a9d4166b8790b1d290906ba",
        "code": "sctest1",
        "displayName": "测试mysql数据表模版"
      }
    ]
  }
}