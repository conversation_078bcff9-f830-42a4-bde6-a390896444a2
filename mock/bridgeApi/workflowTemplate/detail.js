module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "orgCode": "TongDun",
    "appCode": "init",
    "canWriter": true,
    "creator": "chen.shen(chen.shen)",
    "operator": "byh(byh)",
    "gmtCreate": "2024-10-29 18:50:55",
    "gmtModify": "2024-12-16 10:47:01",
    "uuid": "736c95462a9d4166b8790b1d290906ba",
    "code": "sctest1",
    "displayName": "测试mysql数据表模版",
    "graphJson": "{\"flowNodeDefinitions\":[{\"x\":191,\"y\":208,\"id\":\"aeaa83e095e311efa7343997b834dae7\",\"name\":\"开始\",\"nodeType\":\"StartFlowNode\",\"attributes\":{},\"incomingFields\":[],\"outgoingFields\":[]},{\"x\":396,\"y\":215,\"id\":\"afe6112095e311efa7343997b834dae7\",\"name\":\"87687\",\"nodeType\":\"DataTableServiceNode\",\"attributes\":{\"limitNum\":\"5\",\"outTableType\":2,\"dataSourceId\":114,\"dataSourceType\":\"mysql\",\"showLog\":true,\"type\":\"sqlSet\",\"messages\":[],\"resData\":[],\"logColumns\":[{\"title\":\"date\",\"dataIndex\":\"date\",\"key\":\"date\"},{\"title\":\"name\",\"dataIndex\":\"name\",\"key\":\"name\"},{\"title\":\"id\",\"dataIndex\":\"id\",\"key\":\"id\"},{\"title\":\"age\",\"dataIndex\":\"age\",\"key\":\"age\"}],\"testResult\":true,\"sqlParams\":[{\"type\":\"variable\",\"name\":\"S_N_WEEKLYTIME\",\"value\":\"10\"},{\"type\":\"date\",\"name\":\"b\",\"value\":\"$[yyyy-MM-dd-1d]\"}],\"nodeName\":\"87687\",\"sqlContent\":\"select * from test_dy.for_test_2 a inner join test_dy.for_test b where a.date < '${b}' \\nand b.id < ${S_N_WEEKLYTIME}\",\"previewTables\":\"\",\"contextField\":{}},\"incomingFields\":[],\"outgoingFields\":[{\"fieldName\":\"S_S_CUSTCRTDAT\",\"mappingName\":\"dataset_json\"}]},{\"x\":751,\"y\":205,\"id\":\"b18e910095e311efa7343997b834dae7\",\"name\":\"结束\",\"nodeType\":\"EndFlowNode\",\"attributes\":{},\"incomingFields\":[],\"outgoingFields\":[]},{\"x\":389,\"y\":311,\"id\":\"9198fe00b2f111efb57295e89acd035a\",\"name\":\"真实接口限流\",\"nodeType\":\"FeatureServiceNode\",\"attributes\":{\"thirdServiceCode\":\"45678\",\"featureCodes\":[{\"dName\":\"necoRRHReportCustIndex\",\"name\":\"necoRRHReportCustIndex\"}],\"thirdServiceName\":\"真实接口限流\",\"exceptionTerminate\":true,\"async\":false,\"originInput\":{\"abc\":\"S_S_CUSTCRTDAT\"},\"originOutput\":{\"age\":\"S_N_AGE\",\"code\":\"S_S_METRICRESULT\"}},\"incomingFields\":[{\"displayName\":\"客户开立日期\",\"fieldName\":\"S_S_CUSTCRTDAT\",\"mappingName\":\"abc\"}],\"outgoingFields\":[{\"displayName\":\"年龄\",\"fieldName\":\"S_N_AGE\",\"mappingName\":\"age\"},{\"displayName\":\"指标结果\",\"fieldName\":\"S_S_METRICRESULT\",\"mappingName\":\"code\"},{\"displayName\":\"[系统]三方返回状态码\",\"fieldName\":\"S_S_THIRDRESCODE\",\"mappingName\":\"responseCode\"}]}],\"flowLineDefinitions\":[{\"id\":\"b31b4b80-95e3-11ef-a734-3997b834dae7\",\"fromPoint\":1,\"toPoint\":\"3\",\"attributes\":{},\"sourceNodeId\":\"aeaa83e095e311efa7343997b834dae7\",\"targetNodeId\":\"afe6112095e311efa7343997b834dae7\"},{\"id\":\"95c828c0-b2f1-11ef-b572-95e89acd035a\",\"fromPoint\":2,\"toPoint\":\"0\",\"attributes\":{},\"sourceNodeId\":\"afe6112095e311efa7343997b834dae7\",\"targetNodeId\":\"9198fe00b2f111efb57295e89acd035a\"},{\"id\":\"99567fa0-b2f1-11ef-b572-95e89acd035a\",\"fromPoint\":1,\"toPoint\":\"3\",\"attributes\":{},\"sourceNodeId\":\"9198fe00b2f111efb57295e89acd035a\",\"targetNodeId\":\"b18e910095e311efa7343997b834dae7\"}]}"
  }
}