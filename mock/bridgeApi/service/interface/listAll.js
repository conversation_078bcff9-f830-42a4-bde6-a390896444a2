module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": [
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "csb(csb)",
      "operator": "csb(csb)",
      "gmtCreate": "2024-12-17 13:37:04",
      "gmtModify": "2024-12-17 13:37:10",
      "uuid": "6114396fcc9f40df9d75e5b60d7a071f",
      "name": "741931",
      "displayName": "测试数据比哦啊",
      "workflowName": "sctest012345_copy",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"field\":\"S_S_SERIALNUM\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"业务流水号\"},{\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"field\":\"S_S_ORGCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"机构标识\"},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"渠道标识\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"field\":\"C_F_PFK731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\"},{\"dataType\":2,\"displayName\":\"每周限笔\",\"field\":\"S_N_WEEKLYTIME\",\"mustInput\":false,\"serviceParam\":\"weeklytime\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"dataType\":1,\"displayName\":\"客户开立日期\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"name\":\"S_S_CUSTCRTDAT\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\",\"name\":\"C_F_PFK731\"},{\"dataType\":1,\"displayName\":\"测试新增系统字段\",\"mustInput\":false,\"serviceParam\":\"test\",\"type\":\"variable\",\"name\":\"C_S_TEST\"},{\"dataType\":1,\"displayName\":\"三方返回状态码\",\"mustInput\":false,\"serviceParam\":\"thirdrescode\",\"type\":\"variable\",\"name\":\"S_S_THIRDRESCODE\"},{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\"},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\"}]",
      "secretKey": "Db9Fhjky2RTSCsjTfHbbKU9EoWCzc21CBLDvHLg3",
      "encryption": "NONE"
    },
    {
      "orgCode": "testorg6",
      "appCode": "test_add",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "chen.shen(申晨)",
      "gmtCreate": "2024-12-13 14:31:00",
      "gmtModify": "2024-12-18 23:10:53",
      "uuid": "b2c88d0d19994e1cb8e2ff06de1f22bf",
      "name": "12345678",
      "displayName": "1234567",
      "workflowName": "sctest012345_copy",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"field\":\"S_S_SERIALNUM\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"业务流水号\"},{\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"field\":\"S_S_ORGCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"机构标识\"},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"渠道标识\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"field\":\"C_F_PFK731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\"},{\"dataType\":2,\"displayName\":\"每周限笔\",\"field\":\"S_N_WEEKLYTIME\",\"mustInput\":false,\"serviceParam\":\"weeklytime\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"dataType\":1,\"displayName\":\"客户开立日期\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"name\":\"S_S_CUSTCRTDAT\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\",\"name\":\"C_F_PFK731\"},{\"dataType\":1,\"displayName\":\"测试新增系统字段\",\"mustInput\":false,\"serviceParam\":\"test\",\"type\":\"variable\",\"name\":\"C_S_TEST\"},{\"dataType\":1,\"displayName\":\"三方返回状态码\",\"mustInput\":false,\"serviceParam\":\"thirdrescode\",\"type\":\"variable\",\"name\":\"S_S_THIRDRESCODE\"},{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\"},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\"}]",
      "secretKey": "dsfMPyZDmHtLzzTUuToHoJk4ePu1EayrKT42ar9z",
      "encryption": "NONE"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "chen.shen(申晨)",
      "gmtCreate": "2024-12-11 16:24:44",
      "gmtModify": "2024-12-16 23:33:55",
      "uuid": "505f8d4e179d4862a228ee995689a4f6",
      "name": "sctest00009",
      "displayName": "测试服务接口授权",
      "workflowName": "sctest234",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"field\":\"S_S_SERIALNUM\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"业务流水号\"},{\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"field\":\"S_S_ORGCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"机构标识\"},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"渠道标识\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"工作单位名称\",\"field\":\"S_S_WORKUNIT\",\"mustInput\":false,\"serviceParam\":\"workunit\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"dataType\":1,\"displayName\":\"客户开立日期\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"name\":\"S_S_CUSTCRTDAT\"},{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\"},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\"}]",
      "secretKey": "7tCcn2ZoWeOTiGCVJ5NlOY8hTLGZxBvPxNoiDaZx",
      "encryption": "NONE"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "chen.shen(申晨)",
      "gmtCreate": "2024-12-10 17:23:27",
      "gmtModify": "2024-12-12 10:45:38",
      "uuid": "ed00addfaff44e07bf18998a3172f9b0",
      "name": "sctest013456",
      "displayName": "测试全组件",
      "workflowName": "sctest012345",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"渠道标识\",\"field\":\"S_S_APPCODE\",\"mustInput\":true,\"serviceParam\":\"appcode\",\"type\":\"variable\",\"key\":2},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":3},{\"dataType\":1,\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"key\":4},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"field\":\"C_F_PFK731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\",\"key\":5},{\"dataType\":2,\"displayName\":\"每周限笔\",\"field\":\"S_N_WEEKLYTIME\",\"mustInput\":false,\"serviceParam\":\"weeklytime\",\"type\":\"variable\",\"key\":6},{\"serviceParam\":\"dynamictablename\",\"type\":\"variable\",\"field\":\"S_S_DYNAMICTABLENAME\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":7,\"displayName\":\"动态表名\"}]",
      "outputConfig": "[{\"dataType\":1,\"displayName\":\"客户开立日期\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"name\":\"S_S_CUSTCRTDAT\"},{\"dataType\":1,\"displayName\":\"微信子商户号\",\"mustInput\":false,\"serviceParam\":\"mpsubid\",\"type\":\"variable\",\"name\":\"S_S_MPSUBID\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\",\"name\":\"C_F_PFK731\"},{\"dataType\":1,\"displayName\":\"测试新增系统字段\",\"mustInput\":false,\"serviceParam\":\"test\",\"type\":\"variable\",\"name\":\"C_S_TEST\"},{\"dataType\":1,\"displayName\":\"三方返回状态码\",\"mustInput\":false,\"serviceParam\":\"thirdrescode\",\"type\":\"variable\",\"name\":\"S_S_THIRDRESCODE\"},{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\"},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\"}]",
      "secretKey": "onX1SH1aYKUIVNaf64tEONv78t3d5QdBO6yMFVZR",
      "encryption": "SE",
      "encryptionType": "SM4"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "fujun.cai(蔡福君)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-12-09 16:30:00",
      "gmtModify": "2024-12-09 16:30:16",
      "uuid": "8964673f395a45eaa19ebff72a2b97f6",
      "name": "asyncTest",
      "displayName": "异步test",
      "workflowName": "asyncTest",
      "contentType": "application/json",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"field\":\"S_S_SERIALNUM\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"业务流水号\"},{\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"field\":\"S_S_ORGCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"机构标识\"},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"渠道标识\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\"},{\"dataType\":1,\"displayName\":\"客户有效状态\",\"mustInput\":false,\"serviceParam\":\"custsts\",\"type\":\"variable\",\"name\":\"S_S_CUSTSTS\"},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\"}]",
      "secretKey": "PADvy5KKEq82VS7bTw3bMvItaMkz7H97cT44PoRi",
      "encryption": "NONE"
    },
    {
      "orgCode": "TongDun",
      "appCode": "necoTestApp",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "csb(csb)",
      "gmtCreate": "2024-12-06 16:40:38",
      "gmtModify": "2024-12-06 17:40:10",
      "uuid": "e26027caf664425090e217fce5fcdcba",
      "name": "sctest001",
      "displayName": "sctest001",
      "workflowName": "dstianzuo",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"渠道标识\",\"field\":\"S_S_APPCODE\",\"mustInput\":true,\"serviceParam\":\"appcode\",\"type\":\"variable\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"serviceParam\":\"cont4name\",\"name\":\"S_S_CONT4NAME\",\"displayName\":\"第四联系人姓名\",\"dataType\":1,\"modelFieldType\":4,\"key\":0}]",
      "secretKey": "Hmolmms7KNOCu6LszAMDws9dkmvMdXis5piIYAf8",
      "ipBlackList": "**********",
      "ipWhiteList": "**********",
      "encryption": "NONE"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "csb(csb)",
      "operator": "chen.shen(申晨)",
      "gmtCreate": "2024-12-06 10:31:17",
      "gmtModify": "2024-12-11 15:56:47",
      "uuid": "0ccecb2b6d3f468386b40fface592c1e",
      "name": "1231",
      "displayName": "测试",
      "workflowName": "sctest12041",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"field\":\"S_S_SERIALNUM\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"业务流水号\"},{\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"field\":\"S_S_ORGCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"机构标识\"},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"displayName\":\"渠道标识\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"field\":\"C_F_PFK731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"dataType\":1,\"displayName\":\"三方返回状态码\",\"mustInput\":false,\"serviceParam\":\"thirdrescode\",\"type\":\"variable\",\"name\":\"S_S_THIRDRESCODE\"},{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\"},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\"}]",
      "secretKey": "sejqcldBERXgVGSbC9zCJjWLteTRGyaQOFKAl8H9",
      "ipBlackList": "************",
      "ipWhiteList": "************;************-************",
      "encryption": "NONE"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "chen.shen(申晨)",
      "gmtCreate": "2024-12-05 20:00:05",
      "gmtModify": "2024-12-19 14:37:45",
      "uuid": "5e09c86fa3994a55a7dccbe8cedfe2c2",
      "name": "sctest0234",
      "displayName": "测试234",
      "workflowName": "sctest12041",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 2,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"渠道标识\",\"field\":\"S_S_APPCODE\",\"mustInput\":true,\"serviceParam\":\"appcode\",\"type\":\"variable\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"field\":\"C_F_PFK731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"dataType\":1,\"displayName\":\"三方返回状态码\",\"mustInput\":false,\"serviceParam\":\"thirdrescode\",\"type\":\"variable\",\"name\":\"S_S_THIRDRESCODE\",\"key\":0},{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\",\"key\":1},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\",\"key\":2},{\"serviceParam\":\"test\",\"name\":\"C_S_TEST\",\"displayName\":\"测试新增系统字段\",\"dataType\":1,\"modelFieldType\":4,\"key\":3},{\"serviceParam\":\"cont4name\",\"name\":\"S_S_CONT4NAME\",\"displayName\":\"第四联系人姓名\",\"dataType\":1,\"modelFieldType\":4,\"key\":4}]",
      "secretKey": "tDH8ksBZVL0XE4BFOz0JT582L8FF28o6hOBOGkZt",
      "encryption": "SE",
      "encryptionType": "SM4"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "chen.shen(申晨)",
      "gmtCreate": "2024-12-04 16:59:45",
      "gmtModify": "2024-12-06 16:51:29",
      "uuid": "16835d5fc8ff45ec9604e046f79af19c",
      "name": "sctest0123",
      "displayName": "测试123",
      "workflowName": "sctest12041",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"渠道标识\",\"field\":\"S_S_APPCODE\",\"mustInput\":true,\"serviceParam\":\"appcode\",\"type\":\"variable\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"策略运行节点详情\",\"field\":\"C_S_NODERESULTLIST\",\"mustInput\":false,\"serviceParam\":\"noderesultlist\",\"type\":\"variable\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"field\":\"C_F_PFK731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"dataType\":1,\"displayName\":\"三方返回状态码\",\"mustInput\":false,\"serviceParam\":\"thirdrescode\",\"type\":\"variable\",\"name\":\"S_S_THIRDRESCODE\"},{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\"},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\"}]",
      "secretKey": "nemv7rUDLU4UdSYyBYa8KAzY2EPtsVRcYzqRRyLl",
      "encryption": "NONE"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "admin(administrator)",
      "operator": "admin(administrator)",
      "gmtCreate": "2024-12-03 10:55:17",
      "gmtModify": "2024-12-03 10:56:49",
      "uuid": "7e39acae9b884e6b8db30c210abcd34f",
      "name": "222",
      "displayName": "测试服务接口222",
      "workflowName": "sctest01",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"渠道标识\",\"field\":\"S_S_APPCODE\",\"mustInput\":true,\"serviceParam\":\"appcode\",\"type\":\"variable\",\"key\":2},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"weeklytime\",\"type\":\"variable\",\"field\":\"S_N_WEEKLYTIME\",\"dataType\":2,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"每周限笔\"},{\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"field\":\"S_S_CUSTCRTDAT\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":5,\"displayName\":\"客户开立日期\"}]",
      "outputConfig": "[{\"serviceParam\":\"appcode\",\"name\":\"S_S_APPCODE\",\"displayName\":\"渠道标识\",\"dataType\":1,\"modelFieldType\":4,\"key\":0}]",
      "secretKey": "QrZve6kPIY1eihdE",
      "encryption": "NONE"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-10-31 14:33:03",
      "gmtModify": "2024-12-19 14:40:43",
      "uuid": "260710fce5ee4edaa29c741e015056bd",
      "name": "sctestyc8",
      "displayName": "20个真实接口限流",
      "workflowName": "sctestyc8",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"渠道标识\",\"field\":\"S_S_APPCODE\",\"mustInput\":false,\"serviceParam\":\"appcode\",\"type\":\"variable\"}]",
      "outputConfig": "[{\"serviceParam\":\"age\",\"name\":\"S_N_AGE\",\"displayName\":\"年龄\",\"dataType\":2,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\",\"dataType\":1,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"displayName\":\"三方返回状态码\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-10-31 14:32:02",
      "gmtModify": "2024-12-19 14:40:18",
      "uuid": "f06b02e96fd644589f49a6b92760296e",
      "name": "sctestyc7",
      "displayName": "20个mock接口限流",
      "workflowName": "sctestyc7",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"渠道标识\"}]",
      "outputConfig": "[{\"serviceParam\":\"age\",\"name\":\"S_N_AGE\",\"displayName\":\"年龄\",\"dataType\":2,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\",\"dataType\":1,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"displayName\":\"三方返回状态码\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-10-31 14:31:09",
      "gmtModify": "2024-12-19 14:39:54",
      "uuid": "86c5a3f931214317a889b6217f311356",
      "name": "sctestyc6",
      "displayName": "单个真实接口限流",
      "workflowName": "sctestyc6",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"渠道标识\"}]",
      "outputConfig": "[{\"serviceParam\":\"age\",\"name\":\"S_N_AGE\",\"displayName\":\"年龄\",\"dataType\":2,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\",\"dataType\":1,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"displayName\":\"三方返回状态码\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-10-31 14:30:12",
      "gmtModify": "2024-12-19 14:39:40",
      "uuid": "03aeba4d65c047dc89744750819701b6",
      "name": "sctestyc5",
      "displayName": "单个mock接口限流",
      "workflowName": "sctestyc5",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"渠道标识\"}]",
      "outputConfig": "[{\"serviceParam\":\"age\",\"name\":\"S_N_AGE\",\"displayName\":\"年龄\",\"dataType\":2,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\",\"dataType\":1,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"displayName\":\"三方返回状态码\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-10-31 14:28:55",
      "gmtModify": "2024-12-19 14:39:21",
      "uuid": "01138ba9df27421d8d74681e1ec33b61",
      "name": "sctestyc4",
      "displayName": "20个真实接口不限流",
      "workflowName": "sctestyc4",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"渠道标识\"}]",
      "outputConfig": "[{\"serviceParam\":\"age\",\"name\":\"S_N_AGE\",\"displayName\":\"年龄\",\"dataType\":2,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\",\"dataType\":1,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"displayName\":\"三方返回状态码\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-10-31 14:27:43",
      "gmtModify": "2024-12-19 14:39:03",
      "uuid": "75eb5e7fcd1d40e4aadcca77762143c4",
      "name": "sctestyc3",
      "displayName": "20个mock接口不限流",
      "workflowName": "sctestyc3",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"渠道标识\"}]",
      "outputConfig": "[{\"serviceParam\":\"age\",\"name\":\"S_N_AGE\",\"displayName\":\"年龄\",\"dataType\":2,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\",\"dataType\":1,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"displayName\":\"三方返回状态码\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-10-31 14:26:10",
      "gmtModify": "2024-12-19 14:38:06",
      "uuid": "61491fabbd27420bab72ade52dab93fa",
      "name": "sctestyc2",
      "displayName": "单个真实接口不限流",
      "workflowName": "sctestyc2",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"渠道标识\"}]",
      "outputConfig": "[{\"serviceParam\":\"age\",\"name\":\"S_N_AGE\",\"displayName\":\"年龄\",\"dataType\":2,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"displayName\":\"指标结果\",\"dataType\":1,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"displayName\":\"三方返回状态码\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "fujun.cai(蔡福君)",
      "gmtCreate": "2024-10-31 12:29:05",
      "gmtModify": "2024-12-19 14:36:10",
      "uuid": "0777c02c1a4147eb8d9976e3b7fecf45",
      "name": "sctestyc1",
      "displayName": "单个mock接口不限流",
      "workflowName": "sctestyc1",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"appcode\",\"type\":\"variable\",\"field\":\"S_S_APPCODE\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"渠道标识\"}]",
      "outputConfig": "[{\"serviceParam\":\"age\",\"name\":\"S_N_AGE\",\"dataType\":2,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"dataType\":1,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "chen.shen(申晨)",
      "operator": "chen.shen(申晨)",
      "gmtCreate": "2024-10-29 18:46:43",
      "gmtModify": "2024-11-19 17:43:32",
      "uuid": "aafb6e8427264fe2ae724276836a3817",
      "name": "sctest1",
      "displayName": "测试mysql数据表",
      "workflowName": "sctest01",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"dataType\":2,\"defaultValue\":\"\",\"displayName\":\"每周限笔\",\"field\":\"S_N_WEEKLYTIME\",\"mustInput\":false,\"serviceParam\":\"weeklytime\",\"type\":\"variable\",\"key\":3},{\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"field\":\"S_S_CUSTCRTDAT\",\"dataType\":1,\"mustInput\":false,\"defaultValue\":\"\",\"key\":4,\"displayName\":\"客户开立日期\"}]",
      "outputConfig": "[{\"serviceParam\":\"custcrtdat\",\"name\":\"S_S_CUSTCRTDAT\",\"dataType\":1,\"modelFieldType\":4,\"key\":0},{\"serviceParam\":\"weeklytime\",\"name\":\"S_N_WEEKLYTIME\",\"dataType\":2,\"modelFieldType\":4,\"key\":1},{\"serviceParam\":\"metricresult\",\"name\":\"S_S_METRICRESULT\",\"dataType\":1,\"modelFieldType\":4,\"key\":2}]"
    },
    {
      "orgCode": "TongDun",
      "appCode": "init",
      "canWriter": true,
      "creator": "yujiang.ye.mock(yujiang.ye.mock)",
      "operator": "yujiang.ye.mock(yujiang.ye.mock)",
      "gmtCreate": "2024-07-16 20:39:51",
      "gmtModify": "2024-07-16 20:50:41",
      "uuid": "8aeefa02b97e4b77ad270047db42b8b5",
      "name": "necoWorkFlowService",
      "displayName": "necoWorkFlowService",
      "workflowName": "necoWorkFlow",
      "contentType": "application/x-www-form-urlencoded",
      "appType": "tianzuo-api",
      "serviceType": 1,
      "status": 1,
      "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\",\"key\":0},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\",\"key\":1},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\",\"key\":2},{\"serviceParam\":\"workunit\",\"type\":\"variable\",\"field\":\"S_S_WORKUNIT\",\"dataType\":1,\"mustInput\":true,\"defaultValue\":\"\",\"key\":3,\"displayName\":\"工作单位名称\"}]",
      "outputConfig": "[{\"serviceParam\":\"thirdrescode\",\"name\":\"S_S_THIRDRESCODE\",\"dataType\":1,\"modelFieldType\":4,\"key\":0}]"
    }
  ]
}