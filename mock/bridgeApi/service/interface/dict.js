module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "encryptionList": [
      {
        "value": "NONE",
        "label": "无"
      },
      {
        "value": "SE",
        "label": "对称加密",
        "children": [
          {
            "value": "SM4",
            "label": "SM4"
          },
          {
            "value": "AES",
            "label": "AES"
          },
          {
            "value": "DES",
            "label": "DES"
          }
        ]
      },
      {
        "value": "AE",
        "label": "非对称加密",
        "children": [
          {
            "value": "SM2",
            "label": "SM2"
          },
          {
            "value": "RSA",
            "label": "RSA"
          }
        ]
      }
    ],
    "bizModelEnum": [
      {
        "displayName": "正式",
        "name": 1
      },
      {
        "displayName": "测试",
        "name": 2
      },
      {
        "displayName": "调用方定时调用",
        "name": 6
      }
    ],
    "riskInvokeStatus": [
      {
        "displayName": "待运行",
        "name": 0
      },
      {
        "displayName": "执行成功",
        "name": 1
      },
      {
        "displayName": "暂停补充",
        "name": 2
      },
      {
        "displayName": "失败可重试",
        "name": 3
      },
      {
        "displayName": "失败不可重试",
        "name": 4
      }
    ],
    "appTypeList": [
      {
        "displayName": "API编排",
        "name": "tianzuo-api"
      }
    ],
    "interfaceContentTypeList": [
      "X_WWW_FORM_URLENCODED",
      "APPLICATION_JSON",
      "APPLICATION_XML",
      "MULTIPART_FORM_DATA"
    ]
  }
}