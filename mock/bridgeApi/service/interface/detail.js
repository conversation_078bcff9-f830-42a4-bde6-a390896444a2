module.exports = {
  "success": true,
  "code": 200,
  "message": "处理成功",
  "data": {
    "orgCode": "TongDun",
    "appCode": "init",
    "canWriter": true,
    "creator": "csb(csb)",
    "operator": "csb(csb)",
    "gmtCreate": "2024-12-17 13:37:04",
    "gmtModify": "2024-12-17 13:37:10",
    "uuid": "6114396fcc9f40df9d75e5b60d7a071f",
    "name": "741931",
    "displayName": "测试数据比哦啊",
    "workflowName": "sctest012345_copy",
    "contentType": "application/x-www-form-urlencoded",
    "appType": "tianzuo-api",
    "serviceType": 1,
    "status": 1,
    "inputConfig": "[{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"业务方流水号\",\"field\":\"S_S_SERIALNUM\",\"mustInput\":true,\"serviceParam\":\"serialnum\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"机构编号\",\"field\":\"S_S_ORGCODE\",\"mustInput\":true,\"serviceParam\":\"orgcode\",\"type\":\"variable\"},{\"dataType\":1,\"defaultValue\":\"\",\"displayName\":\"渠道标识\",\"field\":\"S_S_APPCODE\",\"mustInput\":true,\"serviceParam\":\"appcode\",\"type\":\"variable\"},{\"dataType\":5,\"defaultValue\":\"\",\"displayName\":\"运行模式\",\"extend\":[{\"description\":\"同步\",\"value\":\"1\"},{\"description\":\"异步\",\"value\":\"2\"}],\"field\":\"S_E_RUNTYPE\",\"mustInput\":true,\"serviceParam\":\"runtype\",\"type\":\"variable\"},{\"dataType\":1,\"displayName\":\"客户开立日期\",\"field\":\"S_S_CUSTCRTDAT\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"field\":\"C_F_PFK731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\"},{\"dataType\":2,\"displayName\":\"每周限笔\",\"field\":\"S_N_WEEKLYTIME\",\"mustInput\":false,\"serviceParam\":\"weeklytime\",\"type\":\"variable\"}]",
    "outputConfig": "[{\"dataType\":1,\"displayName\":\"客户开立日期\",\"mustInput\":false,\"serviceParam\":\"custcrtdat\",\"type\":\"variable\",\"name\":\"S_S_CUSTCRTDAT\"},{\"dataType\":3,\"displayName\":\"评分卡输出字段731\",\"mustInput\":false,\"serviceParam\":\"pfk731\",\"type\":\"variable\",\"name\":\"C_F_PFK731\"},{\"dataType\":1,\"displayName\":\"测试新增系统字段\",\"mustInput\":false,\"serviceParam\":\"test\",\"type\":\"variable\",\"name\":\"C_S_TEST\"},{\"dataType\":1,\"displayName\":\"三方返回状态码\",\"mustInput\":false,\"serviceParam\":\"thirdrescode\",\"type\":\"variable\",\"name\":\"S_S_THIRDRESCODE\"},{\"dataType\":2,\"displayName\":\"年龄\",\"mustInput\":false,\"serviceParam\":\"age\",\"type\":\"variable\",\"name\":\"S_N_AGE\"},{\"dataType\":1,\"displayName\":\"指标结果\",\"mustInput\":false,\"serviceParam\":\"metricresult\",\"type\":\"variable\",\"name\":\"S_S_METRICRESULT\"}]",
    "secretKey": "Db9Fhjky2RTSCsjTfHbbKU9EoWCzc21CBLDvHLg3",
    "encryption": "NONE",
    "mockTips": {
      "45678": "真实接口限流"
    }
  }
}