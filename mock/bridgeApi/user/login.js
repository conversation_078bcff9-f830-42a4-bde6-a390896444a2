module.exports = {
  "code": 200,
  "message": "成功",
  "success": true,
  "data": {
    "tdToken": "eyJhbGciOiJIUzI1NiJ9.eyJqdGkiOiI4MzdDNzJDMzhBRUZGMEU5RDcwMUExNUYyNTk2M0FCNiIsInN1YiI6ImJ5aCIsImlzcyI6ImZyYXVkbWV0cml4IiwiaWF0IjoxNzM1Mjg5NzU5LCJleHAiOjE3MzU4OTQ1NTl9.82OcqvzaLprwgB9rRIR_D_6rSqKL0gSKHQxztXdj-ZY",
    "needInit": false,
    "csrfToken": "FxSpQLovlSRWNJwtsI1TgrrxV6PxOAKLrdSfg2v9UMFzUZtd6y5nBbNkIp9XbPAUbIi3shcr4tPgJG2Uc5dJT2nTXuWN6X0caOrdpNOkXh0NFMtDjsv2FpmyX2oFlquYuSRGIon3h7rMPsy24IUcqBIB2sX3JNQ/pQnHwjgo+Yo=",
    "expiration": *********,
    "indexPath": "/handle/dashboard/dataService",
    "userId": "a483313fcceb4f47961931f02f03fd8c",
    "user": {
      "id": 23,
      "uuid": "a483313fcceb4f47961931f02f03fd8c",
      "gmtCreate": *************,
      "gmtModified": *************,
      "account": "byh",
      "userName": "byh",
      "orgUuid": "a8202aea546f48979754bdd45c471b08",
      "roleUuids": "[\"ee8dbc99831b4a9cb17578b51bbb09e0\",\"cfb9c519fde64476bc6ca01ba324a6a6\"]",
      "roleUuidsSelected": "[\"ee8dbc99831b4a9cb17578b51bbb09e0\",\"cfb9c519fde64476bc6ca01ba324a6a6\"]",
      "avatar": "male1",
      "expiration": *************,
      "gender": 0,
      "appName": "[\"test_add\",\"bug404503\",\"init\",\"necoTestApp\",\"JYTest\"]",
      "status": 0,
      "updateBy": "fujun.cai",
      "lang": "cn",
      "theme": "default",
      "layout": "default",
      "simplified": 1,
      "tokenMD5": "867177068FFE74212636097B47648474",
      "verificationCode": "",
      "tryTime": 0,
      "tryDate": *************,
      "updatePwdTime": *************,
      "firstLogin": "1",
      "isDelete": 0,
      "portalInfo": "[]"
    }
  }
}