module.exports = {
  "code": 200,
  "message": "成功",
  "success": true,
  "data": [
    {
      "id": 2,
      "uuid": "6fba24597d814b74bcfff2513ad0aa05",
      "gmtCreate": 1712900681000,
      "gmtModified": 1722413332000,
      "name": "test_add",
      "displayName": "测试新增渠道",
      "partnerCode": "kratos",
      "have": true,
      "secretKey": "hhdAmlYUUXWTuwPxUVlnRoEGDszoKvXgCYCclsCx",
      "signForSwitch": false,
      "signForWrite": false
    },
    {
      "id": 3,
      "uuid": "e86784e65645477a862a64625c695b7b",
      "gmtCreate": 1713422494000,
      "gmtModified": 1722418077000,
      "name": "bug404503",
      "displayName": "bug404503",
      "partnerCode": "kratos",
      "have": true,
      "secretKey": "dlgEpbeQJlXaxGGIMOVayNJHilUMkeIgbbrWeITY",
      "signForSwitch": false,
      "signForWrite": false
    },
    {
      "id": 1,
      "uuid": "3daebd1f764911ee948efafd476b2800",
      "gmtCreate": 1635130473000,
      "gmtModified": 1635130497000,
      "name": "init",
      "displayName": "initApp",
      "enDisplayName": "initApp",
      "partnerCode": "kratos",
      "have": true,
      "description": "initApp"
    },
    {
      "id": 4,
      "uuid": "5efa424887c84c4798df4f9438cfcf81",
      "gmtCreate": 1718072251000,
      "gmtModified": 1722409977000,
      "name": "necoTestApp",
      "displayName": "necoTestApp",
      "partnerCode": "kratos",
      "have": true,
      "secretKey": "FpJsISPRmyqopMwNNPWfBtvuLxVkwOoNywDjKKKI",
      "signForSwitch": false,
      "signForWrite": false
    },
    {
      "id": 6,
      "uuid": "bad5b23350bb4895945f15afa120e75b",
      "gmtCreate": 1721183484000,
      "gmtModified": 1721183484000,
      "name": "JYTest",
      "displayName": "交易测试",
      "partnerCode": "kratos",
      "have": true,
      "secretKey": "GPLemqqzpNkbglGcxQhrAvBQkOPKyKRBwRzVOcrw",
      "signForSwitch": false,
      "signForWrite": false
    }
  ]
}