module.exports = {
  "code": 200,
  "message": "成功",
  "success": true,
  "data": {
    "name": "天座2405",
    "enName": "tianzuo-2405",
    "code": "system",
    "extendMap": "{\"menuAlignMode\":\"default\",\"themeSwitch\":true,\"globalNavigation\":true,\"displayType\":\"enterprise\",\"isMultiplePage\":true,\"defaultTheme\":\"themeS1\",\"language\":[\"en\",\"cn\"]}",
    "menuTree": [
      {
        "uuid": "d2a10274e06e46c8b49e02398db157d7",
        "code": "Statistics",
        "sortNo": 0,
        "level": 1,
        "type": 2,
        "parentUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "icon": "pie",
        "name": "大盘展示",
        "enName": "Dashboard",
        "originalName": "大盘展示",
        "originalEnName": "Dashboard",
        "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "createBy": "csb",
        "updateBy": "csb",
        "gmtCreate": *************,
        "gmtModified": *************,
        "children": [
          {
            "uuid": "e2c80631473e40208a9d792f809ee16d",
            "code": "InvokeStatistics",
            "sortNo": 0,
            "level": 2,
            "type": 2,
            "parentUuid": "d2a10274e06e46c8b49e02398db157d7",
            "icon": "celve1",
            "name": "调用大盘",
            "enName": "Calling Dashboard",
            "originalName": "调用大盘",
            "originalEnName": "Calling Dashboard",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "59aece7936a94b699de9deaba0963143",
                "code": "TZ0102",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/handle/dashboard/dataService",
                "parentUuid": "e2c80631473e40208a9d792f809ee16d",
                "icon": "query",
                "name": "数据源大盘",
                "enName": "Data Source Broad",
                "originalName": "数据源大盘",
                "originalEnName": "Data Source Broad",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "ab07234103464a64adc66f9755546fa7",
                    "functionUuid": "ab07234103464a64adc66f9755546fa7",
                    "code": "dataSourceReasonCodeStat",
                    "name": "数据源错误码统计",
                    "enName": "dataSourceReasonCodeStat",
                    "sortNo": 1,
                    "menuUuid": "59aece7936a94b699de9deaba0963143",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b6591b8790364b49bc9a999cb6cf0e57",
                    "functionUuid": "b6591b8790364b49bc9a999cb6cf0e57",
                    "code": "dataSourceDetailStat",
                    "name": "数据源手动统计",
                    "enName": "dataSourceDetailStat",
                    "sortNo": 1,
                    "menuUuid": "59aece7936a94b699de9deaba0963143",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "eca67f3f98094df2afca061b30bcc532",
                    "functionUuid": "eca67f3f98094df2afca061b30bcc532",
                    "code": "dataSourceInvokeRankStat",
                    "name": "数据源服务调用排行",
                    "enName": "dataSourceInvokeRankStat",
                    "sortNo": 1,
                    "menuUuid": "59aece7936a94b699de9deaba0963143",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "58693406498b4b759d41492a4b3e83b8",
                    "functionUuid": "58693406498b4b759d41492a4b3e83b8",
                    "code": "dataSourceInvokeDetailStat",
                    "name": "数据源调用统计",
                    "enName": "dataSourceInvokeDetailStat",
                    "sortNo": 1,
                    "menuUuid": "59aece7936a94b699de9deaba0963143",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0f21382564154bcdb84f88164f9f86a4",
                    "functionUuid": "0f21382564154bcdb84f88164f9f86a4",
                    "code": "dataSourceInvokeDetailForDay",
                    "name": "数据源状态及耗时",
                    "enName": "dataSourceInvokeDetailForDay",
                    "sortNo": 1,
                    "menuUuid": "59aece7936a94b699de9deaba0963143",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "70d49bd34181446d801b6543ab355fe1",
                    "functionUuid": "70d49bd34181446d801b6543ab355fe1",
                    "code": "dataSourceInvokeSourceTypeStat",
                    "name": "数据源调用来源类型分布",
                    "enName": "dataSourceInvokeSourceTypeStat",
                    "sortNo": 1,
                    "menuUuid": "59aece7936a94b699de9deaba0963143",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f1cdb60f216445b5a53c3c57814ec24b",
                    "functionUuid": "f1cdb60f216445b5a53c3c57814ec24b",
                    "code": "dataSourceWholeView",
                    "name": "数据源概览",
                    "enName": "dataSourceWholeView",
                    "sortNo": 1,
                    "menuUuid": "59aece7936a94b699de9deaba0963143",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "59aece7936a94b699de9deaba0963143",
                "groupUuid": "59aece7936a94b699de9deaba0963143",
                "groupIcon": "query",
                "menuName": "数据源大盘",
                "hasPermission": true
              },
              {
                "uuid": "dcfd632cf9eb47cfb355c1a513879dbc",
                "code": "dydp95569",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/handle/dashboard/appChannel",
                "parentUuid": "e2c80631473e40208a9d792f809ee16d",
                "name": "服务调用大盘",
                "enName": "Service Call Dashboard",
                "originalName": "服务调用大盘",
                "originalEnName": "Service Call Dashboard",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [],
                "id": "dcfd632cf9eb47cfb355c1a513879dbc",
                "groupUuid": "dcfd632cf9eb47cfb355c1a513879dbc",
                "menuName": "服务调用大盘",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "e2c80631473e40208a9d792f809ee16d",
            "groupUuid": "e2c80631473e40208a9d792f809ee16d",
            "groupName": "调用大盘",
            "groupIcon": "celve1",
            "hasPermission": true
          }
        ],
        "functionList": [],
        "id": "d2a10274e06e46c8b49e02398db157d7",
        "groupUuid": "d2a10274e06e46c8b49e02398db157d7",
        "groupName": "大盘展示",
        "groupIcon": "pie",
        "hasPermission": false
      },
      {
        "uuid": "c52719956ed54658afd8009f56702de8",
        "code": "ConfigManager",
        "sortNo": 1,
        "level": 1,
        "type": 2,
        "parentUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "icon": "workflow",
        "name": "数据接入",
        "enName": "External Data Access",
        "originalName": "数据接入",
        "originalEnName": "External Data Access",
        "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "createBy": "csb",
        "updateBy": "csb",
        "gmtCreate": *************,
        "gmtModified": *************,
        "children": [
          {
            "uuid": "8889f989ab834831baa6ab220803670c",
            "code": "DataManagement",
            "sortNo": 0,
            "level": 2,
            "type": 2,
            "parentUuid": "c52719956ed54658afd8009f56702de8",
            "icon": "database",
            "name": "数据管理",
            "enName": "Data Management",
            "originalName": "数据管理",
            "originalEnName": "Data Management",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "admin",
            "updateBy": "csb",
            "gmtCreate": 1733193702000,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "3a0f69ac88c546aa9937ea8433d0a514",
                "code": "DataSource",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/dm/datasource",
                "parentUuid": "8889f989ab834831baa6ab220803670c",
                "name": "数据源管理",
                "enName": "Data Source Management",
                "originalName": "数据源管理",
                "originalEnName": "Data Source Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.4.1",
                "createBy": "admin",
                "updateBy": "fujun.cai",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": 1733984255000,
                "functionList": [
                  {
                    "funcUuid": "6c6cb5a0788f4ca0b1f465bd71f277d8",
                    "functionUuid": "6c6cb5a0788f4ca0b1f465bd71f277d8",
                    "code": "test",
                    "name": "测试",
                    "enName": "Test",
                    "sortNo": 1,
                    "menuUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0765793f57984c8b90a887ba85aa480c",
                    "functionUuid": "0765793f57984c8b90a887ba85aa480c",
                    "code": "kgsjy",
                    "name": "开关数据源",
                    "enName": "kgsjy",
                    "sortNo": 1,
                    "menuUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "04cae019f1ec4608a0a676f67d8c4344",
                    "functionUuid": "04cae019f1ec4608a0a676f67d8c4344",
                    "code": "tableExport",
                    "name": "导出",
                    "enName": "Table Export",
                    "sortNo": 1,
                    "menuUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ca8390e14f9a4f4baec4f508670d6a83",
                    "functionUuid": "ca8390e14f9a4f4baec4f508670d6a83",
                    "code": "saveOrUpdate",
                    "name": "新增或修改",
                    "enName": "Save or Update",
                    "sortNo": 1,
                    "menuUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "60b1665c9cc749e5a960446fe8b7503c",
                    "functionUuid": "60b1665c9cc749e5a960446fe8b7503c",
                    "code": "dataSourceDetail",
                    "name": "详情",
                    "enName": "Data source details",
                    "sortNo": 1,
                    "menuUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e2a259bb3fa94af0a8330b948b50f641",
                    "functionUuid": "e2a259bb3fa94af0a8330b948b50f641",
                    "code": "searchDatasource",
                    "name": "搜索",
                    "enName": "Search",
                    "sortNo": 1,
                    "menuUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7d244ea559b14d90920c4c25a01323be",
                    "functionUuid": "7d244ea559b14d90920c4c25a01323be",
                    "code": "tableImport",
                    "name": "导入",
                    "enName": "Table Import",
                    "sortNo": 1,
                    "menuUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ace18678c1844c619a1779af44e4069b",
                    "functionUuid": "ace18678c1844c619a1779af44e4069b",
                    "code": "delete",
                    "name": "删除",
                    "enName": "Delete",
                    "sortNo": 1,
                    "menuUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "3a0f69ac88c546aa9937ea8433d0a514",
                "groupUuid": "3a0f69ac88c546aa9937ea8433d0a514",
                "menuName": "数据源管理",
                "hasPermission": true
              },
              {
                "uuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                "code": "DataTable2",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/dm/datagather",
                "parentUuid": "8889f989ab834831baa6ab220803670c",
                "name": "数据表管理",
                "enName": "Data Table Management",
                "originalName": "数据表管理",
                "originalEnName": "Data Table Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "d93afa6a8b4e42e6a77e56eccb808fce",
                    "functionUuid": "d93afa6a8b4e42e6a77e56eccb808fce",
                    "code": "saveLabel",
                    "name": "保存表标签",
                    "enName": "Save Table Label",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6eb962eef0f0456fb075dbea3e197f72",
                    "functionUuid": "6eb962eef0f0456fb075dbea3e197f72",
                    "code": "getDatatableDetail",
                    "name": "查询数据表详情",
                    "enName": "Get Datatable Detail",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "adfd186aeeaa4ca3b9e3f502cddc8ab5",
                    "functionUuid": "adfd186aeeaa4ca3b9e3f502cddc8ab5",
                    "code": "dataTableReference",
                    "name": "查看数据表引用关系",
                    "enName": "View Table Reference Relationships",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "18fa5ba54536445fad302605aaedad80",
                    "functionUuid": "18fa5ba54536445fad302605aaedad80",
                    "code": "createTable",
                    "name": "新建Hive表",
                    "enName": "Create Empty Hive Table",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5e2f9e231cba4539bff8d44b42f35c0a",
                    "functionUuid": "5e2f9e231cba4539bff8d44b42f35c0a",
                    "code": "relatedQualityTask",
                    "name": "关联数据质量任务",
                    "enName": "Associated Data Quality Tasks",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c4bb343fda93447c83218aeba26e7029",
                    "functionUuid": "c4bb343fda93447c83218aeba26e7029",
                    "code": "search",
                    "name": "搜索",
                    "enName": "Search",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e84a938412094d03a74dc6c29e62fcb1",
                    "functionUuid": "e84a938412094d03a74dc6c29e62fcb1",
                    "code": "testSql",
                    "name": "测试SQL运行",
                    "enName": "Test Sql Running",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a673afee00ce43e4a1470c6728f8f52f",
                    "functionUuid": "a673afee00ce43e4a1470c6728f8f52f",
                    "code": "saveColumns",
                    "name": "保存表结构",
                    "enName": "Save Table Columns",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "cbce52e34a3a43c4a0322728d5d82457",
                    "functionUuid": "cbce52e34a3a43c4a0322728d5d82457",
                    "code": "browseTableData",
                    "name": "查询表数据",
                    "enName": "Query Table Data",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "bccbdc65bb0441f9a6c1eaef451f3c86",
                    "functionUuid": "bccbdc65bb0441f9a6c1eaef451f3c86",
                    "code": "delete",
                    "name": "删除",
                    "enName": "Delete",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9b438c70b7a648efb13d332b6e694515",
                    "functionUuid": "9b438c70b7a648efb13d332b6e694515",
                    "code": "relatedTable",
                    "name": "关联数据表",
                    "enName": "Create Data Table",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e29ee6a25d3446ea8652524e04722f10",
                    "functionUuid": "e29ee6a25d3446ea8652524e04722f10",
                    "code": "refresh",
                    "name": "刷新表",
                    "enName": "Refresh Table",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "01efaee64e4e43e49669c0c4d6f1a1f4",
                    "functionUuid": "01efaee64e4e43e49669c0c4d6f1a1f4",
                    "code": "import",
                    "name": "导入表信息",
                    "enName": "Import Table Information",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ec7d89dc2d524d99895ad5266eb28155",
                    "functionUuid": "ec7d89dc2d524d99895ad5266eb28155",
                    "code": "updateTable",
                    "name": "修改",
                    "enName": "Modify",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4650a427d36345df9cdf1b379acfea09",
                    "functionUuid": "4650a427d36345df9cdf1b379acfea09",
                    "code": "export",
                    "name": "导出表信息",
                    "enName": "Export Table Information",
                    "sortNo": 1,
                    "menuUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                "groupUuid": "e38a86700f2f47f7ba7bec2a7f1b5a87",
                "menuName": "数据表管理",
                "hasPermission": true
              },
              {
                "uuid": "f1bd8a4530f34e26912ad8024666c2ef",
                "code": "resourceManage",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/dm/resourcemanage",
                "parentUuid": "8889f989ab834831baa6ab220803670c",
                "name": "资源管理",
                "enName": "Resource Management",
                "originalName": "资源管理",
                "originalEnName": "Resource Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "0b53a0f023a1496a88475beb6186d4bd",
                    "functionUuid": "0b53a0f023a1496a88475beb6186d4bd",
                    "code": "dataResourcesCreate",
                    "name": "文件夹创建",
                    "enName": "Folder Creation",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "db2b5fa0ff354268ae7f05c2b2e689eb",
                    "functionUuid": "db2b5fa0ff354268ae7f05c2b2e689eb",
                    "code": "dataResourcesRename",
                    "name": "文件夹重命名",
                    "enName": "Folder Renaming",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ec9ccd708b9e45ecbfc311b2ba6e4578",
                    "functionUuid": "ec9ccd708b9e45ecbfc311b2ba6e4578",
                    "code": "dataResourcesUpdate",
                    "name": "文件编辑",
                    "enName": "File Editing",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "745b919c7aaa492f9a9a2708b69ae260",
                    "functionUuid": "745b919c7aaa492f9a9a2708b69ae260",
                    "code": "dataResourcesListFile",
                    "name": " 文件列表查看",
                    "enName": "dataResources List File",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9aff4c57dbed49a28c648371195082e8",
                    "functionUuid": "9aff4c57dbed49a28c648371195082e8",
                    "code": "dataResourcesDelete",
                    "name": "文件夹删除",
                    "enName": "Folder Deletion",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b4f76acbdaae40edbdcb3886955baa87",
                    "functionUuid": "b4f76acbdaae40edbdcb3886955baa87",
                    "code": "dataResourceslistDir",
                    "name": "文件树层级关系",
                    "enName": "File Tree Hierarchy",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c1db0c022ddc4151840ec511b5280fd1",
                    "functionUuid": "c1db0c022ddc4151840ec511b5280fd1",
                    "code": "dataResourcesDelete",
                    "name": "文件删除",
                    "enName": "File Deletion",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a6d2eac6743d4417b66dc801d7c28190",
                    "functionUuid": "a6d2eac6743d4417b66dc801d7c28190",
                    "code": "dataResourcesInfo",
                    "name": "获取指定文件的信息",
                    "enName": "Get Information for Specific File",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "61201bbac74e41a084d3388c990f3666",
                    "functionUuid": "61201bbac74e41a084d3388c990f3666",
                    "code": "dataResourcesDownload",
                    "name": "文件下载",
                    "enName": "File Download",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9968685fc0f34079b1fd9c719971f3e6",
                    "functionUuid": "9968685fc0f34079b1fd9c719971f3e6",
                    "code": "dataResourcesUpload",
                    "name": "文件上传",
                    "enName": "File Upload",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f8963ff1ec3e4b6ba65904c884ac870d",
                    "functionUuid": "f8963ff1ec3e4b6ba65904c884ac870d",
                    "code": "copyPath",
                    "name": "复制路径",
                    "enName": "Copy Path",
                    "sortNo": 1,
                    "menuUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "f1bd8a4530f34e26912ad8024666c2ef",
                "groupUuid": "f1bd8a4530f34e26912ad8024666c2ef",
                "menuName": "资源管理",
                "hasPermission": true
              },
              {
                "uuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                "code": "DataProcess",
                "sortNo": 3,
                "level": 3,
                "type": 3,
                "path": "/dm/dataprocessing",
                "parentUuid": "8889f989ab834831baa6ab220803670c",
                "name": "数据加工",
                "enName": "Data Processing",
                "originalName": "数据加工",
                "originalEnName": "Data Processing",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "aecf72cd19124f498ba92fde6cc247bb",
                    "functionUuid": "aecf72cd19124f498ba92fde6cc247bb",
                    "code": "listAllRunning",
                    "name": "查询运行区所有数据加工任务",
                    "enName": "listAllRunning",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "02c52074ee944d3a8159c75bcddf13ad",
                    "functionUuid": "02c52074ee944d3a8159c75bcddf13ad",
                    "code": "batchOffline",
                    "name": "批量下线",
                    "enName": "Batch Offline",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "627bd99e0ecc4eb1acee4239329d6882",
                    "functionUuid": "627bd99e0ecc4eb1acee4239329d6882",
                    "code": "detail",
                    "name": "获取任务详情",
                    "enName": "Get Task Details",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f808c3b9adf6483fb24015d026c3f3fd",
                    "functionUuid": "f808c3b9adf6483fb24015d026c3f3fd",
                    "code": "updateCycle",
                    "name": "修改调度",
                    "enName": "Modify Schedule",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9ae643531a69423bb0dca168e1e1f9db",
                    "functionUuid": "9ae643531a69423bb0dca168e1e1f9db",
                    "code": "delete",
                    "name": "删除",
                    "enName": "Deletion",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "25ac4bde76dc40d390b2d34d1b847ac3",
                    "functionUuid": "25ac4bde76dc40d390b2d34d1b847ac3",
                    "code": "deleteByVersion",
                    "name": "删除版本",
                    "enName": "deleteByVersion",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "98bddb418eba45d8a654fc3e6e34f1de",
                    "functionUuid": "98bddb418eba45d8a654fc3e6e34f1de",
                    "code": "export",
                    "name": "导出",
                    "enName": "Export",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8306426f18ec42df8cf236fd5cea411a",
                    "functionUuid": "8306426f18ec42df8cf236fd5cea411a",
                    "code": "runSqlTask",
                    "name": "试跑",
                    "enName": "Test Run",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "55faa8bf6e7843a3ab3c5c3e172fdb94",
                    "functionUuid": "55faa8bf6e7843a3ab3c5c3e172fdb94",
                    "code": "pageQuery",
                    "name": "分页查询",
                    "enName": "Pagination Query",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ce1a026ebe824292b94b6ecb8137afa4",
                    "functionUuid": "ce1a026ebe824292b94b6ecb8137afa4",
                    "code": "detailByVersion",
                    "name": "通过版本查询详情",
                    "enName": "detailByVersion",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2828ad17fd2d4f57945423c11f064b1b",
                    "functionUuid": "2828ad17fd2d4f57945423c11f064b1b",
                    "code": "import",
                    "name": "文件导入",
                    "enName": "File Import",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0db069b71b2d49f4ad55fa6c0c47d4db",
                    "functionUuid": "0db069b71b2d49f4ad55fa6c0c47d4db",
                    "code": "changeVersion",
                    "name": "切换版本",
                    "enName": "Version Switch",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "10a6f2bdf80a487d82139c58fa6cfa3c",
                    "functionUuid": "10a6f2bdf80a487d82139c58fa6cfa3c",
                    "code": "batchOnline",
                    "name": "批量上线",
                    "enName": "Batch Online",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7e075b957e134d639e0eb4304b2d2a96",
                    "functionUuid": "7e075b957e134d639e0eb4304b2d2a96",
                    "code": "offline",
                    "name": "下线",
                    "enName": "Offline",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2d714288ef3745218f7b6d42e526df68",
                    "functionUuid": "2d714288ef3745218f7b6d42e526df68",
                    "code": "online",
                    "name": "上线",
                    "enName": "Online",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9aa39104e1b5421798d9fc560b610d9d",
                    "functionUuid": "9aa39104e1b5421798d9fc560b610d9d",
                    "code": "historyDetail",
                    "name": "历史版本详情",
                    "enName": "Historical Version Details",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d28c97b1c27f4c89b7880b3efbd11c8f",
                    "functionUuid": "d28c97b1c27f4c89b7880b3efbd11c8f",
                    "code": "listVersion",
                    "name": "查询任务版本",
                    "enName": "Query Task Versions",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6e672c5b7e7941fe929c00396d789200",
                    "functionUuid": "6e672c5b7e7941fe929c00396d789200",
                    "code": "execute",
                    "name": "列表试跑",
                    "enName": "List Test Run",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "edf6c10f3e104a60abed8318ab40913e",
                    "functionUuid": "edf6c10f3e104a60abed8318ab40913e",
                    "code": "addOrUpdate",
                    "name": "新增或修改",
                    "enName": "Add or Modify",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c9f6e4c3ffc24ba9a70363ca5e7ed643",
                    "functionUuid": "c9f6e4c3ffc24ba9a70363ca5e7ed643",
                    "code": "historyList",
                    "name": "历史版本",
                    "enName": "Historical Versions",
                    "sortNo": 1,
                    "menuUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "e182d5b3d8e44bcd8e758c8ada964dad",
                "groupUuid": "e182d5b3d8e44bcd8e758c8ada964dad",
                "menuName": "数据加工",
                "hasPermission": true
              },
              {
                "uuid": "cbda7fd9a54c413d847b888ab15c747c",
                "code": "DataTransport",
                "sortNo": 4,
                "level": 3,
                "type": 3,
                "path": "/dm/transport",
                "parentUuid": "8889f989ab834831baa6ab220803670c",
                "name": "数据同步",
                "enName": "Data Synchronism",
                "originalName": "数据同步",
                "originalEnName": "Data Synchronism",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "58efe2f263fa46088dc104bd2c7ecbbc",
                    "functionUuid": "58efe2f263fa46088dc104bd2c7ecbbc",
                    "code": "test",
                    "name": "试跑",
                    "enName": "Test Run",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "69e5693687f24647b9a4414bde7b7694",
                    "functionUuid": "69e5693687f24647b9a4414bde7b7694",
                    "code": "search",
                    "name": "搜索",
                    "enName": "Search",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f7aaab5f2e7c4793b1f8bd433a077be4",
                    "functionUuid": "f7aaab5f2e7c4793b1f8bd433a077be4",
                    "code": "changeVersion",
                    "name": "版本切换",
                    "enName": "Version Switch",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "33c1a2b7196d4fe2b7f8bebc0613d610",
                    "functionUuid": "33c1a2b7196d4fe2b7f8bebc0613d610",
                    "code": "syncOnLine",
                    "name": "数据同步任务上线",
                    "enName": "Data Synchronization Task Online",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "06a54451ef154ba79de7e54461de6922",
                    "functionUuid": "06a54451ef154ba79de7e54461de6922",
                    "code": "listAll",
                    "name": "数据同步非分页列表",
                    "enName": "Non-Paginated Data Synchronization",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8e52b6daa7294451a421445de0cec582",
                    "functionUuid": "8e52b6daa7294451a421445de0cec582",
                    "code": "add",
                    "name": "新增",
                    "enName": "Add",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7e4f667ff42b492e98124e95d8570b67",
                    "functionUuid": "7e4f667ff42b492e98124e95d8570b67",
                    "code": "export",
                    "name": "导出",
                    "enName": "Export",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e956ccf9b39a4d0092a718ee3001e5cc",
                    "functionUuid": "e956ccf9b39a4d0092a718ee3001e5cc",
                    "code": "onceLog",
                    "name": "查看日志",
                    "enName": "View Logs",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0a58211f1828457abdc4e719ad51406c",
                    "functionUuid": "0a58211f1828457abdc4e719ad51406c",
                    "code": "delete",
                    "name": "删除任务",
                    "enName": "Delete Task",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "681c195d3011463f8347d437d6c8c365",
                    "functionUuid": "681c195d3011463f8347d437d6c8c365",
                    "code": "syncOffLine",
                    "name": "数据同步任务下线",
                    "enName": "Data Synchronization Task Offline",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0d569a0f4b434e81b795c374a5aa501e",
                    "functionUuid": "0d569a0f4b434e81b795c374a5aa501e",
                    "code": "records",
                    "name": "运行记录",
                    "enName": "Running Record",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "be7db6990c854fec82f279d9a3389aed",
                    "functionUuid": "be7db6990c854fec82f279d9a3389aed",
                    "code": "syncSave",
                    "name": "数据同步任务新增",
                    "enName": "Data Synchronization Task Creation",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "bc434d4cdc034a85a58f6a572195ce5f",
                    "functionUuid": "bc434d4cdc034a85a58f6a572195ce5f",
                    "code": "delHisVersion",
                    "name": "删除历史版本",
                    "enName": "Delete Historical Versions",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "598f9d7b477a4b4e931f2eb968ddf0a6",
                    "functionUuid": "598f9d7b477a4b4e931f2eb968ddf0a6",
                    "code": "run",
                    "name": "开始运行",
                    "enName": "Start Running",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c274b4c2bbc548de811f828aa9202b2b",
                    "functionUuid": "c274b4c2bbc548de811f828aa9202b2b",
                    "code": "syncDetail",
                    "name": "数据同步任务详情",
                    "enName": "Data Synchronization Task Details",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1f882073621e4f158b01b30ad6af63b4",
                    "functionUuid": "1f882073621e4f158b01b30ad6af63b4",
                    "code": "syncDelete",
                    "name": "数据同步任务删除",
                    "enName": "Data Synchronization Task Deletion",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "cc282867f1924c779db513350c4fc9dc",
                    "functionUuid": "cc282867f1924c779db513350c4fc9dc",
                    "code": "import",
                    "name": "导入",
                    "enName": "Import",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0a057815ac9143d589f5175719e503db",
                    "functionUuid": "0a057815ac9143d589f5175719e503db",
                    "code": "syncList",
                    "name": "数据同步任务列表",
                    "enName": "Data Synchronization Task List",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "3c7e81c1ce5d49f5aa2e67dc9fc0ea7f",
                    "functionUuid": "3c7e81c1ce5d49f5aa2e67dc9fc0ea7f",
                    "code": "hisVersion",
                    "name": "历史版本",
                    "enName": "Historical Versions",
                    "sortNo": 1,
                    "menuUuid": "cbda7fd9a54c413d847b888ab15c747c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "cbda7fd9a54c413d847b888ab15c747c",
                "groupUuid": "cbda7fd9a54c413d847b888ab15c747c",
                "menuName": "数据同步",
                "hasPermission": true
              },
              {
                "uuid": "fdb4c10514514f57bdd5efb139fa0391",
                "code": "DataQualityInvestigation",
                "sortNo": 5,
                "level": 3,
                "type": 3,
                "path": "/dm/dataquality",
                "parentUuid": "8889f989ab834831baa6ab220803670c",
                "name": "数据质量探查",
                "enName": "Quality Profiling",
                "originalName": "数据质量探查",
                "originalEnName": "Quality Profiling",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "8e1e16b203494e258f87f46a8cb8ae78",
                    "functionUuid": "8e1e16b203494e258f87f46a8cb8ae78",
                    "code": "esDeleteTask",
                    "name": "ES-删除任务",
                    "enName": "ES - Delete Task",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1b173e7a1115476fa989e57eae9a3990",
                    "functionUuid": "1b173e7a1115476fa989e57eae9a3990",
                    "code": "log",
                    "name": "日志",
                    "enName": "Log",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "44e841a12d48477598552774d41889e8",
                    "functionUuid": "44e841a12d48477598552774d41889e8",
                    "code": "previewReport",
                    "name": "质量报告",
                    "enName": "Quality Report",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d70e91c6518d4cfbb91d9594b1220676",
                    "functionUuid": "d70e91c6518d4cfbb91d9594b1220676",
                    "code": "esUpdateTask",
                    "name": "ES-更新任务",
                    "enName": "ES - Update Task",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e3d208231c284157956ed9fa38d31893",
                    "functionUuid": "e3d208231c284157956ed9fa38d31893",
                    "code": "runQualityTask",
                    "name": "运行任务",
                    "enName": "Run Task",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0c9fdb8cfeb74a0f8d3a7a455783ae29",
                    "functionUuid": "0c9fdb8cfeb74a0f8d3a7a455783ae29",
                    "code": "esAddTask",
                    "name": "ES-新增任务",
                    "enName": "ES - Add Task",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d81803f307444e368a09cc0f7eb888a4",
                    "functionUuid": "d81803f307444e368a09cc0f7eb888a4",
                    "code": "esTaskDetail",
                    "name": "ES-任务详情",
                    "enName": "ES - Task Details",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "89afc3ab41ad423fabb752aaaad92286",
                    "functionUuid": "89afc3ab41ad423fabb752aaaad92286",
                    "code": "esAnalyTask",
                    "name": "ES-估算分析任务计算量",
                    "enName": "ES - Estimate Analysis Task Computing Volum",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "aad71c6938044ca3a5d3518f23bbc937",
                    "functionUuid": "aad71c6938044ca3a5d3518f23bbc937",
                    "code": "esViewReport",
                    "name": "ES-查看分析报告",
                    "enName": "ES - View Report",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b8f9533f51d54c35b90b558f4d5d4ae1",
                    "functionUuid": "b8f9533f51d54c35b90b558f4d5d4ae1",
                    "code": "esTaskRun",
                    "name": "ES-运行任务",
                    "enName": "ES - Task Run",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "34120d8004d144fd8fae70a00b6975ff",
                    "functionUuid": "34120d8004d144fd8fae70a00b6975ff",
                    "code": "delete",
                    "name": "删除",
                    "enName": "Delete",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "fb309d4ce2f0418b8c79c89cd422e5a8",
                    "functionUuid": "fb309d4ce2f0418b8c79c89cd422e5a8",
                    "code": "saveReport",
                    "name": "更新报告信息",
                    "enName": "Update Report Information",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a3e4f8a4f72c4273aee9be8245c25095",
                    "functionUuid": "a3e4f8a4f72c4273aee9be8245c25095",
                    "code": "getQualityTaskDetail",
                    "name": "查询任务详情",
                    "enName": "Query Task Details",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2d63e50a517548ff84842c132f36d581",
                    "functionUuid": "2d63e50a517548ff84842c132f36d581",
                    "code": "esTaskLog",
                    "name": "ES-任务日志",
                    "enName": "ES - Task Log",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b0f7145e455c4a05a712250d634ff011",
                    "functionUuid": "b0f7145e455c4a05a712250d634ff011",
                    "code": "updateQualityTask",
                    "name": "更新",
                    "enName": "Update",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c334364385d64af986b689eaa6ee423e",
                    "functionUuid": "c334364385d64af986b689eaa6ee423e",
                    "code": "getQualityMethod",
                    "name": "数据类型支持的分析方法",
                    "enName": "Supported Analysis Methods for Data Types",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "aa9e7574562041a3b2611f202867f339",
                    "functionUuid": "aa9e7574562041a3b2611f202867f339",
                    "code": "serach",
                    "name": "搜索",
                    "enName": "Search",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ec57fe94ae6542fe9d5e496334bc24c2",
                    "functionUuid": "ec57fe94ae6542fe9d5e496334bc24c2",
                    "code": "duplicateQuality",
                    "name": "复制",
                    "enName": "Copy",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "22a543e89a6345c8a655dcaae5bea58e",
                    "functionUuid": "22a543e89a6345c8a655dcaae5bea58e",
                    "code": "esQulityList",
                    "name": "ES-质量探查列表",
                    "enName": "ES - Quality Inspection List",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "839faad890a04aaaba04388cfa2536a1",
                    "functionUuid": "839faad890a04aaaba04388cfa2536a1",
                    "code": "esTaskCopy",
                    "name": "ES-复制任务",
                    "enName": "ES - Copy Task",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e72a052448824766b4bf82c46ca1bcf5",
                    "functionUuid": "e72a052448824766b4bf82c46ca1bcf5",
                    "code": "createQualityTask",
                    "name": "新建质量任务",
                    "enName": "Add quality task",
                    "sortNo": 1,
                    "menuUuid": "fdb4c10514514f57bdd5efb139fa0391",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "fdb4c10514514f57bdd5efb139fa0391",
                "groupUuid": "fdb4c10514514f57bdd5efb139fa0391",
                "menuName": "数据质量探查",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "8889f989ab834831baa6ab220803670c",
            "groupUuid": "8889f989ab834831baa6ab220803670c",
            "groupName": "数据管理",
            "groupIcon": "database",
            "hasPermission": true
          },
          {
            "uuid": "c2b7a18e80b549dc98d87b4bd18e49fd",
            "code": "TaskManagement",
            "sortNo": 1,
            "level": 2,
            "type": 2,
            "parentUuid": "c52719956ed54658afd8009f56702de8",
            "icon": "jianmo",
            "name": "任务管理",
            "enName": "Task Management",
            "originalName": "任务管理",
            "originalEnName": "Task Management",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "admin",
            "updateBy": "csb",
            "gmtCreate": 1733193702000,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "5f74934b5dd24790a7a24db960c524b7",
                "code": "taskFlowManage",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/dm/taskFlowManage",
                "parentUuid": "c2b7a18e80b549dc98d87b4bd18e49fd",
                "name": "任务流管理",
                "enName": "Task Flow Management",
                "originalName": "任务流管理",
                "originalEnName": "Task Flow Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "3bba9072738e4f90bb5935a4601dae21",
                    "functionUuid": "3bba9072738e4f90bb5935a4601dae21",
                    "code": "taskFlowVersionChangeVersion",
                    "name": "版本切换",
                    "enName": "Version Switch",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "84aa67cd26fa49768fcdb53001077bcf",
                    "functionUuid": "84aa67cd26fa49768fcdb53001077bcf",
                    "code": "taskFlowManageDetail",
                    "name": "数据检查详情",
                    "enName": "Data Check Details",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1d296e21e93a4475bcdc57149c5776b5",
                    "functionUuid": "1d296e21e93a4475bcdc57149c5776b5",
                    "code": "taskFlowManagePageQuery",
                    "name": "数据检查分页列表查询",
                    "enName": "Data Check Pagination Query",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "fddf6c54a5f64c32b34200c26e35f8c4",
                    "functionUuid": "fddf6c54a5f64c32b34200c26e35f8c4",
                    "code": "commonUsers",
                    "name": "获取所有用户",
                    "enName": "Get All Users",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "baa30cdc2ef14fe6927177f3d8591739",
                    "functionUuid": "baa30cdc2ef14fe6927177f3d8591739",
                    "code": "checkConfig",
                    "name": "数据检查配置信息",
                    "enName": "Data Check Configuration Information",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a58fe54cc10d46629d2f6b054f556164",
                    "functionUuid": "a58fe54cc10d46629d2f6b054f556164",
                    "code": "test",
                    "name": "任务流测试",
                    "enName": "Task Flow Testing",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ff92a4c9ac514e7e8ab39ad6c7a3df55",
                    "functionUuid": "ff92a4c9ac514e7e8ab39ad6c7a3df55",
                    "code": "AllRealTables",
                    "name": "获取所有的数据源下的表（注册、物理）",
                    "enName": "Get All Tables under All Data Sources - Registered, Physical",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ebe45fd7e7184fac87de0d68d1b72af1",
                    "functionUuid": "ebe45fd7e7184fac87de0d68d1b72af1",
                    "code": "update",
                    "name": "任务流更新",
                    "enName": "Task Flow Update",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4a10902cb0b647829b4a5f84d8b62fde",
                    "functionUuid": "4a10902cb0b647829b4a5f84d8b62fde",
                    "code": "taskFlowVersionUpdate",
                    "name": "任务流版本更新",
                    "enName": "Task Flow Version Update",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7a29e93bbc174ff0890d32cbba19d5d0",
                    "functionUuid": "7a29e93bbc174ff0890d32cbba19d5d0",
                    "code": "online",
                    "name": "任务流上线",
                    "enName": "Task Flow Online",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e0c8fe9d9fb64813b111b2ee26a9e59f",
                    "functionUuid": "e0c8fe9d9fb64813b111b2ee26a9e59f",
                    "code": "sublist",
                    "name": "子任务流下拉",
                    "enName": "Child Task Flow Dropdown",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "851a89a33da841a29674dec0640cb883",
                    "functionUuid": "851a89a33da841a29674dec0640cb883",
                    "code": "scriptTest",
                    "name": "数据检查表_脚本测试",
                    "enName": "Data Check Table Script Testing",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "65ed8906a3f6490eab3e31ac0bec3b7e",
                    "functionUuid": "65ed8906a3f6490eab3e31ac0bec3b7e",
                    "code": "dependenceList",
                    "name": "任务流依赖列表",
                    "enName": "Task Flow Dependency List",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "998ce94379db4dcea73700a21b2d43b4",
                    "functionUuid": "998ce94379db4dcea73700a21b2d43b4",
                    "code": "configTable",
                    "name": "任务流输入输出表配置",
                    "enName": "Task Flow Input and Output Table Configuration",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1e41293ec86f49d89ee915e91d68f460",
                    "functionUuid": "1e41293ec86f49d89ee915e91d68f460",
                    "code": "taskFlowManageList",
                    "name": "任务流版本列表",
                    "enName": "Task Flow Version List",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e9d473a6cb754c6fb8f284e75d0867cf",
                    "functionUuid": "e9d473a6cb754c6fb8f284e75d0867cf",
                    "code": "complementDataExecute",
                    "name": "补数执行",
                    "enName": "Retrospective Execution",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "586adb56d24e4e48be705951afe0c567",
                    "functionUuid": "586adb56d24e4e48be705951afe0c567",
                    "code": "offlineVerify",
                    "name": "任务流下线校验",
                    "enName": "Task Flow Deactivation Verification",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f89725f084ba42f9a49c0fac951ad412",
                    "functionUuid": "f89725f084ba42f9a49c0fac951ad412",
                    "code": "offlineBatch",
                    "name": "任务流批量下线",
                    "enName": "Task Flow Batch Offline",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "433b7fd91bb0443d9bead760b33c887b",
                    "functionUuid": "433b7fd91bb0443d9bead760b33c887b",
                    "code": "offline",
                    "name": "任务流下线",
                    "enName": "Task Flow Offline",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "17695ba10b374aa28fb0dd71e1f063b6",
                    "functionUuid": "17695ba10b374aa28fb0dd71e1f063b6",
                    "code": "taskFlowManageDependentCreate",
                    "name": "任务流依赖新增",
                    "enName": "Task Flow Dependency Creation",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0c9cffe9c723424385102e946d0b5eb1",
                    "functionUuid": "0c9cffe9c723424385102e946d0b5eb1",
                    "code": "taskFlowDict",
                    "name": "任务流字典",
                    "enName": "Task Flow Dictionary",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "18b5decbc6fd49afb0f4417292f5ac05",
                    "functionUuid": "18b5decbc6fd49afb0f4417292f5ac05",
                    "code": "taskFlowManageDataCheckCreate",
                    "name": "数据检查新增",
                    "enName": "Data Check Creation",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d38e6810aead4f44b1d07dbc59abc1c7",
                    "functionUuid": "d38e6810aead4f44b1d07dbc59abc1c7",
                    "code": "complementDataPreview",
                    "name": "补数预览",
                    "enName": "Retrospective Preview",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "570af1cdeb284e14993b7b83ffcf1a47",
                    "functionUuid": "570af1cdeb284e14993b7b83ffcf1a47",
                    "code": "taskFlowVersionDelete",
                    "name": "任务流版本删除",
                    "enName": "Task Flow Version Deletion",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e765501316e74847ae16d459efc720cd",
                    "functionUuid": "e765501316e74847ae16d459efc720cd",
                    "code": "taskFlowManageDependenceDetail",
                    "name": "任务流依赖详情",
                    "enName": "Task Flow Dependency Details",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "caa23515d8f849fc9b477e8b23887cbe",
                    "functionUuid": "caa23515d8f849fc9b477e8b23887cbe",
                    "code": "taskFlowManageDetail",
                    "name": "任务流版本详情",
                    "enName": "Task Flow Version Details",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9ef4834043f44089b52b7b0caea22e91",
                    "functionUuid": "9ef4834043f44089b52b7b0caea22e91",
                    "code": "taskFlowManagePage",
                    "name": "分页列表",
                    "enName": "Paginated List",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "93ada0fa29794f16bdf9dac9e3a88f98",
                    "functionUuid": "93ada0fa29794f16bdf9dac9e3a88f98",
                    "code": "UpdateCycle",
                    "name": "修改调度周期",
                    "enName": "Modify Schedule Period",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "010e728170de4556a72a12432c977bdf",
                    "functionUuid": "010e728170de4556a72a12432c977bdf",
                    "code": "taskFlowManageDelete",
                    "name": "任务依赖删除",
                    "enName": "Task Dependency Deletion",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a4d062eedbd64fc5b433de6b9edcb021",
                    "functionUuid": "a4d062eedbd64fc5b433de6b9edcb021",
                    "code": "dataCheckDelete",
                    "name": "数据检查删除",
                    "enName": "Data Check Deletion",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a4d419cdbc944992b97bc23b294e6d42",
                    "functionUuid": "a4d419cdbc944992b97bc23b294e6d42",
                    "code": "export",
                    "name": "任务流导出",
                    "enName": "Task Flow Export",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "285dc49e1ae64614a03a939b239ded25",
                    "functionUuid": "285dc49e1ae64614a03a939b239ded25",
                    "code": "import",
                    "name": "任务流导入",
                    "enName": "Task Flow Import",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "aa5bb587da5a4fec96cd6a0f4406c9e7",
                    "functionUuid": "aa5bb587da5a4fec96cd6a0f4406c9e7",
                    "code": "create",
                    "name": "任务流新增",
                    "enName": "Task Flow Creation",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4abdca5768884bd9bb3001f919e9e03e",
                    "functionUuid": "4abdca5768884bd9bb3001f919e9e03e",
                    "code": "taskFlowManagePageQuery",
                    "name": "任务依赖分页列表",
                    "enName": "Task Dependency Pagination List",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "785ca40cb69b4af08438792b1daae705",
                    "functionUuid": "785ca40cb69b4af08438792b1daae705",
                    "code": "taskFlowManageUpdate",
                    "name": "数据检查更新",
                    "enName": "Data Check Update",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0101017e3b684cf6873a024e3b38bda6",
                    "functionUuid": "0101017e3b684cf6873a024e3b38bda6",
                    "code": "delete",
                    "name": "任务流删除",
                    "enName": "Task Flow Deletion",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "09aa1c34abe74bb69fbf1c3ca995b1c1",
                    "functionUuid": "09aa1c34abe74bb69fbf1c3ca995b1c1",
                    "code": "taskFlowManageConfig",
                    "name": "基本配置信息",
                    "enName": "Basic Configuration Information",
                    "sortNo": 1,
                    "menuUuid": "5f74934b5dd24790a7a24db960c524b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "5f74934b5dd24790a7a24db960c524b7",
                "groupUuid": "5f74934b5dd24790a7a24db960c524b7",
                "menuName": "任务流管理",
                "hasPermission": true
              },
              {
                "uuid": "33197093a0a547b0881568e87aa235df",
                "code": "approvalManage",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/dm/taskFlowApprovalManage",
                "parentUuid": "c2b7a18e80b549dc98d87b4bd18e49fd",
                "name": "任务流审批",
                "enName": "Task Flow Approval",
                "originalName": "任务流审批",
                "originalEnName": "Task Flow Approval",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "d37d464749b64007a0504428e4fd9465",
                    "functionUuid": "d37d464749b64007a0504428e4fd9465",
                    "code": "taskFlowAuditDetail",
                    "name": "审批详情",
                    "enName": "Approval Details",
                    "sortNo": 1,
                    "menuUuid": "33197093a0a547b0881568e87aa235df",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "03ab111262d449cba224cb04a22daacd",
                    "functionUuid": "03ab111262d449cba224cb04a22daacd",
                    "code": "taskFlowAuditPage",
                    "name": "任务流审批查询",
                    "enName": "Task Flow Approval Query",
                    "sortNo": 1,
                    "menuUuid": "33197093a0a547b0881568e87aa235df",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "23180f9d58c54eff898f3ccb113162bb",
                    "functionUuid": "23180f9d58c54eff898f3ccb113162bb",
                    "code": "taskFlowAuditHandler",
                    "name": "任务流审批",
                    "enName": "Task Flow Approval",
                    "sortNo": 1,
                    "menuUuid": "33197093a0a547b0881568e87aa235df",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "33197093a0a547b0881568e87aa235df",
                "groupUuid": "33197093a0a547b0881568e87aa235df",
                "menuName": "任务流审批",
                "hasPermission": true
              },
              {
                "uuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                "code": "taskFlowInstance",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/dm/taskFlowInstance",
                "parentUuid": "c2b7a18e80b549dc98d87b4bd18e49fd",
                "name": "作业中心",
                "enName": "Work Center",
                "originalName": "作业中心",
                "originalEnName": "Work Center",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "78b4d1d2b7864f73aa936511fefa192f",
                    "functionUuid": "78b4d1d2b7864f73aa936511fefa192f",
                    "code": "taskFlowInstanceDetail",
                    "name": "任务流实例详情",
                    "enName": "Task Flow Instance Details",
                    "sortNo": 1,
                    "menuUuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "12ca73d8240f40f78278e207767cabd6",
                    "functionUuid": "12ca73d8240f40f78278e207767cabd6",
                    "code": "taskFlowInstanceReRunFlow",
                    "name": "任务流重跑",
                    "enName": "Task Flow Rerun",
                    "sortNo": 1,
                    "menuUuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "575f8b1a96334c59bd2ffe74e6bb5630",
                    "functionUuid": "575f8b1a96334c59bd2ffe74e6bb5630",
                    "code": "lineage",
                    "name": "任务流血缘",
                    "enName": "Task Flow Lineage",
                    "sortNo": 1,
                    "menuUuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0f28a974bc2940efa6c4e1eb7f787956",
                    "functionUuid": "0f28a974bc2940efa6c4e1eb7f787956",
                    "code": "termination",
                    "name": "任务流实例终止",
                    "enName": "Task Flow Instance Termination",
                    "sortNo": 1,
                    "menuUuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "20d44e7b923b411ba3a4489ca744a6f0",
                    "functionUuid": "20d44e7b923b411ba3a4489ca744a6f0",
                    "code": "taskFlowInstanceReRunTask",
                    "name": "任务流失败节点重跑",
                    "enName": "Task Flow Failed Node Rerun",
                    "sortNo": 1,
                    "menuUuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "684499ba6e5b414e87548bc57b11b2e6",
                    "functionUuid": "684499ba6e5b414e87548bc57b11b2e6",
                    "code": "taskFlowInstanceTaskLog",
                    "name": "任务实例日志",
                    "enName": "Task Instance Logs",
                    "sortNo": 1,
                    "menuUuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "33eea1d9514e48d4bd89b21c158425ca",
                    "functionUuid": "33eea1d9514e48d4bd89b21c158425ca",
                    "code": "taskFlowInstancePage",
                    "name": "任务流实例分页列表查询",
                    "enName": "Task Flow Instance Pagination Query",
                    "sortNo": 1,
                    "menuUuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "fed60a251d0b4d07bf980408ebe8b8e2",
                "groupUuid": "fed60a251d0b4d07bf980408ebe8b8e2",
                "menuName": "作业中心",
                "hasPermission": true
              },
              {
                "uuid": "f10601be91384bfc9fdd40988d1915e3",
                "code": "taskFlowTemplate",
                "sortNo": 3,
                "level": 3,
                "type": 3,
                "path": "/dm/taskFlowTemplate",
                "parentUuid": "c2b7a18e80b549dc98d87b4bd18e49fd",
                "name": "任务流模板",
                "enName": "Task Flow Template",
                "originalName": "任务流模板",
                "originalEnName": "Task Flow Template",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "ea8609085a46426dacdd96538455577f",
                    "functionUuid": "ea8609085a46426dacdd96538455577f",
                    "code": "templatePage",
                    "name": "任务流_分页获取任务流模板列表",
                    "enName": "Task Flow - Paginated Task Flow Template List",
                    "sortNo": 1,
                    "menuUuid": "f10601be91384bfc9fdd40988d1915e3",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "68138726a46340d68db0a6146b6fd96f",
                    "functionUuid": "68138726a46340d68db0a6146b6fd96f",
                    "code": "TemplateByScene",
                    "name": "获取任务流模板根据任务流场景",
                    "enName": "Get Task Flow Template by Task Flow Scenario",
                    "sortNo": 1,
                    "menuUuid": "f10601be91384bfc9fdd40988d1915e3",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "bb20159871344843b8b94f203ce28479",
                    "functionUuid": "bb20159871344843b8b94f203ce28479",
                    "code": "taskFlowManageTemplate",
                    "name": "任务流模板详情",
                    "enName": "Task Flow Template Details",
                    "sortNo": 1,
                    "menuUuid": "f10601be91384bfc9fdd40988d1915e3",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "f10601be91384bfc9fdd40988d1915e3",
                "groupUuid": "f10601be91384bfc9fdd40988d1915e3",
                "menuName": "任务流模板",
                "hasPermission": true
              },
              {
                "uuid": "6b14d8550fe14f8cad3b666aedd72e2d",
                "code": "taskFlowAlert",
                "sortNo": 4,
                "level": 3,
                "type": 3,
                "path": "/dm/taskFlowAlert",
                "parentUuid": "c2b7a18e80b549dc98d87b4bd18e49fd",
                "name": "任务流预警",
                "enName": "Task Flow Alert",
                "originalName": "任务流预警",
                "originalEnName": "Task Flow Alert",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.2.0",
                "createBy": "admin",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": 1733193702000,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "e977686737294499b688ec7474672df6",
                    "functionUuid": "e977686737294499b688ec7474672df6",
                    "code": "taskFlowAlertHandle",
                    "name": "预警处理",
                    "enName": "Alert Handling",
                    "sortNo": 1,
                    "menuUuid": "6b14d8550fe14f8cad3b666aedd72e2d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f2bfa553e47d4bfc953e5ab51b415803",
                    "functionUuid": "f2bfa553e47d4bfc953e5ab51b415803",
                    "code": "taskFlowAlertPage",
                    "name": "预警列表查询",
                    "enName": "Alert List Query",
                    "sortNo": 1,
                    "menuUuid": "6b14d8550fe14f8cad3b666aedd72e2d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "6b14d8550fe14f8cad3b666aedd72e2d",
                "groupUuid": "6b14d8550fe14f8cad3b666aedd72e2d",
                "menuName": "任务流预警",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "c2b7a18e80b549dc98d87b4bd18e49fd",
            "groupUuid": "c2b7a18e80b549dc98d87b4bd18e49fd",
            "groupName": "任务管理",
            "groupIcon": "jianmo",
            "hasPermission": true
          },
          {
            "uuid": "6d4ced81df0444c3a0e8848fe2ad675a",
            "code": "FieldConfig",
            "sortNo": 2,
            "level": 2,
            "type": 2,
            "parentUuid": "c52719956ed54658afd8009f56702de8",
            "icon": "puzzle",
            "name": "字段管理",
            "enName": "Field Management",
            "originalName": "字段管理",
            "originalEnName": "Field Management",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "e92ff5eae745442f9b8d005b3f31001b",
                "code": "QX0802",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/bridge/fields/fieldManage",
                "parentUuid": "6d4ced81df0444c3a0e8848fe2ad675a",
                "name": "字段管理",
                "enName": "Field Management",
                "originalName": "字段管理",
                "originalEnName": "Field Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "7e20ea16e8274c2fa5f6bbf18901e9d1",
                    "functionUuid": "7e20ea16e8274c2fa5f6bbf18901e9d1",
                    "code": "viewSystemfield",
                    "name": "查看系统字段",
                    "enName": "viewSystemfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "87a4a53ea930446788e6d8a2efeea55f",
                    "functionUuid": "87a4a53ea930446788e6d8a2efeea55f",
                    "code": "fieldSystemRelation",
                    "name": "系统字段引用关系",
                    "enName": "fieldSystemRelation",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7edf3ac7cecf4178b60f6ec5c9fdc769",
                    "functionUuid": "7edf3ac7cecf4178b60f6ec5c9fdc769",
                    "code": "hqmocksjscff13146",
                    "name": "获取mock数据生成方法",
                    "enName": "mockGenMethod",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8d4ca860b53243a2ae07bda17619b138",
                    "functionUuid": "8d4ca860b53243a2ae07bda17619b138",
                    "code": "deleteSystemfield",
                    "name": "删除系统字段",
                    "enName": "deleteSystemfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ab5f1b8f40f64386b900aecc06a1ac9d",
                    "functionUuid": "ab5f1b8f40f64386b900aecc06a1ac9d",
                    "code": "modifySystemfield",
                    "name": "修改系统字段",
                    "enName": "modifySystemfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c3a2b1f713b047109f1ed824262d66ec",
                    "functionUuid": "c3a2b1f713b047109f1ed824262d66ec",
                    "code": "exportScriptField",
                    "name": "导出动态字段",
                    "enName": "exportScriptField",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6a672b7874e14177aeb3b135fab0c385",
                    "functionUuid": "6a672b7874e14177aeb3b135fab0c385",
                    "code": "addSystemfield",
                    "name": "添加系统字段",
                    "enName": "addSystemfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1ec04020fd6546329dc1281db7d72ecc",
                    "functionUuid": "1ec04020fd6546329dc1281db7d72ecc",
                    "code": "deleteDynamicfield",
                    "name": "删除动态字段",
                    "enName": "deleteDynamicfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "548bc5bf65ac4305a00877cdc256d2b4",
                    "functionUuid": "548bc5bf65ac4305a00877cdc256d2b4",
                    "code": "modifyDynamicfield",
                    "name": "修改动态字段",
                    "enName": "modifyDynamicfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "feb0422dd1144ca691e85511e8535c20",
                    "functionUuid": "feb0422dd1144ca691e85511e8535c20",
                    "code": "viewDynamicfield",
                    "name": "查看动态字段",
                    "enName": "viewDynamicfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c62d8788531f4b93adb8ecab197b0f54",
                    "functionUuid": "c62d8788531f4b93adb8ecab197b0f54",
                    "code": "importSystemfield",
                    "name": "导入系统字段",
                    "enName": "importSystemfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c0634031b6fa421ebba15642126a5309",
                    "functionUuid": "c0634031b6fa421ebba15642126a5309",
                    "code": "batchDeleteSystemField",
                    "name": "批量删除系统字段",
                    "enName": "batchDeleteSystemField",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6a9d81da773845d5b678adb85e0bfa82",
                    "functionUuid": "6a9d81da773845d5b678adb85e0bfa82",
                    "code": "batchDeleteDynamicField",
                    "name": "批量删除动态字段",
                    "enName": "batchDeleteDynamicField",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "47372e7246d34b69b87f12dc441a002a",
                    "functionUuid": "47372e7246d34b69b87f12dc441a002a",
                    "code": "fieldScriptRelation",
                    "name": "动态字段引用关系",
                    "enName": "fieldScriptRelation",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "dd3a6ba65764458e8db57eb5a02c2162",
                    "functionUuid": "dd3a6ba65764458e8db57eb5a02c2162",
                    "code": "importDynamicfield",
                    "name": "导入动态字段",
                    "enName": "importDynamicfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7be7c9440abd40a19dfd1b2a27940c49",
                    "functionUuid": "7be7c9440abd40a19dfd1b2a27940c49",
                    "code": "exportSystemField",
                    "name": "导出系统字段",
                    "enName": "exportSystemField",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "13b67569468b46918f7036eceb174f5a",
                    "functionUuid": "13b67569468b46918f7036eceb174f5a",
                    "code": "addDynamicfield",
                    "name": "添加动态字段",
                    "enName": "addDynamicfield",
                    "sortNo": 1,
                    "menuUuid": "e92ff5eae745442f9b8d005b3f31001b",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "e92ff5eae745442f9b8d005b3f31001b",
                "groupUuid": "e92ff5eae745442f9b8d005b3f31001b",
                "menuName": "字段管理",
                "hasPermission": true
              },
              {
                "uuid": "ed755d4b04884b02b27e031169d067ff",
                "code": "QX0801",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/bridge/fields/fieldGroup",
                "parentUuid": "6d4ced81df0444c3a0e8848fe2ad675a",
                "name": "字段分组",
                "enName": "Field Grouping",
                "originalName": "字段分组",
                "originalEnName": "Field Grouping",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "8a638fcad6d3433dabcb1f21fabe22f9",
                    "functionUuid": "8a638fcad6d3433dabcb1f21fabe22f9",
                    "code": "addGroup",
                    "name": "添加字段分组",
                    "enName": "addGroup",
                    "sortNo": 1,
                    "menuUuid": "ed755d4b04884b02b27e031169d067ff",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "27b5419c0b844282ad8d54e9d199b18e",
                    "functionUuid": "27b5419c0b844282ad8d54e9d199b18e",
                    "code": "modifyGroup",
                    "name": "修改字段分组",
                    "enName": "modifyGroup",
                    "sortNo": 1,
                    "menuUuid": "ed755d4b04884b02b27e031169d067ff",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f6822a785cd6411db65bcdcc073cf61b",
                    "functionUuid": "f6822a785cd6411db65bcdcc073cf61b",
                    "code": "deleteGroup",
                    "name": "删除字段分组",
                    "enName": "deleteGroup",
                    "sortNo": 1,
                    "menuUuid": "ed755d4b04884b02b27e031169d067ff",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "fbf77fb7183848dfb08dcf9a4df1de85",
                    "functionUuid": "fbf77fb7183848dfb08dcf9a4df1de85",
                    "code": "viewGroup",
                    "name": "查看字段分组",
                    "enName": "viewGroup",
                    "sortNo": 1,
                    "menuUuid": "ed755d4b04884b02b27e031169d067ff",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "ed755d4b04884b02b27e031169d067ff",
                "groupUuid": "ed755d4b04884b02b27e031169d067ff",
                "menuName": "字段分组",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "6d4ced81df0444c3a0e8848fe2ad675a",
            "groupUuid": "6d4ced81df0444c3a0e8848fe2ad675a",
            "groupName": "字段管理",
            "groupIcon": "puzzle",
            "hasPermission": true
          },
          {
            "uuid": "fb74e7233e544c1fbdcb60f36e8a2ceb",
            "code": "ExternalIndicator",
            "sortNo": 3,
            "level": 2,
            "type": 2,
            "parentUuid": "c52719956ed54658afd8009f56702de8",
            "icon": "model",
            "name": "指标管理",
            "enName": "External Indicator",
            "originalName": "指标管理",
            "originalEnName": "External Indicator",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "7828bcd5b0b745a79e476e5c43859f58",
                "code": "CP0101",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/params/parameter",
                "parentUuid": "fb74e7233e544c1fbdcb60f36e8a2ceb",
                "icon": "query",
                "name": "报文结构管理",
                "enName": "Message Structure Management",
                "originalName": "报文结构管理",
                "originalEnName": "Message Structure Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "4efb94ca005341918d8df8a475a0858c",
                    "functionUuid": "4efb94ca005341918d8df8a475a0858c",
                    "code": "add",
                    "name": "新增",
                    "enName": "add",
                    "sortNo": 0,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0900cd9e4a7b47b892fcd6bce01d4cec",
                    "functionUuid": "0900cd9e4a7b47b892fcd6bce01d4cec",
                    "code": "export",
                    "name": "导出",
                    "enName": "export",
                    "sortNo": 0,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "042f82bcba81466ea657748b07d1a43f",
                    "functionUuid": "042f82bcba81466ea657748b07d1a43f",
                    "code": "config",
                    "name": "结构配置",
                    "enName": "config",
                    "sortNo": 0,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f1630043fd2f4102bfb608b851de373f",
                    "functionUuid": "f1630043fd2f4102bfb608b851de373f",
                    "code": "import",
                    "name": "导入",
                    "enName": "import",
                    "sortNo": 0,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "159f2ee38dba4ed5bacb1f6a2a01fa6a",
                    "functionUuid": "159f2ee38dba4ed5bacb1f6a2a01fa6a",
                    "code": "delete",
                    "name": "删除",
                    "enName": "delete",
                    "sortNo": 0,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "3742ede41b244595b9ce832aeb28e375",
                    "functionUuid": "3742ede41b244595b9ce832aeb28e375",
                    "code": "modify",
                    "name": "修改",
                    "enName": "modify",
                    "sortNo": 0,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "3a70e8c6273d450fb350e7e23481a976",
                    "functionUuid": "3a70e8c6273d450fb350e7e23481a976",
                    "code": "get",
                    "name": "查看",
                    "enName": "get",
                    "sortNo": 1,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d69f3324ad964694888bd9b317a09575",
                    "functionUuid": "d69f3324ad964694888bd9b317a09575",
                    "code": "fieldCreate",
                    "name": "字段创建",
                    "enName": "fieldCreate",
                    "sortNo": 1,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "98eebf884ab045489302e7e3cf3cf560",
                    "functionUuid": "98eebf884ab045489302e7e3cf3cf560",
                    "code": "fieldReorder",
                    "name": "字段排序",
                    "enName": "fieldReorder",
                    "sortNo": 1,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f86a44e46e474561a5e425c9b7e4c897",
                    "functionUuid": "f86a44e46e474561a5e425c9b7e4c897",
                    "code": "fieldUpdate",
                    "name": "字段更新",
                    "enName": "fieldUpdate",
                    "sortNo": 1,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ea949ddce51b45b8b789e7179cc58734",
                    "functionUuid": "ea949ddce51b45b8b789e7179cc58734",
                    "code": "fieldDelete",
                    "name": "字段删除",
                    "enName": "fieldDelete",
                    "sortNo": 1,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "95a2c0eb3fa94b3cbb44644328d74af0",
                    "functionUuid": "95a2c0eb3fa94b3cbb44644328d74af0",
                    "code": "docquery",
                    "name": "报文查询",
                    "enName": "doc_query",
                    "sortNo": 1,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2fdabda0e0d445aeaaa871e569ef3758",
                    "functionUuid": "2fdabda0e0d445aeaaa871e569ef3758",
                    "code": "doclist",
                    "name": "报文列表",
                    "enName": "doc_list",
                    "sortNo": 1,
                    "menuUuid": "7828bcd5b0b745a79e476e5c43859f58",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "7828bcd5b0b745a79e476e5c43859f58",
                "groupUuid": "7828bcd5b0b745a79e476e5c43859f58",
                "groupIcon": "query",
                "menuName": "报文结构管理",
                "hasPermission": true
              },
              {
                "uuid": "e9a45cd5552545b8a3a50587cad60b3a",
                "code": "CP0103",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/params/template",
                "parentUuid": "fb74e7233e544c1fbdcb60f36e8a2ceb",
                "icon": "query",
                "name": "指标模板管理",
                "enName": "Indicator Template Management",
                "originalName": "指标模板管理",
                "originalEnName": "Indicator Template Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "138a4e09ea524e389c2d4749b43ca0f0",
                    "functionUuid": "138a4e09ea524e389c2d4749b43ca0f0",
                    "code": "delete",
                    "name": "删除",
                    "enName": "delete",
                    "sortNo": 0,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "75a950b350dc4cec8267c9b70f7030fa",
                    "functionUuid": "75a950b350dc4cec8267c9b70f7030fa",
                    "code": "add",
                    "name": "增加",
                    "enName": "add",
                    "sortNo": 0,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f6881078df7c463c81edfabbf1d44222",
                    "functionUuid": "f6881078df7c463c81edfabbf1d44222",
                    "code": "update",
                    "name": "修改",
                    "enName": "update",
                    "sortNo": 0,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "561ff8880ac84516aad752deba972a24",
                    "functionUuid": "561ff8880ac84516aad752deba972a24",
                    "code": "export",
                    "name": "导出",
                    "enName": "export",
                    "sortNo": 0,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4125ec308fbf4dbcb389e540e5399eb1",
                    "functionUuid": "4125ec308fbf4dbcb389e540e5399eb1",
                    "code": "import",
                    "name": "导入",
                    "enName": "import",
                    "sortNo": 0,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f778cbc2e334461bb02c043553130675",
                    "functionUuid": "f778cbc2e334461bb02c043553130675",
                    "code": "templateCategoryDelete",
                    "name": "删除模板分类",
                    "enName": "templateCategoryDelete",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1002f70558c94efc81e296f05974923f",
                    "functionUuid": "1002f70558c94efc81e296f05974923f",
                    "code": "DownLoadCheckedFile",
                    "name": "下载校验明细文件",
                    "enName": "DownLoadCheckedFile",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "efe61a3bc67e44ea9669bb6d769d40a1",
                    "functionUuid": "efe61a3bc67e44ea9669bb6d769d40a1",
                    "code": "visualTemplate",
                    "name": "可视化模版",
                    "enName": "visualTemplate",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "809da92b68b34506b72fddf0856eddfa",
                    "functionUuid": "809da92b68b34506b72fddf0856eddfa",
                    "code": "batchEnable",
                    "name": "批量启用",
                    "enName": "batch_enable",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a56fc5130cbb41f69b0391d7a02dd3e6",
                    "functionUuid": "a56fc5130cbb41f69b0391d7a02dd3e6",
                    "code": "visualCreate",
                    "name": "可视化模版新增",
                    "enName": "visual_create",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6d6ecfe86c2b43df896780ac77a807f8",
                    "functionUuid": "6d6ecfe86c2b43df896780ac77a807f8",
                    "code": "tempDisable",
                    "name": "模版禁用",
                    "enName": "temp_disable",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e608da3ec8f449beb43015276067046e",
                    "functionUuid": "e608da3ec8f449beb43015276067046e",
                    "code": "visualizationGet",
                    "name": "可视化模板获取",
                    "enName": "visualizationGet",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "81fc2d3e94074b9fa025c6c5cc6493aa",
                    "functionUuid": "81fc2d3e94074b9fa025c6c5cc6493aa",
                    "code": "templateGet",
                    "name": "模版获取",
                    "enName": "tempGet",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8080ad3d366b4e38bd6ac9cc4d857b7f",
                    "functionUuid": "8080ad3d366b4e38bd6ac9cc4d857b7f",
                    "code": "templateCategoryUpdate",
                    "name": "更新模板分类",
                    "enName": "templateCategoryUpdate",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e4e0cbe3118f4b18b1e6dd5ec9cd5a59",
                    "functionUuid": "e4e0cbe3118f4b18b1e6dd5ec9cd5a59",
                    "code": "tempPage",
                    "name": "模版分页",
                    "enName": "tempPage",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2f25736905ee4fb780be924bd0047011",
                    "functionUuid": "2f25736905ee4fb780be924bd0047011",
                    "code": "batchDisableByCondition",
                    "name": "根据条件批量禁用模版",
                    "enName": "batchDisableByCondition",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b465f244a6f747288adba5d8fd3ff91c",
                    "functionUuid": "b465f244a6f747288adba5d8fd3ff91c",
                    "code": "batchDisable",
                    "name": "批量禁用",
                    "enName": "batch_disable",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "de01ca248da241d2be9100b61781c46e",
                    "functionUuid": "de01ca248da241d2be9100b61781c46e",
                    "code": "templateCategoryReorder",
                    "name": "重排序模板分类",
                    "enName": "templateCategoryReorder",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "132b77c07fce49cb85aeecc3c4a8add1",
                    "functionUuid": "132b77c07fce49cb85aeecc3c4a8add1",
                    "code": "templateCategoryAdd",
                    "name": "创建模板分类",
                    "enName": "templateCategoryAdd",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b6a69d0c5cdc4a65818ac205cd08c9d3",
                    "functionUuid": "b6a69d0c5cdc4a65818ac205cd08c9d3",
                    "code": "templateTest",
                    "name": "模板测试",
                    "enName": "templateTest",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d87d3d1c692544e89810080650734b45",
                    "functionUuid": "d87d3d1c692544e89810080650734b45",
                    "code": "tempList",
                    "name": "模版列表",
                    "enName": "tempList",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "93f7c752c051457aafe6621ac58b541a",
                    "functionUuid": "93f7c752c051457aafe6621ac58b541a",
                    "code": "visualUpdate",
                    "name": "可视化模版修改",
                    "enName": "visual_update",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d16c3a2828a44f70bd6b38400a3cd20c",
                    "functionUuid": "d16c3a2828a44f70bd6b38400a3cd20c",
                    "code": "batchEnableByCondition",
                    "name": "根据条件批量启用模版",
                    "enName": "batchEnableByCondition",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c902753e61664be2b9a21542f4b4a3ee",
                    "functionUuid": "c902753e61664be2b9a21542f4b4a3ee",
                    "code": "tampEnable",
                    "name": "模版启用",
                    "enName": "temp_enable",
                    "sortNo": 1,
                    "menuUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "e9a45cd5552545b8a3a50587cad60b3a",
                "groupUuid": "e9a45cd5552545b8a3a50587cad60b3a",
                "groupIcon": "query",
                "menuName": "指标模板管理",
                "hasPermission": true
              },
              {
                "uuid": "b1e251bcedca4af4b2757236ce3e27f4",
                "code": "CP0102",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/params/target",
                "parentUuid": "fb74e7233e544c1fbdcb60f36e8a2ceb",
                "icon": "query",
                "name": "指标管理",
                "enName": "Indicator Management",
                "originalName": "指标管理",
                "originalEnName": "Indicator Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "79c08f2c517946f1a11ed7f7ae6fd2f5",
                    "functionUuid": "79c08f2c517946f1a11ed7f7ae6fd2f5",
                    "code": "modify",
                    "name": "修改",
                    "enName": "modify",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "86266a804e6f46cfb441ac64f71785f5",
                    "functionUuid": "86266a804e6f46cfb441ac64f71785f5",
                    "code": "export",
                    "name": "导出",
                    "enName": "export",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "59c972d1dbf14e9bb85847a13d8cc8f8",
                    "functionUuid": "59c972d1dbf14e9bb85847a13d8cc8f8",
                    "code": "import",
                    "name": "导入",
                    "enName": "import",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "39ed37e955e242e9a2ae4a1bf51facea",
                    "functionUuid": "39ed37e955e242e9a2ae4a1bf51facea",
                    "code": "add",
                    "name": "新增",
                    "enName": "add",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ac938feabc5a4ce1864d88c57e8436a9",
                    "functionUuid": "ac938feabc5a4ce1864d88c57e8436a9",
                    "code": "addPackage",
                    "name": "加入指标集",
                    "enName": "addPackage",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "73b972e0338e4e34b6b74794841ccd37",
                    "functionUuid": "73b972e0338e4e34b6b74794841ccd37",
                    "code": "indexVersionTest",
                    "name": "指标版本测试",
                    "enName": "indexVersionTest",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a1a725efea36497b8040d1d5168398ba",
                    "functionUuid": "a1a725efea36497b8040d1d5168398ba",
                    "code": "test",
                    "name": "测试",
                    "enName": "test",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5df8a76bcd3d49c48843bd0a98398976",
                    "functionUuid": "5df8a76bcd3d49c48843bd0a98398976",
                    "code": "delete",
                    "name": "删除",
                    "enName": "delete",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "11a865cd87cb449dbdc86ed17beb9cab",
                    "functionUuid": "11a865cd87cb449dbdc86ed17beb9cab",
                    "code": "batchCreate",
                    "name": "批量创建",
                    "enName": "batchCreate",
                    "sortNo": 0,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e0af01358a5e45d78c882dba59189abb",
                    "functionUuid": "e0af01358a5e45d78c882dba59189abb",
                    "code": "addPackageByCondition",
                    "name": "根据条件加入指标集",
                    "enName": "addPackageByCondition",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7d2be00b84d146dca7262aa3c4ee060b",
                    "functionUuid": "7d2be00b84d146dca7262aa3c4ee060b",
                    "code": "createIndexVersion",
                    "name": "创建指标版本",
                    "enName": "createIndexVersion",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6dd9264fb0a0450a809795aca91ab02d",
                    "functionUuid": "6dd9264fb0a0450a809795aca91ab02d",
                    "code": "selectAllOffline",
                    "name": "全选批量下线",
                    "enName": "selectAllOffline",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e922f6d33edc4d36a4a1f48527ca0c11",
                    "functionUuid": "e922f6d33edc4d36a4a1f48527ca0c11",
                    "code": "categoryTree",
                    "name": "指标分类查询",
                    "enName": "categoryTree",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "508dd20d423a48d1aa04f2936c6bf694",
                    "functionUuid": "508dd20d423a48d1aa04f2936c6bf694",
                    "code": "indexDisable",
                    "name": "指标禁用",
                    "enName": "index_disable",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "586d261979ea413b8811a405cf9f9f9b",
                    "functionUuid": "586d261979ea413b8811a405cf9f9f9b",
                    "code": "indexCategoryAdd",
                    "name": "创建指标分类",
                    "enName": "indexCategoryAdd",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "152758891aaa438aab03ea0a4ca52b7f",
                    "functionUuid": "152758891aaa438aab03ea0a4ca52b7f",
                    "code": "indexCategoryDelete",
                    "name": "删除指标分类",
                    "enName": "indexCategoryDelete",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "07ad83711f164baa8c777a82049b0e6c",
                    "functionUuid": "07ad83711f164baa8c777a82049b0e6c",
                    "code": "batchDisable",
                    "name": "批量禁用",
                    "enName": "batch_disable",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5d0656a4a01a4e6c9d3828f2060d5339",
                    "functionUuid": "5d0656a4a01a4e6c9d3828f2060d5339",
                    "code": "indexCategoryReorder",
                    "name": "重排序指标分类",
                    "enName": "indexCategoryReorder",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a2c35f6865f6407196fe67e179b95b1d",
                    "functionUuid": "a2c35f6865f6407196fe67e179b95b1d",
                    "code": "selectAllOnline",
                    "name": "全选批量上线",
                    "enName": "selectAllOnline",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1ed25bda2b7e492dbf8f855cea9e3a32",
                    "functionUuid": "1ed25bda2b7e492dbf8f855cea9e3a32",
                    "code": "indexEnable",
                    "name": "指标启用",
                    "enName": "index_enable",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "77b0fa75cef640ae8e6b14f56859daa4",
                    "functionUuid": "77b0fa75cef640ae8e6b14f56859daa4",
                    "code": "batchEnable",
                    "name": "批量启用",
                    "enName": "batch_enable",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "997d4b946763488fa289a400cf42851a",
                    "functionUuid": "997d4b946763488fa289a400cf42851a",
                    "code": "indexlist",
                    "name": "指标列表",
                    "enName": "index_list",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8551ec8cb11441fba8ad538bd086d65a",
                    "functionUuid": "8551ec8cb11441fba8ad538bd086d65a",
                    "code": "indexCategoryUpdate",
                    "name": "更新指标分类",
                    "enName": "indexCategoryUpdate",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2470916be515437c824a81008054982d",
                    "functionUuid": "2470916be515437c824a81008054982d",
                    "code": "deleteIndexVersion",
                    "name": "删除指标版本",
                    "enName": "deleteIndexVersion",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b2c537b3792f4cac9a5bb16283741e39",
                    "functionUuid": "b2c537b3792f4cac9a5bb16283741e39",
                    "code": "onlineIndexVersion",
                    "name": "指标版本上线",
                    "enName": "onlineIndexVersion",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8c82599a4f044f7c9a9b7c595b995990",
                    "functionUuid": "8c82599a4f044f7c9a9b7c595b995990",
                    "code": "updateIndexVersion",
                    "name": "更新指标版本",
                    "enName": "updateIndexVersion",
                    "sortNo": 1,
                    "menuUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "b1e251bcedca4af4b2757236ce3e27f4",
                "groupUuid": "b1e251bcedca4af4b2757236ce3e27f4",
                "groupIcon": "query",
                "menuName": "指标管理",
                "hasPermission": true
              },
              {
                "uuid": "199a8254c5734a82a661d48c2297e0b7",
                "code": "CP0104",
                "sortNo": 3,
                "level": 3,
                "type": 3,
                "path": "/params/indexpackage",
                "parentUuid": "fb74e7233e544c1fbdcb60f36e8a2ceb",
                "icon": "query",
                "name": "指标集管理",
                "enName": "Indicator Set Management",
                "originalName": "指标集管理",
                "originalEnName": "Indicator Set Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "caef6ee8222a45fbac1cbe7da55b7202",
                    "functionUuid": "caef6ee8222a45fbac1cbe7da55b7202",
                    "code": "createVersion",
                    "name": "创建版本",
                    "enName": "createVersion",
                    "sortNo": 0,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "378c4f3e9e994bcf902c8aa6056320a5",
                    "functionUuid": "378c4f3e9e994bcf902c8aa6056320a5",
                    "code": "deleteVersion",
                    "name": "删除版本",
                    "enName": "deleteVersion",
                    "sortNo": 0,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d8e52559c4ec41cb8aa24268f1452678",
                    "functionUuid": "d8e52559c4ec41cb8aa24268f1452678",
                    "code": "deleteIndex",
                    "name": "删除指标",
                    "enName": "deleteIndex",
                    "sortNo": 0,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "accc34eec4a745a9bd94f7f07f5012e6",
                    "functionUuid": "accc34eec4a745a9bd94f7f07f5012e6",
                    "code": "addIndex",
                    "name": "添加指标至指标集",
                    "enName": "addIndex",
                    "sortNo": 0,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2d0f563e68e74dedac8b2410a9ae1000",
                    "functionUuid": "2d0f563e68e74dedac8b2410a9ae1000",
                    "code": "update",
                    "name": "更新",
                    "enName": "update",
                    "sortNo": 0,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6e223dd54ad641ebac9e6c120d85de68",
                    "functionUuid": "6e223dd54ad641ebac9e6c120d85de68",
                    "code": "create",
                    "name": "创建",
                    "enName": "create",
                    "sortNo": 0,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0a4c5f814a1e434885c209dba15e5cf4",
                    "functionUuid": "0a4c5f814a1e434885c209dba15e5cf4",
                    "code": "onlineVersion",
                    "name": "上线版本",
                    "enName": "onlineVersion",
                    "sortNo": 0,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "efdf2e4d2435421c8666b73cb9d1079a",
                    "functionUuid": "efdf2e4d2435421c8666b73cb9d1079a",
                    "code": "delete",
                    "name": "删除",
                    "enName": "delete",
                    "sortNo": 0,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f86f64a6ae3e4d14834871d79f99ae5e",
                    "functionUuid": "f86f64a6ae3e4d14834871d79f99ae5e",
                    "code": "monitorableCancel",
                    "name": "指标集移除监控",
                    "enName": "monitorableCancel",
                    "sortNo": 1,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "0f7d7ca0a7124064adacab4750d0c841",
                    "functionUuid": "0f7d7ca0a7124064adacab4750d0c841",
                    "code": "monitorableAdd",
                    "name": "指标集添加监控",
                    "enName": "monitorableAdd",
                    "sortNo": 1,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "cfdd5a6c9ed84ef9897b0924fd48fd67",
                    "functionUuid": "cfdd5a6c9ed84ef9897b0924fd48fd67",
                    "code": "indexPackagePurposeEnable",
                    "name": "是否开启指标集用途配置",
                    "enName": "indexPackagePurposeEnable",
                    "sortNo": 1,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1fdede1009d8453092485a23e8d0d3ce",
                    "functionUuid": "1fdede1009d8453092485a23e8d0d3ce",
                    "code": "selectIndexPage",
                    "name": "已选指标分页",
                    "enName": "selectIndexPage",
                    "sortNo": 1,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d5c0df5a26c24335baff7f3ef20b3b47",
                    "functionUuid": "d5c0df5a26c24335baff7f3ef20b3b47",
                    "code": "indexList",
                    "name": "指标集指标列表",
                    "enName": "indexList",
                    "sortNo": 1,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b96dc7f7287d469aa661553b890741ed",
                    "functionUuid": "b96dc7f7287d469aa661553b890741ed",
                    "code": "configIndexPage",
                    "name": "配置指标列表",
                    "enName": "configIndexPage",
                    "sortNo": 1,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "dde23cdae58b45ba863c100d886985b6",
                    "functionUuid": "dde23cdae58b45ba863c100d886985b6",
                    "code": "packagelist",
                    "name": "指标集列表",
                    "enName": "package_list",
                    "sortNo": 1,
                    "menuUuid": "199a8254c5734a82a661d48c2297e0b7",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "199a8254c5734a82a661d48c2297e0b7",
                "groupUuid": "199a8254c5734a82a661d48c2297e0b7",
                "groupIcon": "query",
                "menuName": "指标集管理",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "fb74e7233e544c1fbdcb60f36e8a2ceb",
            "groupUuid": "fb74e7233e544c1fbdcb60f36e8a2ceb",
            "groupName": "指标管理",
            "groupIcon": "model",
            "hasPermission": true
          },
          {
            "uuid": "0d1ded92725e49d5883a8773a93b1e04",
            "code": "DataSourceConfig",
            "sortNo": 4,
            "level": 2,
            "type": 2,
            "parentUuid": "c52719956ed54658afd8009f56702de8",
            "icon": "entry",
            "name": "数据源配置",
            "enName": "Third party Data Management",
            "originalName": "数据源配置",
            "originalEnName": "Third party Data Management",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "b06a954019aa479999b51d40fcc79462",
                "code": "TZ0201",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/handle/supplierManagement/supplierList",
                "parentUuid": "0d1ded92725e49d5883a8773a93b1e04",
                "icon": "query",
                "name": "合作方管理",
                "enName": "Partner Management",
                "originalName": "合作方管理",
                "originalEnName": "Partner Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "df902198171a47d38e7f7a82f26dd331",
                    "functionUuid": "df902198171a47d38e7f7a82f26dd331",
                    "code": "delete",
                    "name": "合作商-删除",
                    "enName": "delete",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b07590355ecd46779ca5ee88c7ac6ee5",
                    "functionUuid": "b07590355ecd46779ca5ee88c7ac6ee5",
                    "code": "modify",
                    "name": "合作商-修改",
                    "enName": "modify",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "73870ffc69a54f9a82958bf9ce34e0ba",
                    "functionUuid": "73870ffc69a54f9a82958bf9ce34e0ba",
                    "code": "deleteContract",
                    "name": "合同-删除",
                    "enName": "deleteContract",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6d9dbb19be034c9fbe586c5684d1ebed",
                    "functionUuid": "6d9dbb19be034c9fbe586c5684d1ebed",
                    "code": "contract",
                    "name": "合同-查看",
                    "enName": "Contract Management",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "28883315dad34210a6db72583c8d7ae2",
                    "functionUuid": "28883315dad34210a6db72583c8d7ae2",
                    "code": "addContract",
                    "name": "合同-新增",
                    "enName": "addContract",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a26443d70b5745818a9924d7c57e3c98",
                    "functionUuid": "a26443d70b5745818a9924d7c57e3c98",
                    "code": "queryContractDetail",
                    "name": "合同-查询详情",
                    "enName": "queryContractDetail",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ff8107aa63724194a433cfa7d289491a",
                    "functionUuid": "ff8107aa63724194a433cfa7d289491a",
                    "code": "query",
                    "name": "合作商-查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4d3e29101b9e47e0ab5bdaded4a267bf",
                    "functionUuid": "4d3e29101b9e47e0ab5bdaded4a267bf",
                    "code": "updateContract",
                    "name": "合同-修改",
                    "enName": "updateContract",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f673db7e63d646518a3a0c89020ac7ae",
                    "functionUuid": "f673db7e63d646518a3a0c89020ac7ae",
                    "code": "add",
                    "name": "合作商-新增",
                    "enName": "add",
                    "sortNo": 0,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "636e9891554942a2ac4cd240be860c16",
                    "functionUuid": "636e9891554942a2ac4cd240be860c16",
                    "code": "look",
                    "name": "合作商-查看",
                    "enName": "look",
                    "sortNo": 1,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9db5dc3e47604c55acc5a502e4aa0aa2",
                    "functionUuid": "9db5dc3e47604c55acc5a502e4aa0aa2",
                    "code": "layoutRelationAnalysis",
                    "name": "服务编排引用关系",
                    "enName": "layoutRelationAnalysis",
                    "sortNo": 1,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "82ebed4bd852415c97c423f714959c22",
                    "functionUuid": "82ebed4bd852415c97c423f714959c22",
                    "code": "contractFileDownload",
                    "name": "合同-附件下载",
                    "enName": "contractFileDownload",
                    "sortNo": 1,
                    "menuUuid": "b06a954019aa479999b51d40fcc79462",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "b06a954019aa479999b51d40fcc79462",
                "groupUuid": "b06a954019aa479999b51d40fcc79462",
                "groupIcon": "query",
                "menuName": "合作方管理",
                "hasPermission": true
              },
              {
                "uuid": "bf9e57281b1c4275ba78fe82a84c9082",
                "code": "TZ0203",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/handle/supplierManagement/etl",
                "parentUuid": "0d1ded92725e49d5883a8773a93b1e04",
                "icon": "query",
                "name": "ETL处理器管理",
                "enName": "ETL Processor Management",
                "originalName": "ETL处理器管理",
                "originalEnName": "ETL Processor Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "65769e9d51b84a5e93c985934882f178",
                    "functionUuid": "65769e9d51b84a5e93c985934882f178",
                    "code": "import",
                    "name": "导入",
                    "enName": "import",
                    "sortNo": 0,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9d3cf6c375944bdaa948c91d2257e702",
                    "functionUuid": "9d3cf6c375944bdaa948c91d2257e702",
                    "code": "add",
                    "name": "新增",
                    "enName": "add",
                    "sortNo": 0,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2e9e2051881d4362959876ea8d51d733",
                    "functionUuid": "2e9e2051881d4362959876ea8d51d733",
                    "code": "modify",
                    "name": "修改",
                    "enName": "modify",
                    "sortNo": 0,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2c77cda11d354e7aaaf15bc9038079ff",
                    "functionUuid": "2c77cda11d354e7aaaf15bc9038079ff",
                    "code": "look",
                    "name": "查看",
                    "enName": "look",
                    "sortNo": 0,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8ce2d3660f514e74a90499eb0ecd651c",
                    "functionUuid": "8ce2d3660f514e74a90499eb0ecd651c",
                    "code": "export",
                    "name": "导出",
                    "enName": "export",
                    "sortNo": 0,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "417aed6cb4034614afeea333c8820709",
                    "functionUuid": "417aed6cb4034614afeea333c8820709",
                    "code": "delete",
                    "name": "删除",
                    "enName": "delete",
                    "sortNo": 0,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "33be7d924d0e4d7eaf2514e2f89ba60b",
                    "functionUuid": "33be7d924d0e4d7eaf2514e2f89ba60b",
                    "code": "etlonline",
                    "name": "批量上线",
                    "enName": "online",
                    "sortNo": 0,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7203816ae2874d6da405ea588764996e",
                    "functionUuid": "7203816ae2874d6da405ea588764996e",
                    "code": "query",
                    "name": "查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "baf9217eb34b4b45a9681041b10d7e29",
                    "functionUuid": "baf9217eb34b4b45a9681041b10d7e29",
                    "code": "versionDetail",
                    "name": "版本详情",
                    "enName": "versionDetail",
                    "sortNo": 1,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e27d559bedeb4f04a336f6e961bfeeed",
                    "functionUuid": "e27d559bedeb4f04a336f6e961bfeeed",
                    "code": "versionCover",
                    "name": "版本覆盖",
                    "enName": "versionCover",
                    "sortNo": 1,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "eda590cccfb74b59ad1ffb5b76f69fea",
                    "functionUuid": "eda590cccfb74b59ad1ffb5b76f69fea",
                    "code": "etlAdminAuth",
                    "name": "确认授权",
                    "enName": "etlAdminAuth",
                    "sortNo": 1,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ceb222efa846452ba019f28396522d07",
                    "functionUuid": "ceb222efa846452ba019f28396522d07",
                    "code": "etlAdminAuthQuery",
                    "name": "查看授权",
                    "enName": "etlAdminAuthQuery",
                    "sortNo": 1,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4bf28d7a8c194d0697a7eda4696b0a30",
                    "functionUuid": "4bf28d7a8c194d0697a7eda4696b0a30",
                    "code": "versionhistroy",
                    "name": "版本历史",
                    "enName": "versionhistroy",
                    "sortNo": 1,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "65671aad97a445118bd79172b6200f32",
                    "functionUuid": "65671aad97a445118bd79172b6200f32",
                    "code": "etloffline",
                    "name": "下线",
                    "enName": "etloffline",
                    "sortNo": 1,
                    "menuUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "bf9e57281b1c4275ba78fe82a84c9082",
                "groupUuid": "bf9e57281b1c4275ba78fe82a84c9082",
                "groupIcon": "query",
                "menuName": "ETL处理器管理",
                "hasPermission": true
              },
              {
                "uuid": "f59fe28d4a9646fb895824a4b979599f",
                "code": "TZ0202",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/handle/supplierManagement/dataServiceList",
                "parentUuid": "0d1ded92725e49d5883a8773a93b1e04",
                "icon": "query",
                "name": "数据源服务接口管理",
                "enName": "Data Source Service Management",
                "originalName": "数据源服务接口管理",
                "originalEnName": "Data Source Service Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "b3726ab99e684a469741c4546d85fba1",
                    "functionUuid": "b3726ab99e684a469741c4546d85fba1",
                    "code": "copy",
                    "name": "复制",
                    "enName": "copy",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7b977ee54c854d9b9907453606bb60ce",
                    "functionUuid": "7b977ee54c854d9b9907453606bb60ce",
                    "code": "add",
                    "name": "新增",
                    "enName": "add",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "fd69d03dba2b4d588c40c8c4ab1c1d1b",
                    "functionUuid": "fd69d03dba2b4d588c40c8c4ab1c1d1b",
                    "code": "modify",
                    "name": "修改",
                    "enName": "modify",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "026d9d4d1b724135b46ef75df262b51d",
                    "functionUuid": "026d9d4d1b724135b46ef75df262b51d",
                    "code": "saveSetting",
                    "name": "保存设置",
                    "enName": "saveSetting",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d1c31ffbb8cc4316a84f39fb13f60161",
                    "functionUuid": "d1c31ffbb8cc4316a84f39fb13f60161",
                    "code": "mockConfig",
                    "name": "查询Mock数据",
                    "enName": "Mock Config",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "45e0b8f32c8c4a7ba6f2cff4ab13cac4",
                    "functionUuid": "45e0b8f32c8c4a7ba6f2cff4ab13cac4",
                    "code": "test",
                    "name": "测试",
                    "enName": "Service Test",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "12b61c2fcdaa4f229925e032f6bec655",
                    "functionUuid": "12b61c2fcdaa4f229925e032f6bec655",
                    "code": "delete",
                    "name": "删除",
                    "enName": "delete",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "31c9205ba9cb42909ac9feb267e6cad5",
                    "functionUuid": "31c9205ba9cb42909ac9feb267e6cad5",
                    "code": "query",
                    "name": "查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "98bbd290837a4b098622f9d63d71eb66",
                    "functionUuid": "98bbd290837a4b098622f9d63d71eb66",
                    "code": "online",
                    "name": "启用/禁用",
                    "enName": "online",
                    "sortNo": 0,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "dd3a8cb3d3a946e798c7842de4cc37a0",
                    "functionUuid": "dd3a8cb3d3a946e798c7842de4cc37a0",
                    "code": "updateMockData",
                    "name": "更新Mock数据(单条)",
                    "enName": "update Mock Data",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e885bd8d4a444af3b63cd5809e2da40b",
                    "functionUuid": "e885bd8d4a444af3b63cd5809e2da40b",
                    "code": "dataServiceListAuthQuery",
                    "name": "查看授权",
                    "enName": "dataServiceListAuthQuery",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "cbd28386dc314853885b33e75001c6f0",
                    "functionUuid": "cbd28386dc314853885b33e75001c6f0",
                    "code": "deleteMockData",
                    "name": "删除Mock数据",
                    "enName": "delete Mock Data",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "738ad3aa7289430399367708ce3b79fe",
                    "functionUuid": "738ad3aa7289430399367708ce3b79fe",
                    "code": "exportMockDataTemplate",
                    "name": "获取Mock数据模板",
                    "enName": "export Mock Data Template",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5f2fd61f4fb44d5195fd8e9c1277846d",
                    "functionUuid": "5f2fd61f4fb44d5195fd8e9c1277846d",
                    "code": "exportServiceConfig",
                    "name": "导出",
                    "enName": "export ServiceConfig",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "65762758caf64312be737926b8d25331",
                    "functionUuid": "65762758caf64312be737926b8d25331",
                    "code": "importMockData",
                    "name": "导入Mock数据",
                    "enName": "import Mock Data",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "dc6d77ca9e8948b8afd20747c199e36f",
                    "functionUuid": "dc6d77ca9e8948b8afd20747c199e36f",
                    "code": "dataServiceListAuth",
                    "name": "确认授权",
                    "enName": "dataServiceListAuth",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8ce1a48bb2ce40508a8755bf57ac7721",
                    "functionUuid": "8ce1a48bb2ce40508a8755bf57ac7721",
                    "code": "serviceConfigGetById",
                    "name": "查询详情",
                    "enName": "service Config GetById",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e429c22a87e748e19d6e044d3adb4ac5",
                    "functionUuid": "e429c22a87e748e19d6e044d3adb4ac5",
                    "code": "look",
                    "name": "查看",
                    "enName": "look",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f53c78db04734422a0b831f483622178",
                    "functionUuid": "f53c78db04734422a0b831f483622178",
                    "code": "mockConfigData",
                    "name": "新增Mock数据(单条)",
                    "enName": "mock Config Data",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8d6dec36636146168e77bd9a930a3a68",
                    "functionUuid": "8d6dec36636146168e77bd9a930a3a68",
                    "code": "importServiceConfig",
                    "name": "导入",
                    "enName": "import ServiceConfig",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "fb3a0130251d41b38442aac02d5ebb03",
                    "functionUuid": "fb3a0130251d41b38442aac02d5ebb03",
                    "code": "mockConfigUpdate",
                    "name": "更新Mock配置",
                    "enName": "mock Config Update",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "3a5d60f9e4c24eeb907f94e8b433368a",
                    "functionUuid": "3a5d60f9e4c24eeb907f94e8b433368a",
                    "code": "deleteEffectContract",
                    "name": "删除延迟合同",
                    "enName": "delete effect contract",
                    "sortNo": 1,
                    "menuUuid": "f59fe28d4a9646fb895824a4b979599f",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "f59fe28d4a9646fb895824a4b979599f",
                "groupUuid": "f59fe28d4a9646fb895824a4b979599f",
                "groupIcon": "query",
                "menuName": "数据源服务接口管理",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "0d1ded92725e49d5883a8773a93b1e04",
            "groupUuid": "0d1ded92725e49d5883a8773a93b1e04",
            "groupName": "数据源配置",
            "groupIcon": "entry",
            "hasPermission": true
          },
          {
            "uuid": "c61bcdc9551140f69abbfdc4c27cf441",
            "code": "zbsp",
            "sortNo": 5,
            "level": 2,
            "type": 2,
            "parentUuid": "c52719956ed54658afd8009f56702de8",
            "icon": "role",
            "name": "指标审批",
            "enName": "Indicator Approval",
            "originalName": "指标审批",
            "originalEnName": "Indicator Approval",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "1bff36d449f34c029eb10435c8c8ef5c",
                "code": "AflowMyApproval",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/aflow/myApproval",
                "parentUuid": "c61bcdc9551140f69abbfdc4c27cf441",
                "name": "我的审批",
                "enName": "My Approvals",
                "originalName": "我的审批",
                "originalEnName": "My Approvals",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.0.0",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [],
                "id": "1bff36d449f34c029eb10435c8c8ef5c",
                "groupUuid": "1bff36d449f34c029eb10435c8c8ef5c",
                "menuName": "我的审批",
                "hasPermission": true
              },
              {
                "uuid": "5adba75a13624611aaed1b2e87bdcd2f",
                "code": "AflowMyNotify",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/aflow/myNotify",
                "parentUuid": "c61bcdc9551140f69abbfdc4c27cf441",
                "name": "我的知会",
                "enName": "My Notifications",
                "originalName": "我的知会",
                "originalEnName": "My Notifications",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.0.0",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [],
                "id": "5adba75a13624611aaed1b2e87bdcd2f",
                "groupUuid": "5adba75a13624611aaed1b2e87bdcd2f",
                "menuName": "我的知会",
                "hasPermission": true
              },
              {
                "uuid": "8ef90f77acca410aa0cf7b17e15b07de",
                "code": "AflowAllApproval",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/aflow/allApproval",
                "parentUuid": "c61bcdc9551140f69abbfdc4c27cf441",
                "name": "全部审批",
                "enName": "All Approvals",
                "originalName": "全部审批",
                "originalEnName": "All Approvals",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.0.0",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [],
                "id": "8ef90f77acca410aa0cf7b17e15b07de",
                "groupUuid": "8ef90f77acca410aa0cf7b17e15b07de",
                "menuName": "全部审批",
                "hasPermission": true
              },
              {
                "uuid": "1b72345e0c0e47aaa68c18286914365a",
                "code": "AflowExceptionApproval",
                "sortNo": 3,
                "level": 3,
                "type": 3,
                "path": "/aflow/exceptionApproval",
                "parentUuid": "c61bcdc9551140f69abbfdc4c27cf441",
                "name": "异常审批",
                "enName": "Exception Approvals",
                "originalName": "异常审批",
                "originalEnName": "Exception Approvals",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.0.0",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [],
                "id": "1b72345e0c0e47aaa68c18286914365a",
                "groupUuid": "1b72345e0c0e47aaa68c18286914365a",
                "menuName": "异常审批",
                "hasPermission": true
              },
              {
                "uuid": "2641afdf73384078af12488cb29bafe5",
                "code": "AflowTemplate",
                "sortNo": 4,
                "level": 3,
                "type": 3,
                "path": "/aflow/flow",
                "parentUuid": "c61bcdc9551140f69abbfdc4c27cf441",
                "name": "审批流模版",
                "enName": "Approval Template",
                "originalName": "审批流模版",
                "originalEnName": "Approval Template",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.0.0",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [],
                "id": "2641afdf73384078af12488cb29bafe5",
                "groupUuid": "2641afdf73384078af12488cb29bafe5",
                "menuName": "审批流模版",
                "hasPermission": true
              },
              {
                "uuid": "6352e33fadd24230ab131ea940fc588a",
                "code": "AflowForm",
                "sortNo": 5,
                "level": 3,
                "type": 3,
                "path": "/aflow/form",
                "parentUuid": "c61bcdc9551140f69abbfdc4c27cf441",
                "name": "审批流表单",
                "enName": "Approval Form",
                "originalName": "审批流表单",
                "originalEnName": "Approval Form",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.0.0",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [],
                "id": "6352e33fadd24230ab131ea940fc588a",
                "groupUuid": "6352e33fadd24230ab131ea940fc588a",
                "menuName": "审批流表单",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "c61bcdc9551140f69abbfdc4c27cf441",
            "groupUuid": "c61bcdc9551140f69abbfdc4c27cf441",
            "groupName": "指标审批",
            "groupIcon": "role",
            "hasPermission": true
          },
          {
            "uuid": "961885a3b9ed4c1987b690fa0ac779d2",
            "code": "apilayoutdomain",
            "sortNo": 6,
            "level": 2,
            "type": 2,
            "parentUuid": "c52719956ed54658afd8009f56702de8",
            "icon": "org",
            "name": "服务编排",
            "enName": "Service Orchestration",
            "originalName": "服务编排",
            "originalEnName": "Service Orchestration",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                "code": "layoutFunctionLibrary",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/handle/formula",
                "parentUuid": "961885a3b9ed4c1987b690fa0ac779d2",
                "name": "脚本函数",
                "enName": "Script Function",
                "originalName": "脚本函数",
                "originalEnName": "Script Function",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "5d0b568a187940e983b020754e0190a8",
                    "functionUuid": "5d0b568a187940e983b020754e0190a8",
                    "code": "FunctionRelation",
                    "name": "引用关系",
                    "enName": "functionRelation",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a7b1657b55644d4ab8ed7f35343d249c",
                    "functionUuid": "a7b1657b55644d4ab8ed7f35343d249c",
                    "code": "PreInvoke",
                    "name": "测试",
                    "enName": "PreInvoke",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "38eae8a5b4e941f382b0fd5ec164640d",
                    "functionUuid": "38eae8a5b4e941f382b0fd5ec164640d",
                    "code": "Import",
                    "name": "导入",
                    "enName": "Import",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "03ea3cbc699b44fd973d316a24f4f5cc",
                    "functionUuid": "03ea3cbc699b44fd973d316a24f4f5cc",
                    "code": "Online",
                    "name": "上线",
                    "enName": "Online",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "fb949bdfdaf744e3a5409c17295f1f4f",
                    "functionUuid": "fb949bdfdaf744e3a5409c17295f1f4f",
                    "code": "Authorization",
                    "name": "授权",
                    "enName": "Authorization",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e275961903d940ab92f8992579891fc6",
                    "functionUuid": "e275961903d940ab92f8992579891fc6",
                    "code": "Update",
                    "name": "修改",
                    "enName": "Update",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b119a883e429474fa0ca0082fca0806a",
                    "functionUuid": "b119a883e429474fa0ca0082fca0806a",
                    "code": "formulaAuthQuery",
                    "name": "查看授权",
                    "enName": "formulaAuthQuery",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "41c3cd7267424737a67bac477d7546bf",
                    "functionUuid": "41c3cd7267424737a67bac477d7546bf",
                    "code": "VersionHistory",
                    "name": "查看版本",
                    "enName": "VersionHistory",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d6e1bb2fe7db491fbb4542de4464ad45",
                    "functionUuid": "d6e1bb2fe7db491fbb4542de4464ad45",
                    "code": "Add",
                    "name": "创建",
                    "enName": "Add",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ee00278e13fe4ba7941312641bdb275e",
                    "functionUuid": "ee00278e13fe4ba7941312641bdb275e",
                    "code": "Copy",
                    "name": "复制",
                    "enName": "Copy",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b81b89f7e3cb47609eb74829972f47f3",
                    "functionUuid": "b81b89f7e3cb47609eb74829972f47f3",
                    "code": "Delete",
                    "name": "删除",
                    "enName": "Delete",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "985f2b519e604405824b9060f0df289d",
                    "functionUuid": "985f2b519e604405824b9060f0df289d",
                    "code": "Export",
                    "name": "导出",
                    "enName": "Export",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d133e39e2e0547d59dc73439c884012b",
                    "functionUuid": "d133e39e2e0547d59dc73439c884012b",
                    "code": "Offline",
                    "name": "下线",
                    "enName": "Offline",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9a6f90c918c943af947d999fd9121c9f",
                    "functionUuid": "9a6f90c918c943af947d999fd9121c9f",
                    "code": "Query",
                    "name": "查询",
                    "enName": "Query",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "42b4466a795d49ffbd49dcb8c7289681",
                    "functionUuid": "42b4466a795d49ffbd49dcb8c7289681",
                    "code": "Cover",
                    "name": "应用",
                    "enName": "Cover",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5cb1595fa0194aabadc7ba256c83e8e7",
                    "functionUuid": "5cb1595fa0194aabadc7ba256c83e8e7",
                    "code": "formulaAuth",
                    "name": "确认授权",
                    "enName": "formulaAuth",
                    "sortNo": 1,
                    "menuUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "5fdb4d0568f04cf8afdceb8a948a703d",
                "groupUuid": "5fdb4d0568f04cf8afdceb8a948a703d",
                "menuName": "脚本函数",
                "hasPermission": true
              },
              {
                "uuid": "b86554c033bb46b58e3d8d51314fd617",
                "code": "subWorkflow",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/handle/workflow/sub",
                "parentUuid": "961885a3b9ed4c1987b690fa0ac779d2",
                "name": "流程模版",
                "enName": "Process Template",
                "originalName": "流程模版",
                "originalEnName": "Process Template",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "904fa7db406f43a9a1642dbf274ff792",
                    "functionUuid": "904fa7db406f43a9a1642dbf274ff792",
                    "code": "WorkflowTemplateAuthQuery",
                    "name": "查看授权",
                    "enName": "WorkflowTemplateAuthQuery",
                    "sortNo": 1,
                    "menuUuid": "b86554c033bb46b58e3d8d51314fd617",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f2bf188f7d5f4934a79fba3fdc6cdb35",
                    "functionUuid": "f2bf188f7d5f4934a79fba3fdc6cdb35",
                    "code": "workflowTemplatePage",
                    "name": "流程模版列表",
                    "enName": "workflowTemplatePage",
                    "sortNo": 1,
                    "menuUuid": "b86554c033bb46b58e3d8d51314fd617",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d2c2077b528541cbb1e3f586231e6644",
                    "functionUuid": "d2c2077b528541cbb1e3f586231e6644",
                    "code": "workflowTemplateDetail",
                    "name": "流程模版详情",
                    "enName": "workflowTemplateDetail",
                    "sortNo": 1,
                    "menuUuid": "b86554c033bb46b58e3d8d51314fd617",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "95e164330a924f4c813aed787af672dc",
                    "functionUuid": "95e164330a924f4c813aed787af672dc",
                    "code": "WorkflowTemplateAuth",
                    "name": "确认授权",
                    "enName": "WorkflowTemplateAuth",
                    "sortNo": 1,
                    "menuUuid": "b86554c033bb46b58e3d8d51314fd617",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "cf713b5e489d417bbb768ae76e0e89ec",
                    "functionUuid": "cf713b5e489d417bbb768ae76e0e89ec",
                    "code": "workflowTemplateAdd",
                    "name": "新增流程模版",
                    "enName": "workflowTemplateAdd",
                    "sortNo": 1,
                    "menuUuid": "b86554c033bb46b58e3d8d51314fd617",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "83498813e2d64c6686f73aac408e0560",
                    "functionUuid": "83498813e2d64c6686f73aac408e0560",
                    "code": "workflowTemplateUpdate",
                    "name": "修改流程模版",
                    "enName": "workflowTemplateUpdate",
                    "sortNo": 1,
                    "menuUuid": "b86554c033bb46b58e3d8d51314fd617",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b52e9ae805ba427681b8d01df4829f7a",
                    "functionUuid": "b52e9ae805ba427681b8d01df4829f7a",
                    "code": "workflowTemplateDelete",
                    "name": "删除流程模版",
                    "enName": "workflowTemplateDelete",
                    "sortNo": 1,
                    "menuUuid": "b86554c033bb46b58e3d8d51314fd617",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "b86554c033bb46b58e3d8d51314fd617",
                "groupUuid": "b86554c033bb46b58e3d8d51314fd617",
                "menuName": "流程模版",
                "hasPermission": true
              },
              {
                "uuid": "455289dfc33a487bb8821cf8b9ed36dc",
                "code": "workflowArrange",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/handle/workflow/arrange",
                "parentUuid": "961885a3b9ed4c1987b690fa0ac779d2",
                "name": "服务编排",
                "enName": "Service Orchestration",
                "originalName": "服务编排",
                "originalEnName": "Service Orchestration",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "9f7e4ae0a98544f9a21c77e9ee35b293",
                    "functionUuid": "9f7e4ae0a98544f9a21c77e9ee35b293",
                    "code": "workflowVersionCover",
                    "name": "工作流版本覆盖",
                    "enName": "workflowVersionCover",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "3477be4bddeb4ea1a82833b8c53a2cb2",
                    "functionUuid": "3477be4bddeb4ea1a82833b8c53a2cb2",
                    "code": "workflowOffline",
                    "name": "工作流下线",
                    "enName": "workflowOffline",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a2ace501a3674fbfbb4d8c17a6f61d74",
                    "functionUuid": "a2ace501a3674fbfbb4d8c17a6f61d74",
                    "code": "workflowDetail",
                    "name": "工作流详情",
                    "enName": "workflowDetail",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e2b15e28a3414ac396e705f30b6b0111",
                    "functionUuid": "e2b15e28a3414ac396e705f30b6b0111",
                    "code": "workflowArrangeAuth",
                    "name": "确认授权",
                    "enName": "workflowArrangeAuth",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "379403c861f74c67a042dc120136bbc7",
                    "functionUuid": "379403c861f74c67a042dc120136bbc7",
                    "code": "workflowPublish",
                    "name": "工作流上线",
                    "enName": "workflowPublish",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6439c001debf45d0af7035fbf61421b3",
                    "functionUuid": "6439c001debf45d0af7035fbf61421b3",
                    "code": "workflowCopy",
                    "name": "复制工作流",
                    "enName": "workflowCopy",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2088877283cc4e088c0beb19497fd9f5",
                    "functionUuid": "2088877283cc4e088c0beb19497fd9f5",
                    "code": "workflowArrangeAuthQuery",
                    "name": "查看授权",
                    "enName": "workflowArrangeAuthQuery",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ff3db1cb36a0453f94fd393d4f7a4c00",
                    "functionUuid": "ff3db1cb36a0453f94fd393d4f7a4c00",
                    "code": "workflowExport",
                    "name": "工作流导出",
                    "enName": "workflowExport",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6d815c9509c14be58a5c92fc88974d07",
                    "functionUuid": "6d815c9509c14be58a5c92fc88974d07",
                    "code": "workflowImport",
                    "name": "工作流导入",
                    "enName": "workflowImport",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b21bf9409c7247f4b7cf2b7fbe1602c6",
                    "functionUuid": "b21bf9409c7247f4b7cf2b7fbe1602c6",
                    "code": "workflowVersionPage",
                    "name": "工作流版本列表",
                    "enName": "workflowVersionPage",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "fa6859c73f724fc29e0f0384bb6ac59d",
                    "functionUuid": "fa6859c73f724fc29e0f0384bb6ac59d",
                    "code": "updateWorkflow",
                    "name": "修改工作流",
                    "enName": "updateWorkflow",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4bccec8e40884136a25ef1e0899ca3ac",
                    "functionUuid": "4bccec8e40884136a25ef1e0899ca3ac",
                    "code": "deleteWorkflow",
                    "name": "删除工作流",
                    "enName": "deleteWorkflow",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8a6301735f884e6980582039f91301fc",
                    "functionUuid": "8a6301735f884e6980582039f91301fc",
                    "code": "addWorkflow",
                    "name": "新增工作流",
                    "enName": "addWorkflow",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8034d719406344eab3a18f191628c8e8",
                    "functionUuid": "8034d719406344eab3a18f191628c8e8",
                    "code": "workflowVersionDetail",
                    "name": "工作流版本详情",
                    "enName": "workflowVersionDetail",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1796df529e834ecab81b98e29bc5422d",
                    "functionUuid": "1796df529e834ecab81b98e29bc5422d",
                    "code": "workflowPage",
                    "name": "工作流列表",
                    "enName": "workflowPage",
                    "sortNo": 1,
                    "menuUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "455289dfc33a487bb8821cf8b9ed36dc",
                "groupUuid": "455289dfc33a487bb8821cf8b9ed36dc",
                "menuName": "服务编排",
                "hasPermission": true
              },
              {
                "uuid": "0a48592e385b4ece88cdb2fa3dd34128",
                "code": "serviceInterfaceManage",
                "sortNo": 3,
                "level": 3,
                "type": 3,
                "path": "/handle/interface/management",
                "parentUuid": "961885a3b9ed4c1987b690fa0ac779d2",
                "name": "服务接口",
                "enName": "Service Interface",
                "originalName": "服务接口",
                "originalEnName": "Service Interface",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "0b2e099acfb7453dae0fde2c5a0e6f62",
                    "functionUuid": "0b2e099acfb7453dae0fde2c5a0e6f62",
                    "code": "serviceInterfaceDetail",
                    "name": "查看服务接口",
                    "enName": "serviceInterfaceDetail",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "67b181e71fe24e5cbeaa7e75259908d9",
                    "functionUuid": "67b181e71fe24e5cbeaa7e75259908d9",
                    "code": "serviceInterfaceAdd",
                    "name": "新增服务接口",
                    "enName": "serviceInterfaceAdd",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ac6de67654cf42ab8c051b2025148db2",
                    "functionUuid": "ac6de67654cf42ab8c051b2025148db2",
                    "code": "serviceInterfacePage",
                    "name": "服务接口列表",
                    "enName": "serviceInterfacePage",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e261749075a34bfea15456502f97c6c4",
                    "functionUuid": "e261749075a34bfea15456502f97c6c4",
                    "code": "ServiceInterfaceAuth",
                    "name": "确认授权",
                    "enName": "ServiceInterfaceAuth",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "40626cfcd44d48aca1acc3dcf1e0c896",
                    "functionUuid": "40626cfcd44d48aca1acc3dcf1e0c896",
                    "code": "serviceInterfaceDelete",
                    "name": "删除服务接口",
                    "enName": "serviceInterfaceDelete",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "151ae511b18f4f10aa77c7b5d26cc19e",
                    "functionUuid": "151ae511b18f4f10aa77c7b5d26cc19e",
                    "code": "serviceInterfaceUpdate",
                    "name": "修改服务接口",
                    "enName": "serviceInterfaceUpdate",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "58417f377aa542468a7ba9614e2b5757",
                    "functionUuid": "58417f377aa542468a7ba9614e2b5757",
                    "code": "testInterface",
                    "name": "测试服务接口",
                    "enName": "testInterface",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1736f51177b3460bb48f638f5c5b1e14",
                    "functionUuid": "1736f51177b3460bb48f638f5c5b1e14",
                    "code": "ServiceInterfaceExport",
                    "name": "导出",
                    "enName": "ServiceInterfaceExport",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7d3041d4ab194e4f8f7b81dd757452a9",
                    "functionUuid": "7d3041d4ab194e4f8f7b81dd757452a9",
                    "code": "serviceInterfaceDownload",
                    "name": "下载服务接口",
                    "enName": "serviceInterfaceDownload",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "db7f3b03622e479c9640dc31a864f6cb",
                    "functionUuid": "db7f3b03622e479c9640dc31a864f6cb",
                    "code": "serviceInterfaceStatus",
                    "name": "修改服务接口状态",
                    "enName": "serviceInterfaceStatus",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6d5c938e915242a18390c4a6efa8b4fe",
                    "functionUuid": "6d5c938e915242a18390c4a6efa8b4fe",
                    "code": "ServiceInterfaceAuthQuery",
                    "name": "查看授权",
                    "enName": "ServiceInterfaceAuthQuery",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "47afa605b60b4253905af0854f15688e",
                    "functionUuid": "47afa605b60b4253905af0854f15688e",
                    "code": "ServiceInterfaceImport",
                    "name": "导入",
                    "enName": "ServiceInterfaceImport",
                    "sortNo": 1,
                    "menuUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "0a48592e385b4ece88cdb2fa3dd34128",
                "groupUuid": "0a48592e385b4ece88cdb2fa3dd34128",
                "menuName": "服务接口",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "961885a3b9ed4c1987b690fa0ac779d2",
            "groupUuid": "961885a3b9ed4c1987b690fa0ac779d2",
            "groupName": "服务编排",
            "groupIcon": "org",
            "hasPermission": true
          }
        ],
        "functionList": [],
        "id": "c52719956ed54658afd8009f56702de8",
        "groupUuid": "c52719956ed54658afd8009f56702de8",
        "groupName": "数据接入",
        "groupIcon": "workflow",
        "hasPermission": false
      },
      {
        "uuid": "e8db394c641449c081e449a8ddb0d52f",
        "code": "DataQuery",
        "sortNo": 2,
        "level": 1,
        "type": 2,
        "parentUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "icon": "plateau",
        "name": "数据查询",
        "enName": "Data Query",
        "originalName": "数据查询",
        "originalEnName": "Data Query",
        "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "createBy": "csb",
        "updateBy": "csb",
        "gmtCreate": *************,
        "gmtModified": *************,
        "children": [
          {
            "uuid": "14205d7c96b34196bacbdfcb97e7bc34",
            "code": "InvokeData",
            "sortNo": 0,
            "level": 2,
            "type": 2,
            "parentUuid": "e8db394c641449c081e449a8ddb0d52f",
            "icon": "check",
            "name": "数据调用明细",
            "enName": "Call Details",
            "originalName": "数据调用明细",
            "originalEnName": "Call Details",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "46a19c608eeb43e482a27e185cadfc67",
                "code": "TZ0501",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/handle/dataManagement/threeCallDetail",
                "parentUuid": "14205d7c96b34196bacbdfcb97e7bc34",
                "icon": "query",
                "name": "数据源调用明细",
                "enName": "Data Source Call Details",
                "originalName": "数据源调用明细",
                "originalEnName": "Data Source Call Details",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "ff20e234eafa435bbff11b0babd19fc6",
                    "functionUuid": "ff20e234eafa435bbff11b0babd19fc6",
                    "code": "query",
                    "name": "查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "46a19c608eeb43e482a27e185cadfc67",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "928f083bbc17408f907dac2a3f7e20ad",
                    "functionUuid": "928f083bbc17408f907dac2a3f7e20ad",
                    "code": "queryServiceDetail",
                    "name": "查看明细",
                    "enName": "query service detail",
                    "sortNo": 1,
                    "menuUuid": "46a19c608eeb43e482a27e185cadfc67",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "46a19c608eeb43e482a27e185cadfc67",
                "groupUuid": "46a19c608eeb43e482a27e185cadfc67",
                "groupIcon": "query",
                "menuName": "数据源调用明细",
                "hasPermission": true
              },
              {
                "uuid": "4b05f42e67cc441790c27136c108b02c",
                "code": "fwdymx88607",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/handle/callerMonitoring/serviceInterfaceCallDetail",
                "parentUuid": "14205d7c96b34196bacbdfcb97e7bc34",
                "name": "服务调用明细",
                "enName": "Service Call Details",
                "originalName": "服务调用明细",
                "originalEnName": "Service Call Details",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "1.1.3",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [],
                "id": "4b05f42e67cc441790c27136c108b02c",
                "groupUuid": "4b05f42e67cc441790c27136c108b02c",
                "menuName": "服务调用明细",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "14205d7c96b34196bacbdfcb97e7bc34",
            "groupUuid": "14205d7c96b34196bacbdfcb97e7bc34",
            "groupName": "数据调用明细",
            "groupIcon": "check",
            "hasPermission": true
          },
          {
            "uuid": "f0e78ae2b0f3468aad9555c02a70f9f9",
            "code": "indexQuery",
            "sortNo": 1,
            "level": 2,
            "type": 2,
            "parentUuid": "e8db394c641449c081e449a8ddb0d52f",
            "icon": "tongji",
            "name": "指标结果查询",
            "enName": "Indicator Result",
            "originalName": "指标结果查询",
            "originalEnName": "Indicator Result",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "af844964dcdc478284bfed8695d47bda",
                "code": "indicatorReport",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/params/indicatorReport",
                "parentUuid": "f0e78ae2b0f3468aad9555c02a70f9f9",
                "name": "指标结果查询",
                "enName": "Indicator Results Query",
                "originalName": "指标结果查询",
                "originalEnName": "Indicator Results Query",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "4a1b0e01300f4dc0b7cd33bfbec07e69",
                    "functionUuid": "4a1b0e01300f4dc0b7cd33bfbec07e69",
                    "code": "indexResultDetail",
                    "name": "指标报告详情",
                    "enName": "indexResultDetail",
                    "sortNo": 1,
                    "menuUuid": "af844964dcdc478284bfed8695d47bda",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "53d01536182f4002ae6f78d7569ae6cd",
                    "functionUuid": "53d01536182f4002ae6f78d7569ae6cd",
                    "code": "indexResultExport",
                    "name": "指标报告导出",
                    "enName": "indexResultExport",
                    "sortNo": 1,
                    "menuUuid": "af844964dcdc478284bfed8695d47bda",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "679d50eddf7d4da998d2c989375f5b7f",
                    "functionUuid": "679d50eddf7d4da998d2c989375f5b7f",
                    "code": "indexResultQuery",
                    "name": "指标报告列表查询",
                    "enName": "indexResultQuery",
                    "sortNo": 1,
                    "menuUuid": "af844964dcdc478284bfed8695d47bda",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "af844964dcdc478284bfed8695d47bda",
                "groupUuid": "af844964dcdc478284bfed8695d47bda",
                "menuName": "指标结果查询",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "f0e78ae2b0f3468aad9555c02a70f9f9",
            "groupUuid": "f0e78ae2b0f3468aad9555c02a70f9f9",
            "groupName": "指标结果查询",
            "groupIcon": "tongji",
            "hasPermission": true
          },
          {
            "uuid": "1c2b4a64d8424fad8762309753eb45af",
            "code": "ManuInvoke",
            "sortNo": 2,
            "level": 2,
            "type": 2,
            "parentUuid": "e8db394c641449c081e449a8ddb0d52f",
            "icon": "permission",
            "name": "手工查询",
            "enName": "Manual Query",
            "originalName": "手工查询",
            "originalEnName": "Manual Query",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "f928bba4d7444a2b87b24d22b87d753e",
                "code": "TZ0504",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/handle/dataManagement/pageQuery",
                "parentUuid": "1c2b4a64d8424fad8762309753eb45af",
                "icon": "query",
                "name": "手工查询",
                "enName": "Manual Query",
                "originalName": "手工查询",
                "originalEnName": "Manual Query",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "43ca204650ea4df7abdb38916d1a5785",
                    "functionUuid": "43ca204650ea4df7abdb38916d1a5785",
                    "code": "batchQuery",
                    "name": "批量查询",
                    "enName": "batchQuery",
                    "sortNo": 0,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9b10e782e18b4be7bab5c785307c395b",
                    "functionUuid": "9b10e782e18b4be7bab5c785307c395b",
                    "code": "singleQuery",
                    "name": "单条查询",
                    "enName": "singleQuery",
                    "sortNo": 0,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2bb782e075404b92be30a93714acdfc5",
                    "functionUuid": "2bb782e075404b92be30a93714acdfc5",
                    "code": "import",
                    "name": "上传数据",
                    "enName": "import",
                    "sortNo": 0,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f0d9c5754b84470ba9c663960ea0b353",
                    "functionUuid": "f0d9c5754b84470ba9c663960ea0b353",
                    "code": "exportServiceInvokeDetail",
                    "name": "下载批量查询明细",
                    "enName": "export ServiceInvoke Detail",
                    "sortNo": 1,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "914ef26c5f9c44efbb0e142a16000e48",
                    "functionUuid": "914ef26c5f9c44efbb0e142a16000e48",
                    "code": "batchList",
                    "name": "获取批次列表",
                    "enName": "batch List",
                    "sortNo": 1,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "164f047bc7e749a0963b9ca7c3cd74ff",
                    "functionUuid": "164f047bc7e749a0963b9ca7c3cd74ff",
                    "code": "submitInvoke",
                    "name": "批次执行查询",
                    "enName": "submitInvoke",
                    "sortNo": 1,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7c35ffc8624e4ab8bfc97ca32f7e968e",
                    "functionUuid": "7c35ffc8624e4ab8bfc97ca32f7e968e",
                    "code": "exportServiceInvokeTemplate",
                    "name": "下载批量异步查询模板",
                    "enName": "export ServiceInvoke Template",
                    "sortNo": 1,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7d36ea4ebaf4406e950f6af5e1487190",
                    "functionUuid": "7d36ea4ebaf4406e950f6af5e1487190",
                    "code": "getBatchDetail",
                    "name": "获取批次详情",
                    "enName": "batch Detail",
                    "sortNo": 1,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "98ed7be3aa014444875212a1189fe6df",
                    "functionUuid": "98ed7be3aa014444875212a1189fe6df",
                    "code": "batchDetails",
                    "name": "获取批次明细",
                    "enName": "batch Details",
                    "sortNo": 1,
                    "menuUuid": "f928bba4d7444a2b87b24d22b87d753e",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "f928bba4d7444a2b87b24d22b87d753e",
                "groupUuid": "f928bba4d7444a2b87b24d22b87d753e",
                "groupIcon": "query",
                "menuName": "手工查询",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "1c2b4a64d8424fad8762309753eb45af",
            "groupUuid": "1c2b4a64d8424fad8762309753eb45af",
            "groupName": "手工查询",
            "groupIcon": "permission",
            "hasPermission": true
          }
        ],
        "functionList": [],
        "id": "e8db394c641449c081e449a8ddb0d52f",
        "groupUuid": "e8db394c641449c081e449a8ddb0d52f",
        "groupName": "数据查询",
        "groupIcon": "plateau",
        "hasPermission": false
      },
      {
        "uuid": "45487c6e532f450a82a3f39196beb5b1",
        "code": "MonitorConfig",
        "sortNo": 3,
        "level": 1,
        "type": 2,
        "parentUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "icon": "security",
        "name": "监控管理",
        "enName": "Monitoring Management",
        "originalName": "监控管理",
        "originalEnName": "Monitoring Management",
        "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "createBy": "csb",
        "updateBy": "csb",
        "gmtCreate": *************,
        "gmtModified": *************,
        "children": [
          {
            "uuid": "5fccb80b15d742eaabe05e608ed29f88",
            "code": "DataSourceMonitor",
            "sortNo": 0,
            "level": 2,
            "type": 2,
            "parentUuid": "45487c6e532f450a82a3f39196beb5b1",
            "icon": "deal-type",
            "name": "数据源监控",
            "enName": "Data Source Monitoring",
            "originalName": "数据源监控",
            "originalEnName": "Data Source Monitoring",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "bff08a0e6fe64f51b4eb07602418a846",
                "code": "TZ0601",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/handle/monitorWarning/threeDataMonitor",
                "parentUuid": "5fccb80b15d742eaabe05e608ed29f88",
                "icon": "query",
                "name": "数据源异常预警",
                "enName": "Data Source Exception Alert",
                "originalName": "数据源异常预警",
                "originalEnName": "Data Source Exception Alert",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "bb33ac938eae43bd845683625b69e985",
                    "functionUuid": "bb33ac938eae43bd845683625b69e985",
                    "code": "query",
                    "name": "查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "bff08a0e6fe64f51b4eb07602418a846",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "bff08a0e6fe64f51b4eb07602418a846",
                "groupUuid": "bff08a0e6fe64f51b4eb07602418a846",
                "groupIcon": "query",
                "menuName": "数据源异常预警",
                "hasPermission": true
              },
              {
                "uuid": "b0601088305a42ccada5dc798c3e7a26",
                "code": "TZ0602",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/handle/monitorWarning/threeDataRemainWarn",
                "parentUuid": "5fccb80b15d742eaabe05e608ed29f88",
                "icon": "query",
                "name": "数据源总流量预警",
                "enName": "Data Source Total Traffic Alert",
                "originalName": "数据源总流量预警",
                "originalEnName": "Data Source Total Traffic Alert",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "e202e6ef59e14470b7b575676193ceca",
                    "functionUuid": "e202e6ef59e14470b7b575676193ceca",
                    "code": "query",
                    "name": "查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "b0601088305a42ccada5dc798c3e7a26",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "b0601088305a42ccada5dc798c3e7a26",
                "groupUuid": "b0601088305a42ccada5dc798c3e7a26",
                "groupIcon": "query",
                "menuName": "数据源总流量预警",
                "hasPermission": true
              },
              {
                "uuid": "2b7bdf44354f44c591363e2708e990b5",
                "code": "TZ0603",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/handle/monitorWarning/supplierWarning",
                "parentUuid": "5fccb80b15d742eaabe05e608ed29f88",
                "icon": "query",
                "name": "数据源期限预警",
                "enName": "Data Source Deadline alert",
                "originalName": "数据源期限预警",
                "originalEnName": "Data Source Deadline alert",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "32532e19da9f405e9c8a80b5223e1e1f",
                    "functionUuid": "32532e19da9f405e9c8a80b5223e1e1f",
                    "code": "query",
                    "name": "查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "2b7bdf44354f44c591363e2708e990b5",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "2b7bdf44354f44c591363e2708e990b5",
                "groupUuid": "2b7bdf44354f44c591363e2708e990b5",
                "groupIcon": "query",
                "menuName": "数据源期限预警",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "5fccb80b15d742eaabe05e608ed29f88",
            "groupUuid": "5fccb80b15d742eaabe05e608ed29f88",
            "groupName": "数据源监控",
            "groupIcon": "deal-type",
            "hasPermission": true
          },
          {
            "uuid": "6acf07683ef94009b63364ab6bbd12ef",
            "code": "IndexMonitor",
            "sortNo": 1,
            "level": 2,
            "type": 2,
            "parentUuid": "45487c6e532f450a82a3f39196beb5b1",
            "icon": "yuce",
            "name": "指标监控",
            "enName": "Indicator Monitoring",
            "originalName": "指标监控",
            "originalEnName": "Indicator Monitoring",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "df3cf80d57594eb79044b19e36d4028a",
                "code": "CP0106",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/params/indexMonitoring",
                "parentUuid": "6acf07683ef94009b63364ab6bbd12ef",
                "name": "指标监控",
                "enName": "Indicator Monitoring",
                "originalName": "指标监控",
                "originalEnName": "Indicator Monitoring",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "9573bf01f7954c56b9f0fbc88dc71b68",
                    "functionUuid": "9573bf01f7954c56b9f0fbc88dc71b68",
                    "code": "pageNullIndexValue",
                    "name": "全局分析-空值变量TOP30",
                    "enName": "pageNullIndexValue",
                    "sortNo": 1,
                    "menuUuid": "df3cf80d57594eb79044b19e36d4028a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2d3e681dde69400380365dc690db1d69",
                    "functionUuid": "2d3e681dde69400380365dc690db1d69",
                    "code": "indexTotalNumAndTodayCallNum",
                    "name": "全局分析-指标总数与今日调用量",
                    "enName": "indexTotalNumAndTodayCallNum",
                    "sortNo": 1,
                    "menuUuid": "df3cf80d57594eb79044b19e36d4028a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "aa708a790c7b4d7bb4701292e9e8b15a",
                    "functionUuid": "aa708a790c7b4d7bb4701292e9e8b15a",
                    "code": "listIndexCallNum",
                    "name": "全局分析-调用量趋势图",
                    "enName": "listIndexCallNum",
                    "sortNo": 1,
                    "menuUuid": "df3cf80d57594eb79044b19e36d4028a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d386f3edd71a40898e94c7579eefe52d",
                    "functionUuid": "d386f3edd71a40898e94c7579eefe52d",
                    "code": "pageExceptionalIndexValue",
                    "name": "全局分析-异常值变量TOP30",
                    "enName": "pageExceptionalIndexValue",
                    "sortNo": 1,
                    "menuUuid": "df3cf80d57594eb79044b19e36d4028a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a509d54846634dfa8da0df3f04460aa0",
                    "functionUuid": "a509d54846634dfa8da0df3f04460aa0",
                    "code": "listIndexCallNumByCategory",
                    "name": "指标监控分析-分类指标调用量趋势",
                    "enName": "listIndexCallNumByCategory",
                    "sortNo": 1,
                    "menuUuid": "df3cf80d57594eb79044b19e36d4028a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f8f3836b23764a678ead00ec8391c31e",
                    "functionUuid": "f8f3836b23764a678ead00ec8391c31e",
                    "code": "indexStdAndQuartile",
                    "name": "指标质量监控-指标质量监控",
                    "enName": "indexStdAndQuartile",
                    "sortNo": 1,
                    "menuUuid": "df3cf80d57594eb79044b19e36d4028a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c88f5bc896d642f0941493154dee9589",
                    "functionUuid": "c88f5bc896d642f0941493154dee9589",
                    "code": "pageIndexPsi",
                    "name": "指标监控分析-所有指标PSI列表",
                    "enName": "pageIndexPsi",
                    "sortNo": 1,
                    "menuUuid": "df3cf80d57594eb79044b19e36d4028a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "95d856e8b72f41b682588db1c47230ac",
                    "functionUuid": "95d856e8b72f41b682588db1c47230ac",
                    "code": "pageIndexOfExceptionalValue",
                    "name": "指标监控分析-所有指标异常值占比列表",
                    "enName": "pageIndexOfExceptionalValue",
                    "sortNo": 1,
                    "menuUuid": "df3cf80d57594eb79044b19e36d4028a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "df3cf80d57594eb79044b19e36d4028a",
                "groupUuid": "df3cf80d57594eb79044b19e36d4028a",
                "menuName": "指标监控",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "6acf07683ef94009b63364ab6bbd12ef",
            "groupUuid": "6acf07683ef94009b63364ab6bbd12ef",
            "groupName": "指标监控",
            "groupIcon": "yuce",
            "hasPermission": true
          }
        ],
        "functionList": [],
        "id": "45487c6e532f450a82a3f39196beb5b1",
        "groupUuid": "45487c6e532f450a82a3f39196beb5b1",
        "groupName": "监控管理",
        "groupIcon": "security",
        "hasPermission": false
      },
      {
        "uuid": "fdf206724fbf4ee880cb3f9d3e9fbc02",
        "code": "AnalyseQuery",
        "sortNo": 4,
        "level": 1,
        "type": 2,
        "parentUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "icon": "trend",
        "name": "计费查询",
        "enName": "Billing Query",
        "originalName": "计费查询",
        "originalEnName": "Billing Query",
        "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "createBy": "csb",
        "updateBy": "csb",
        "gmtCreate": *************,
        "gmtModified": *************,
        "children": [
          {
            "uuid": "e2ba3acb565b42078f460f342d791d61",
            "code": "ChargeQuery",
            "sortNo": 0,
            "level": 2,
            "type": 2,
            "parentUuid": "fdf206724fbf4ee880cb3f9d3e9fbc02",
            "icon": "check",
            "name": "计费查询",
            "enName": "Billing Query",
            "originalName": "计费查询",
            "originalEnName": "Billing Query",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "4b761c4be50e47779834f21ce70e70f0",
                "code": "TZ0302",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/handle/accountManagement/external",
                "parentUuid": "e2ba3acb565b42078f460f342d791d61",
                "icon": "query",
                "name": "数据源计费",
                "enName": "Data Source Billing",
                "originalName": "数据源计费",
                "originalEnName": "Data Source Billing",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "bfc78e72593342c5a3a7597b70ed0345",
                    "functionUuid": "bfc78e72593342c5a3a7597b70ed0345",
                    "code": "export",
                    "name": "导出",
                    "enName": "export",
                    "sortNo": 0,
                    "menuUuid": "4b761c4be50e47779834f21ce70e70f0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "312e1ae13d6c4687aec78887c8b3c979",
                    "functionUuid": "312e1ae13d6c4687aec78887c8b3c979",
                    "code": "look",
                    "name": "查看",
                    "enName": "look",
                    "sortNo": 0,
                    "menuUuid": "4b761c4be50e47779834f21ce70e70f0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5158576b173e415ea1f3b601a91a6352",
                    "functionUuid": "5158576b173e415ea1f3b601a91a6352",
                    "code": "query",
                    "name": "查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "4b761c4be50e47779834f21ce70e70f0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "72de701c57a6431fb3bd5ad1e4fa2d7d",
                    "functionUuid": "72de701c57a6431fb3bd5ad1e4fa2d7d",
                    "code": "serviceBillExport",
                    "name": "服务明细账单导出",
                    "enName": "Service Bill Export",
                    "sortNo": 0,
                    "menuUuid": "4b761c4be50e47779834f21ce70e70f0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "4b761c4be50e47779834f21ce70e70f0",
                "groupUuid": "4b761c4be50e47779834f21ce70e70f0",
                "groupIcon": "query",
                "menuName": "数据源计费",
                "hasPermission": true
              },
              {
                "uuid": "d186e8c91b1a4a2e815289b6c7acf051",
                "code": "TZ0301",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/handle/accountManagement/internal",
                "parentUuid": "e2ba3acb565b42078f460f342d791d61",
                "icon": "query",
                "name": "调用方计费",
                "enName": "Caller Billing",
                "originalName": "调用方计费",
                "originalEnName": "Caller Billing",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "0cada38db4ef412f967c848f1541a410",
                    "functionUuid": "0cada38db4ef412f967c848f1541a410",
                    "code": "query",
                    "name": "按应用计费-查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6a5709f212a4454a8a45442a93d3c141",
                    "functionUuid": "6a5709f212a4454a8a45442a93d3c141",
                    "code": "queryOrg",
                    "name": "按机构计费-查询",
                    "enName": "queryOrg",
                    "sortNo": 0,
                    "menuUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "dab6838fceae41229f518be550ecd1dd",
                    "functionUuid": "dab6838fceae41229f518be550ecd1dd",
                    "code": "exportOrg",
                    "name": "按机构计费-导出",
                    "enName": "exportOrg",
                    "sortNo": 0,
                    "menuUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9dfe45d173c145df983babb78b98752b",
                    "functionUuid": "9dfe45d173c145df983babb78b98752b",
                    "code": "look",
                    "name": "按应用计费-查看",
                    "enName": "look",
                    "sortNo": 0,
                    "menuUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "23a2631fb1234e6984ad1d2b42b7bcda",
                    "functionUuid": "23a2631fb1234e6984ad1d2b42b7bcda",
                    "code": "export",
                    "name": "按应用计费-导出",
                    "enName": "export",
                    "sortNo": 0,
                    "menuUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8f7a474f8c2541a19a8a106590e2dbc4",
                    "functionUuid": "8f7a474f8c2541a19a8a106590e2dbc4",
                    "code": "lookOrg",
                    "name": "按机构计费-查看",
                    "enName": "lookOrg",
                    "sortNo": 0,
                    "menuUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "3bdc6926e7e14ba4bf9513b2575a432c",
                    "functionUuid": "3bdc6926e7e14ba4bf9513b2575a432c",
                    "code": "channelBillListExport",
                    "name": "按应用计费明细-导出",
                    "enName": "channelBillList Export",
                    "sortNo": 1,
                    "menuUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "436a4a5f72cb49068af151a8cfdf6e51",
                    "functionUuid": "436a4a5f72cb49068af151a8cfdf6e51",
                    "code": "orgBillListExport",
                    "name": "按机构计费明细-导出",
                    "enName": "orgBillList Export",
                    "sortNo": 1,
                    "menuUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "d186e8c91b1a4a2e815289b6c7acf051",
                "groupUuid": "d186e8c91b1a4a2e815289b6c7acf051",
                "groupIcon": "query",
                "menuName": "调用方计费",
                "hasPermission": true
              },
              {
                "uuid": "9eb0a92f7c3847ff8547c96ab769dd35",
                "code": "TZ0303",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/handle/accountManagement/contractBillingDetail",
                "parentUuid": "e2ba3acb565b42078f460f342d791d61",
                "name": "合同计费",
                "enName": "Contract Billing",
                "originalName": "合同计费",
                "originalEnName": "Contract Billing",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "9b99f5e3813d41708100344d9dac25e4",
                    "functionUuid": "9b99f5e3813d41708100344d9dac25e4",
                    "code": "contractListExport",
                    "name": "导出合同计费列表",
                    "enName": "contractListExport",
                    "sortNo": 1,
                    "menuUuid": "9eb0a92f7c3847ff8547c96ab769dd35",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5d2edefd186c4f25b2d1f3a957691ec0",
                    "functionUuid": "5d2edefd186c4f25b2d1f3a957691ec0",
                    "code": "contractBillListExport",
                    "name": "导出合同计费明细列表",
                    "enName": "contractBillListExport",
                    "sortNo": 1,
                    "menuUuid": "9eb0a92f7c3847ff8547c96ab769dd35",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "27bb1871207c4d59b05ad272b4b0f9ac",
                    "functionUuid": "27bb1871207c4d59b05ad272b4b0f9ac",
                    "code": "contractList",
                    "name": "查询合同计费列表",
                    "enName": "contractList",
                    "sortNo": 1,
                    "menuUuid": "9eb0a92f7c3847ff8547c96ab769dd35",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c809299588d14f40b157a5a5b00d426d",
                    "functionUuid": "c809299588d14f40b157a5a5b00d426d",
                    "code": "contractBillList",
                    "name": "查询合同计费明细列表",
                    "enName": "contractBillList",
                    "sortNo": 1,
                    "menuUuid": "9eb0a92f7c3847ff8547c96ab769dd35",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "9eb0a92f7c3847ff8547c96ab769dd35",
                "groupUuid": "9eb0a92f7c3847ff8547c96ab769dd35",
                "menuName": "合同计费",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "e2ba3acb565b42078f460f342d791d61",
            "groupUuid": "e2ba3acb565b42078f460f342d791d61",
            "groupName": "计费查询",
            "groupIcon": "check",
            "hasPermission": true
          }
        ],
        "functionList": [],
        "id": "fdf206724fbf4ee880cb3f9d3e9fbc02",
        "groupUuid": "fdf206724fbf4ee880cb3f9d3e9fbc02",
        "groupName": "计费查询",
        "groupIcon": "trend",
        "hasPermission": false
      },
      {
        "uuid": "88fe84c03f4941a0aa61d089de51c74b",
        "code": "OfflineConfig",
        "sortNo": 5,
        "level": 1,
        "type": 2,
        "parentUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "icon": "flow",
        "name": "离线管理",
        "enName": "Offline Management",
        "originalName": "离线管理",
        "originalEnName": "Offline Management",
        "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "createBy": "csb",
        "updateBy": "csb",
        "gmtCreate": *************,
        "gmtModified": *************,
        "children": [
          {
            "uuid": "7f1c531d07914f42b1f64b39aca93072",
            "code": "OfflineIndexConfig",
            "sortNo": 0,
            "level": 2,
            "type": 2,
            "parentUuid": "88fe84c03f4941a0aa61d089de51c74b",
            "icon": "spider",
            "name": "离线报文管理",
            "enName": "Offline Packet Management",
            "originalName": "离线报文管理",
            "originalEnName": "Offline Packet Management",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "3e9b6121d87444c5bd9b0d9f644fc655",
                "code": "CP0105",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/params/offline",
                "parentUuid": "7f1c531d07914f42b1f64b39aca93072",
                "name": "离线报文管理",
                "enName": "Offline Index",
                "originalName": "离线报文管理",
                "originalEnName": "Offline Index",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "3b1c5e0867c64c0bbe7bb788ad0108b4",
                    "functionUuid": "3b1c5e0867c64c0bbe7bb788ad0108b4",
                    "code": "download",
                    "name": "下载",
                    "enName": "download",
                    "sortNo": 1,
                    "menuUuid": "3e9b6121d87444c5bd9b0d9f644fc655",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c978495a9ce54b26950a6bec3d566841",
                    "functionUuid": "c978495a9ce54b26950a6bec3d566841",
                    "code": "offlineTaskDetail",
                    "name": "查看离线任务详情",
                    "enName": "offlineTaskDetail",
                    "sortNo": 1,
                    "menuUuid": "3e9b6121d87444c5bd9b0d9f644fc655",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a62a418bfa0647f2a10f3ede26df22a2",
                    "functionUuid": "a62a418bfa0647f2a10f3ede26df22a2",
                    "code": "delete",
                    "name": "删除",
                    "enName": "delete",
                    "sortNo": 1,
                    "menuUuid": "3e9b6121d87444c5bd9b0d9f644fc655",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6735703b980047ffa167f23c274f514c",
                    "functionUuid": "6735703b980047ffa167f23c274f514c",
                    "code": "offlinetaskList",
                    "name": "任务列表",
                    "enName": "offlinetask_list",
                    "sortNo": 1,
                    "menuUuid": "3e9b6121d87444c5bd9b0d9f644fc655",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "cfaa6429f3814f82a2d353ad12a6e0ff",
                    "functionUuid": "cfaa6429f3814f82a2d353ad12a6e0ff",
                    "code": "offlineTaskLog",
                    "name": "查看离线任务日志",
                    "enName": "offlineTaskLog",
                    "sortNo": 1,
                    "menuUuid": "3e9b6121d87444c5bd9b0d9f644fc655",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "20de6170eaf64af1b9a4c0a86e0e6edc",
                    "functionUuid": "20de6170eaf64af1b9a4c0a86e0e6edc",
                    "code": "create",
                    "name": "新增跑批任务",
                    "enName": "create",
                    "sortNo": 1,
                    "menuUuid": "3e9b6121d87444c5bd9b0d9f644fc655",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "3e9b6121d87444c5bd9b0d9f644fc655",
                "groupUuid": "3e9b6121d87444c5bd9b0d9f644fc655",
                "menuName": "离线报文管理",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "7f1c531d07914f42b1f64b39aca93072",
            "groupUuid": "7f1c531d07914f42b1f64b39aca93072",
            "groupName": "离线报文管理",
            "groupIcon": "spider",
            "hasPermission": true
          },
          {
            "uuid": "502a193d27a14ba8b53eb4eb1ebd0e83",
            "code": "BatchInvokeTask",
            "sortNo": 1,
            "level": 2,
            "type": 2,
            "parentUuid": "88fe84c03f4941a0aa61d089de51c74b",
            "icon": "access",
            "name": "批量调用管理",
            "enName": "Batch Call Management",
            "originalName": "批量调用管理",
            "originalEnName": "Batch Call Management",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                "code": "TZ0801",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/handle/batchCall/taskList",
                "parentUuid": "502a193d27a14ba8b53eb4eb1ebd0e83",
                "icon": "query",
                "name": "批量调用任务管理",
                "enName": "Batch Call Management",
                "originalName": "批量调用任务管理",
                "originalEnName": "Batch Call Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "3.3.12-tianzuo",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "169c0a821c3a4f54994884cb81cdd8e4",
                    "functionUuid": "169c0a821c3a4f54994884cb81cdd8e4",
                    "code": "query",
                    "name": "查询",
                    "enName": "query",
                    "sortNo": 0,
                    "menuUuid": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "a1c2b5aa66b049eaba1ee0b680c7b870",
                    "functionUuid": "a1c2b5aa66b049eaba1ee0b680c7b870",
                    "code": "delete",
                    "name": "删除",
                    "enName": "delete",
                    "sortNo": 0,
                    "menuUuid": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "9ebe7c6410ff451da4d0d6106a6ee9bc",
                    "functionUuid": "9ebe7c6410ff451da4d0d6106a6ee9bc",
                    "code": "download",
                    "name": "下载模版",
                    "enName": "download",
                    "sortNo": 0,
                    "menuUuid": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "fe169a8749c04664af90bd521b08d04d",
                    "functionUuid": "fe169a8749c04664af90bd521b08d04d",
                    "code": "add",
                    "name": "新增",
                    "enName": "add",
                    "sortNo": 0,
                    "menuUuid": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4642ac80b48e4b318922336188b138ee",
                    "functionUuid": "4642ac80b48e4b318922336188b138ee",
                    "code": "getTaskDetail",
                    "name": "任务明细查看",
                    "enName": "getTaskDetail",
                    "sortNo": 1,
                    "menuUuid": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "601193774f63463abc42752bb88b712e",
                    "functionUuid": "601193774f63463abc42752bb88b712e",
                    "code": "downloadResult",
                    "name": "下载结果文件",
                    "enName": "download Result",
                    "sortNo": 1,
                    "menuUuid": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                "groupUuid": "5d0e7ea7ad3d43f0ab99708d954ca4e0",
                "groupIcon": "query",
                "menuName": "批量调用任务管理",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "502a193d27a14ba8b53eb4eb1ebd0e83",
            "groupUuid": "502a193d27a14ba8b53eb4eb1ebd0e83",
            "groupName": "批量调用管理",
            "groupIcon": "access",
            "hasPermission": true
          }
        ],
        "functionList": [],
        "id": "88fe84c03f4941a0aa61d089de51c74b",
        "groupUuid": "88fe84c03f4941a0aa61d089de51c74b",
        "groupName": "离线管理",
        "groupIcon": "flow",
        "hasPermission": false
      },
      {
        "uuid": "a073d4a8a82c4cfab81bb7a7c61994f7",
        "code": "PermissionConfig",
        "sortNo": 6,
        "level": 1,
        "type": 2,
        "parentUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "icon": "xitong",
        "name": "权限中心",
        "enName": "PermissionConfig",
        "originalName": "权限中心",
        "originalEnName": "PermissionConfig",
        "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
        "createBy": "csb",
        "updateBy": "csb",
        "gmtCreate": *************,
        "gmtModified": *************,
        "children": [
          {
            "uuid": "4775d628a9aa4bf099d915d73b3a851d",
            "code": "Config",
            "sortNo": 0,
            "level": 2,
            "type": 2,
            "parentUuid": "a073d4a8a82c4cfab81bb7a7c61994f7",
            "icon": "setting",
            "name": "系统配置",
            "enName": "Config",
            "originalName": "系统配置",
            "originalEnName": "Config",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "b1e8af7ebb06434cbbadc634dea4a871",
                "code": "QX0401",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/bridge/dictionary",
                "parentUuid": "4775d628a9aa4bf099d915d73b3a851d",
                "icon": "icon",
                "name": "字典配置",
                "enName": "Dictionary Configuration",
                "originalName": "字典配置",
                "originalEnName": "Dictionary Configuration",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "088884cc7a21404ab7f5838010da00f0",
                    "functionUuid": "088884cc7a21404ab7f5838010da00f0",
                    "code": "addDictionary",
                    "name": "新建字典配置",
                    "enName": "Add Dictionary",
                    "sortNo": 0,
                    "menuUuid": "b1e8af7ebb06434cbbadc634dea4a871",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1a7d6bd84f904e9db8557d0d55695f4e",
                    "functionUuid": "1a7d6bd84f904e9db8557d0d55695f4e",
                    "code": "viewDictionary",
                    "name": "查看字典配置",
                    "enName": "View Dictionary",
                    "sortNo": 0,
                    "menuUuid": "b1e8af7ebb06434cbbadc634dea4a871",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f81d381da9014b9097f222f35fcf1f81",
                    "functionUuid": "f81d381da9014b9097f222f35fcf1f81",
                    "code": "modifyDictionary",
                    "name": "修改字典配置",
                    "enName": "Modify Dictionary",
                    "sortNo": 0,
                    "menuUuid": "b1e8af7ebb06434cbbadc634dea4a871",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8803df6dda31449b83701fbbce06aef7",
                    "functionUuid": "8803df6dda31449b83701fbbce06aef7",
                    "code": "deleteDictionary",
                    "name": "删除字典配置",
                    "enName": "Delete Dictionary",
                    "sortNo": 0,
                    "menuUuid": "b1e8af7ebb06434cbbadc634dea4a871",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "b1e8af7ebb06434cbbadc634dea4a871",
                "groupUuid": "b1e8af7ebb06434cbbadc634dea4a871",
                "groupIcon": "icon",
                "menuName": "字典配置",
                "hasPermission": true
              },
              {
                "uuid": "a49412d4c73e41a690a97aa81ee4dd4a",
                "code": "QX0402",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/bridge/parameter",
                "parentUuid": "4775d628a9aa4bf099d915d73b3a851d",
                "icon": "python",
                "name": "参数配置",
                "enName": "Parameter Management",
                "originalName": "参数配置",
                "originalEnName": "Parameter Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "0edbe1169f984e21a9ec8d3c82b72586",
                    "functionUuid": "0edbe1169f984e21a9ec8d3c82b72586",
                    "code": "getSystemConfigList",
                    "name": "查询配置列表",
                    "enName": "View Config List",
                    "sortNo": 0,
                    "menuUuid": "a49412d4c73e41a690a97aa81ee4dd4a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "cd80c0c78f3040a29f51a750ba08bfd0",
                    "functionUuid": "cd80c0c78f3040a29f51a750ba08bfd0",
                    "code": "modifySystemConfig",
                    "name": "修改配置",
                    "enName": "Modify System Config",
                    "sortNo": 0,
                    "menuUuid": "a49412d4c73e41a690a97aa81ee4dd4a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "8bf3fb1e4c6442b885200f87f2a22d39",
                    "functionUuid": "8bf3fb1e4c6442b885200f87f2a22d39",
                    "code": "addSystemConfig",
                    "name": "新建配置",
                    "enName": "Add System Config",
                    "sortNo": 0,
                    "menuUuid": "a49412d4c73e41a690a97aa81ee4dd4a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6deb146b2e264d18b7fa446d0edcb319",
                    "functionUuid": "6deb146b2e264d18b7fa446d0edcb319",
                    "code": "deleteSystemConfig",
                    "name": "删除配置",
                    "enName": "Delete System Config",
                    "sortNo": 0,
                    "menuUuid": "a49412d4c73e41a690a97aa81ee4dd4a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e1e8e20c45424579a6513286e3f17d10",
                    "functionUuid": "e1e8e20c45424579a6513286e3f17d10",
                    "code": "viewSystemConfigDetail",
                    "name": "查询配置详情",
                    "enName": "View Config Detail",
                    "sortNo": 0,
                    "menuUuid": "a49412d4c73e41a690a97aa81ee4dd4a",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "a49412d4c73e41a690a97aa81ee4dd4a",
                "groupUuid": "a49412d4c73e41a690a97aa81ee4dd4a",
                "groupIcon": "python",
                "menuName": "参数配置",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "4775d628a9aa4bf099d915d73b3a851d",
            "groupUuid": "4775d628a9aa4bf099d915d73b3a851d",
            "groupName": "系统配置",
            "groupIcon": "setting",
            "hasPermission": true
          },
          {
            "uuid": "5b5cd63be1a24a7893ce185c713c7b17",
            "code": "Permission",
            "sortNo": 1,
            "level": 2,
            "type": 2,
            "parentUuid": "a073d4a8a82c4cfab81bb7a7c61994f7",
            "icon": "case",
            "name": "权限管理",
            "enName": "Permission",
            "originalName": "权限管理",
            "originalEnName": "Permission",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                "code": "QX0105",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/bridge/application",
                "parentUuid": "5b5cd63be1a24a7893ce185c713c7b17",
                "icon": "icon",
                "name": "渠道管理",
                "enName": "Channel Management",
                "originalName": "渠道管理",
                "originalEnName": "Channel Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "e5f0ccd2bc9b4b03bc6d6e39eed62e60",
                    "functionUuid": "e5f0ccd2bc9b4b03bc6d6e39eed62e60",
                    "code": "viewApplicationList",
                    "name": "查询渠道列表",
                    "enName": "View Application List",
                    "sortNo": 0,
                    "menuUuid": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f44087ca51dd43e7bd6ff55cd594aad7",
                    "functionUuid": "f44087ca51dd43e7bd6ff55cd594aad7",
                    "code": "createApplication",
                    "name": "新建渠道",
                    "enName": "Add Application",
                    "sortNo": 0,
                    "menuUuid": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6f818d9839cf42889ba4b01d1874b1f5",
                    "functionUuid": "6f818d9839cf42889ba4b01d1874b1f5",
                    "code": "modifyApplication",
                    "name": "修改渠道",
                    "enName": "Modify Application",
                    "sortNo": 0,
                    "menuUuid": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "438950f998bc4b3a96044ea0e9b9acea",
                    "functionUuid": "438950f998bc4b3a96044ea0e9b9acea",
                    "code": "deleteApplication",
                    "name": "删除渠道",
                    "enName": "Delete Application",
                    "sortNo": 0,
                    "menuUuid": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "85a6ee6fc76048e89d03de75444a20cb",
                    "functionUuid": "85a6ee6fc76048e89d03de75444a20cb",
                    "code": "viewApplicationDetail",
                    "name": "查询渠道详情",
                    "enName": "View Application Detail",
                    "sortNo": 0,
                    "menuUuid": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2446016477b64e2b999120e5a7644e91",
                    "functionUuid": "2446016477b64e2b999120e5a7644e91",
                    "code": "getAllApp",
                    "name": "获取全部渠道",
                    "enName": "getAllApp",
                    "sortNo": 1,
                    "menuUuid": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                "groupUuid": "cf3ca00a6fe3411ab83ff0ccfc5c9b73",
                "groupIcon": "icon",
                "menuName": "渠道管理",
                "hasPermission": true
              },
              {
                "uuid": "1cfc5aef3284438da460988741218dd8",
                "code": "QX0101",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/bridge/permission/organization",
                "parentUuid": "5b5cd63be1a24a7893ce185c713c7b17",
                "icon": "org",
                "name": "机构管理\t",
                "enName": "Organization Management",
                "originalName": "机构管理\t",
                "originalEnName": "Organization Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "33e4910c676b45c7b70d8d6d4d091082",
                    "functionUuid": "33e4910c676b45c7b70d8d6d4d091082",
                    "code": "viewOrg",
                    "name": "查看机构",
                    "enName": "View Organization",
                    "sortNo": 0,
                    "menuUuid": "1cfc5aef3284438da460988741218dd8",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "1955a6168909452987f1f6dc6c9fc8c3",
                    "functionUuid": "1955a6168909452987f1f6dc6c9fc8c3",
                    "code": "viewPermission",
                    "name": "查看菜单功能权限",
                    "enName": "View Permission",
                    "sortNo": 0,
                    "menuUuid": "1cfc5aef3284438da460988741218dd8",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c92a249fbc8f453980d969279fff505c",
                    "functionUuid": "c92a249fbc8f453980d969279fff505c",
                    "code": "modifyOrg",
                    "name": "编辑机构",
                    "enName": "Modify Organization",
                    "sortNo": 0,
                    "menuUuid": "1cfc5aef3284438da460988741218dd8",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "da2b1e2136db464bb5cb0b22284052df",
                    "functionUuid": "da2b1e2136db464bb5cb0b22284052df",
                    "code": "modifyPermission",
                    "name": "设置菜单功能权限",
                    "enName": "Function",
                    "sortNo": 0,
                    "menuUuid": "1cfc5aef3284438da460988741218dd8",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "36705e309cc2470d8b2f28e1660411b3",
                    "functionUuid": "36705e309cc2470d8b2f28e1660411b3",
                    "code": "deleteOrg",
                    "name": "删除机构",
                    "enName": "Delete Organization",
                    "sortNo": 0,
                    "menuUuid": "1cfc5aef3284438da460988741218dd8",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "41e79472ab37492f8235c2d250e55e70",
                    "functionUuid": "41e79472ab37492f8235c2d250e55e70",
                    "code": "addOrg",
                    "name": "添加机构",
                    "enName": "Add Organization",
                    "sortNo": 0,
                    "menuUuid": "1cfc5aef3284438da460988741218dd8",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "97dec720ef654820bf075dc048d7a66b",
                    "functionUuid": "97dec720ef654820bf075dc048d7a66b",
                    "code": "getLastOrg",
                    "name": "获取底层机构列表",
                    "enName": "getLastOrg",
                    "sortNo": 1,
                    "menuUuid": "1cfc5aef3284438da460988741218dd8",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "1cfc5aef3284438da460988741218dd8",
                "groupUuid": "1cfc5aef3284438da460988741218dd8",
                "groupIcon": "org",
                "menuName": "机构管理\t",
                "hasPermission": true
              },
              {
                "uuid": "ffff9b250943450e86ad5c5e4685f5bf",
                "code": "QX0102",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/bridge/permission/role",
                "parentUuid": "5b5cd63be1a24a7893ce185c713c7b17",
                "icon": "role",
                "name": "角色管理",
                "enName": "Role Management",
                "originalName": "角色管理",
                "originalEnName": "Role Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "85764d295bdb4c77ae491739eb1d94a7",
                    "functionUuid": "85764d295bdb4c77ae491739eb1d94a7",
                    "code": "viewPermission",
                    "name": "查看菜单功能权限",
                    "enName": "View Permission",
                    "sortNo": 0,
                    "menuUuid": "ffff9b250943450e86ad5c5e4685f5bf",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "3c7261c744174e0d96bda7ce1393487d",
                    "functionUuid": "3c7261c744174e0d96bda7ce1393487d",
                    "code": "addRole",
                    "name": "添加角色",
                    "enName": "Add Role",
                    "sortNo": 0,
                    "menuUuid": "ffff9b250943450e86ad5c5e4685f5bf",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "260949a5edda4255a1476ad85a0aba85",
                    "functionUuid": "260949a5edda4255a1476ad85a0aba85",
                    "code": "modifyPermission",
                    "name": "设置菜单功能权限",
                    "enName": "Modify Permission",
                    "sortNo": 0,
                    "menuUuid": "ffff9b250943450e86ad5c5e4685f5bf",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "74ff12c90d7041e193b90eb93f33dc68",
                    "functionUuid": "74ff12c90d7041e193b90eb93f33dc68",
                    "code": "deleteRole",
                    "name": "删除角色",
                    "enName": "Delete Role",
                    "sortNo": 0,
                    "menuUuid": "ffff9b250943450e86ad5c5e4685f5bf",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ad694786409043e48029b809e7fdff1e",
                    "functionUuid": "ad694786409043e48029b809e7fdff1e",
                    "code": "viewRole",
                    "name": "查看角色",
                    "enName": "View Role",
                    "sortNo": 0,
                    "menuUuid": "ffff9b250943450e86ad5c5e4685f5bf",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "61682d4eb34f44de896b2c9f627ab554",
                    "functionUuid": "61682d4eb34f44de896b2c9f627ab554",
                    "code": "modifyRole",
                    "name": "编辑角色",
                    "enName": "Modify Role",
                    "sortNo": 0,
                    "menuUuid": "ffff9b250943450e86ad5c5e4685f5bf",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "ffff9b250943450e86ad5c5e4685f5bf",
                "groupUuid": "ffff9b250943450e86ad5c5e4685f5bf",
                "groupIcon": "role",
                "menuName": "角色管理",
                "hasPermission": true
              },
              {
                "uuid": "56d88c7057f54a4cb60d9f153df69db1",
                "code": "QX0103",
                "sortNo": 3,
                "level": 3,
                "type": 3,
                "path": "/bridge/permission/user",
                "parentUuid": "5b5cd63be1a24a7893ce185c713c7b17",
                "icon": "user-group",
                "name": "用户管理",
                "enName": "User Management",
                "originalName": "用户管理",
                "originalEnName": "User Management",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "0702e12ba0d149dead6f44a1aa642739",
                    "functionUuid": "0702e12ba0d149dead6f44a1aa642739",
                    "code": "searchUser",
                    "name": "查看搜索用户",
                    "enName": "Search User",
                    "sortNo": 0,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5ec9cfd429de48dab72c79c8dafd42b3",
                    "functionUuid": "5ec9cfd429de48dab72c79c8dafd42b3",
                    "code": "deleteUser",
                    "name": "删除用户",
                    "enName": "Delete User",
                    "sortNo": 0,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4bfa2ed576e543f6bfdc998b0aece563",
                    "functionUuid": "4bfa2ed576e543f6bfdc998b0aece563",
                    "code": "addUser",
                    "name": "添加用户",
                    "enName": "Add User",
                    "sortNo": 0,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "31812df61f36470580ab90e8ec3abc38",
                    "functionUuid": "31812df61f36470580ab90e8ec3abc38",
                    "code": "modifyUser",
                    "name": "编辑用户",
                    "enName": "Modify User",
                    "sortNo": 0,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4416c51a3508424ea19fbbef2e0bcc81",
                    "functionUuid": "4416c51a3508424ea19fbbef2e0bcc81",
                    "code": "userLockUnlock",
                    "name": "启用禁用用户",
                    "enName": "User Lock/Unlock",
                    "sortNo": 0,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4e3f9eadf59e444b8255e58733264b2e",
                    "functionUuid": "4e3f9eadf59e444b8255e58733264b2e",
                    "code": "resetPassword",
                    "name": "重置密码",
                    "enName": "Reset Password",
                    "sortNo": 0,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c9f4bffc6fa3497aaf46510d8859cbcd",
                    "functionUuid": "c9f4bffc6fa3497aaf46510d8859cbcd",
                    "code": "viewValidUserList",
                    "name": "查看有效用户列表",
                    "enName": "viewValidUserList",
                    "sortNo": 1,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4a2f6f174142421b884c9af551b675a8",
                    "functionUuid": "4a2f6f174142421b884c9af551b675a8",
                    "code": "viewUser",
                    "name": "查看用户",
                    "enName": "view user",
                    "sortNo": 1,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "6bd829e6da414ca3ba3c69f3e1ee9964",
                    "functionUuid": "6bd829e6da414ca3ba3c69f3e1ee9964",
                    "code": "PreviewUserMenuTree",
                    "name": "菜单预览",
                    "enName": "PreviewUserMenuTree",
                    "sortNo": 1,
                    "menuUuid": "56d88c7057f54a4cb60d9f153df69db1",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "56d88c7057f54a4cb60d9f153df69db1",
                "groupUuid": "56d88c7057f54a4cb60d9f153df69db1",
                "groupIcon": "user-group",
                "menuName": "用户管理",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "5b5cd63be1a24a7893ce185c713c7b17",
            "groupUuid": "5b5cd63be1a24a7893ce185c713c7b17",
            "groupName": "权限管理",
            "groupIcon": "case",
            "hasPermission": true
          },
          {
            "uuid": "ba6ae3378a08426e947e925f1f66d9f6",
            "code": "Solution",
            "sortNo": 2,
            "level": 2,
            "type": 2,
            "parentUuid": "a073d4a8a82c4cfab81bb7a7c61994f7",
            "icon": "database",
            "name": "解决方案",
            "enName": "Solution",
            "originalName": "解决方案",
            "originalEnName": "Solution",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "defaultf55da6e783ac40beba781a728",
                "code": "QX0201",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/bridge/system/register",
                "parentUuid": "ba6ae3378a08426e947e925f1f66d9f6",
                "icon": "python",
                "name": "功能注册",
                "enName": "Function Register",
                "originalName": "功能注册",
                "originalEnName": "Function Register",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "cc64d3503db64970af275348d16c050d",
                    "functionUuid": "cc64d3503db64970af275348d16c050d",
                    "code": "QueryGroupOrMenuList",
                    "name": "查询分组或菜单列表",
                    "enName": "query group or menu list",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "84ea0ac82dbc427090e959978a2a9056",
                    "functionUuid": "84ea0ac82dbc427090e959978a2a9056",
                    "code": "DeleteFunction",
                    "name": "删除功能",
                    "enName": "delete function",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "93a2860bed564ee5a0d4fc989dfbb91d",
                    "functionUuid": "93a2860bed564ee5a0d4fc989dfbb91d",
                    "code": "DeleteGroupOrMenu",
                    "name": "删除分组或菜单列表",
                    "enName": "delete group or menu",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "5407fee99aa44d11a8f46ee4eec6886f",
                    "functionUuid": "5407fee99aa44d11a8f46ee4eec6886f",
                    "code": "UpdateGroupOrMenu",
                    "name": "修改分组或菜单列表",
                    "enName": "update group or menu",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "f90f42edb6824b98aec00af3b3c2fcfa",
                    "functionUuid": "f90f42edb6824b98aec00af3b3c2fcfa",
                    "code": "CreateFunc",
                    "name": "添加功能",
                    "enName": "create func",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "44d5a21ca03040b38d3a996762c68d85",
                    "functionUuid": "44d5a21ca03040b38d3a996762c68d85",
                    "code": "UpdateFunction",
                    "name": "更新功能",
                    "enName": "update function",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2245a822da574b74894e1440d195e6a1",
                    "functionUuid": "2245a822da574b74894e1440d195e6a1",
                    "code": "CreateGroupOrMenu",
                    "name": "创建分组或菜单列表",
                    "enName": "create group or menu",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "48b5e5ecc2e24e669329473e07df88d7",
                    "functionUuid": "48b5e5ecc2e24e669329473e07df88d7",
                    "code": "AddSystem",
                    "name": "添加系统",
                    "enName": "add system",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "4fbf5a2293af4b78a4a5f7d954db81d4",
                    "functionUuid": "4fbf5a2293af4b78a4a5f7d954db81d4",
                    "code": "DeleteSystem",
                    "name": "删除系统",
                    "enName": "delete system",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "e0681260955e41f5ba25f142d9fbf5c3",
                    "functionUuid": "e0681260955e41f5ba25f142d9fbf5c3",
                    "code": "QueryAllSystems",
                    "name": "查询所有系统",
                    "enName": "query all systems",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "d3c8baea927944618f8d8a64e107a159",
                    "functionUuid": "d3c8baea927944618f8d8a64e107a159",
                    "code": "ModifySystem",
                    "name": "更新系统",
                    "enName": "modify system",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "74eb56946c0c428d9d5d827a79b4bb89",
                    "functionUuid": "74eb56946c0c428d9d5d827a79b4bb89",
                    "code": "QueryFunctionList",
                    "name": "查询功能列表",
                    "enName": "query function list",
                    "sortNo": 1,
                    "menuUuid": "defaultf55da6e783ac40beba781a728",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "defaultf55da6e783ac40beba781a728",
                "groupUuid": "defaultf55da6e783ac40beba781a728",
                "groupIcon": "python",
                "menuName": "功能注册",
                "hasPermission": true
              },
              {
                "uuid": "cf15f39998f64915ad7860048a7c20d6",
                "code": "QX0202",
                "sortNo": 1,
                "level": 3,
                "type": 3,
                "path": "/bridge/system/menu",
                "parentUuid": "ba6ae3378a08426e947e925f1f66d9f6",
                "icon": "python",
                "name": "系统菜单管理",
                "enName": "System Menu",
                "originalName": "系统菜单管理",
                "originalEnName": "System Menu",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "7413f69c40a24d66ba2b8833ec14f18c",
                    "functionUuid": "7413f69c40a24d66ba2b8833ec14f18c",
                    "code": "getCandidateSolution",
                    "name": "获取候选解决方案菜单列表",
                    "enName": "getCandidateSolution",
                    "sortNo": 1,
                    "menuUuid": "cf15f39998f64915ad7860048a7c20d6",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "ab10d11455e7435c9eeefe6ea31449c0",
                    "functionUuid": "ab10d11455e7435c9eeefe6ea31449c0",
                    "code": "updateMenuTree",
                    "name": "更新系统菜单树",
                    "enName": "updateMenuTree",
                    "sortNo": 1,
                    "menuUuid": "cf15f39998f64915ad7860048a7c20d6",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c6c90fb82430458f8b75669eef25b0e9",
                    "functionUuid": "c6c90fb82430458f8b75669eef25b0e9",
                    "code": "getSystemMenuInfo",
                    "name": "获取系统菜单信息",
                    "enName": "getSystemMenuInfo",
                    "sortNo": 1,
                    "menuUuid": "cf15f39998f64915ad7860048a7c20d6",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "2e4dcc8af29c418d843580624ca54f37",
                    "functionUuid": "2e4dcc8af29c418d843580624ca54f37",
                    "code": "updateSystemMenuBaseInfo",
                    "name": "更新系统基本信息",
                    "enName": "updateSystemMenuBaseInfo",
                    "sortNo": 1,
                    "menuUuid": "cf15f39998f64915ad7860048a7c20d6",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "cf15f39998f64915ad7860048a7c20d6",
                "groupUuid": "cf15f39998f64915ad7860048a7c20d6",
                "groupIcon": "python",
                "menuName": "系统菜单管理",
                "hasPermission": true
              },
              {
                "uuid": "895cbd088b904ce3856be8a6d87e7d8c",
                "code": "SolutionAndLicense",
                "sortNo": 2,
                "level": 3,
                "type": 3,
                "path": "/bridge/system/solutionLicense",
                "parentUuid": "ba6ae3378a08426e947e925f1f66d9f6",
                "icon": "Python",
                "name": "解决方案与License",
                "enName": "Solution And License",
                "originalName": "解决方案与License",
                "originalEnName": "Solution And License",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "b62ccb60ec9d4556899c1e33c235d68e",
                    "functionUuid": "b62ccb60ec9d4556899c1e33c235d68e",
                    "code": "ImportSolutions",
                    "name": "导入解决方案",
                    "enName": "ImportSolutions",
                    "sortNo": 1,
                    "menuUuid": "895cbd088b904ce3856be8a6d87e7d8c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "b5663e066c3f4d6ba2c840f8ac1f1756",
                    "functionUuid": "b5663e066c3f4d6ba2c840f8ac1f1756",
                    "code": "GetSystemMenuRef",
                    "name": "获取系统菜单引用的菜单列表",
                    "enName": "GetSystemMenuRef",
                    "sortNo": 1,
                    "menuUuid": "895cbd088b904ce3856be8a6d87e7d8c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "22e6bae353b0431ebcc41f57f7956e14",
                    "functionUuid": "22e6bae353b0431ebcc41f57f7956e14",
                    "code": "GetSolutionList",
                    "name": "获取解决方案列表",
                    "enName": "GetSolutionList",
                    "sortNo": 1,
                    "menuUuid": "895cbd088b904ce3856be8a6d87e7d8c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c5fbaabb8e7c4244a4c950fb9ad66b98",
                    "functionUuid": "c5fbaabb8e7c4244a4c950fb9ad66b98",
                    "code": "ImportLicenses",
                    "name": "导入License",
                    "enName": "ImportLicenses",
                    "sortNo": 1,
                    "menuUuid": "895cbd088b904ce3856be8a6d87e7d8c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "c2fada3e26cf481e8d03d9891bb67161",
                    "functionUuid": "c2fada3e26cf481e8d03d9891bb67161",
                    "code": "UpdateLicenseSpecialSolution",
                    "name": "指定解决方案导入或更新License",
                    "enName": "UpdateLicenseSpecialSolution",
                    "sortNo": 1,
                    "menuUuid": "895cbd088b904ce3856be8a6d87e7d8c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "bfb674cc3d6d4582b077da76a09f5fd3",
                    "functionUuid": "bfb674cc3d6d4582b077da76a09f5fd3",
                    "code": "PreviewSolutionMenu",
                    "name": "解决方案菜单预览",
                    "enName": "PreviewSolutionMenu",
                    "sortNo": 1,
                    "menuUuid": "895cbd088b904ce3856be8a6d87e7d8c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "7badcb8da21744278be667b548fbcab6",
                    "functionUuid": "7badcb8da21744278be667b548fbcab6",
                    "code": "DeleteSolution",
                    "name": "删除解决方案",
                    "enName": "DeleteSolution",
                    "sortNo": 1,
                    "menuUuid": "895cbd088b904ce3856be8a6d87e7d8c",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "895cbd088b904ce3856be8a6d87e7d8c",
                "groupUuid": "895cbd088b904ce3856be8a6d87e7d8c",
                "groupIcon": "Python",
                "menuName": "解决方案与License",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "ba6ae3378a08426e947e925f1f66d9f6",
            "groupUuid": "ba6ae3378a08426e947e925f1f66d9f6",
            "groupName": "解决方案",
            "groupIcon": "database",
            "hasPermission": true
          },
          {
            "uuid": "27314225ff4b4e5aba5665e43d6a2841",
            "code": "LogManager",
            "sortNo": 3,
            "level": 2,
            "type": 2,
            "parentUuid": "a073d4a8a82c4cfab81bb7a7c61994f7",
            "icon": "check",
            "name": "日志管理",
            "enName": "LogManager",
            "originalName": "日志管理",
            "originalEnName": "LogManager",
            "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "originalSolutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
            "createBy": "csb",
            "updateBy": "csb",
            "gmtCreate": *************,
            "gmtModified": *************,
            "children": [
              {
                "uuid": "9a4e791b171a41baa7f34a765e4ff327",
                "code": "QX0301",
                "sortNo": 0,
                "level": 3,
                "type": 3,
                "path": "/bridge/operatorLog",
                "parentUuid": "27314225ff4b4e5aba5665e43d6a2841",
                "icon": "python",
                "name": "操作日志",
                "enName": "Operation Log",
                "originalName": "操作日志",
                "originalEnName": "Operation Log",
                "solutionUuid": "57c07089e5ee4b8c838273619d4a60eb",
                "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                "menuVersion": "2.6.4",
                "createBy": "csb",
                "updateBy": "csb",
                "target": "self",
                "gmtCreate": *************,
                "gmtModified": *************,
                "functionList": [
                  {
                    "funcUuid": "30f41ab6f8794eb880b0991433622e52",
                    "functionUuid": "30f41ab6f8794eb880b0991433622e52",
                    "code": "viewOperationLog",
                    "name": "查看操作日志",
                    "enName": "View Operation Log",
                    "sortNo": 0,
                    "menuUuid": "9a4e791b171a41baa7f34a765e4ff327",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  },
                  {
                    "funcUuid": "550fa57b426244eaafc32f8085c0c7bc",
                    "functionUuid": "550fa57b426244eaafc32f8085c0c7bc",
                    "code": "ExportOperationLogs",
                    "name": "导出操作日志",
                    "enName": "Export Operation Logs",
                    "sortNo": 1,
                    "menuUuid": "9a4e791b171a41baa7f34a765e4ff327",
                    "createBy": "fujun.cai",
                    "updateBy": "fujun.cai",
                    "hasPermission": true,
                    "solutionUuid": "b577af4d90c14f4a8d937eca958741d8",
                    "originalSolutionUuid": "b577af4d90c14f4a8d937eca958741d8"
                  }
                ],
                "id": "9a4e791b171a41baa7f34a765e4ff327",
                "groupUuid": "9a4e791b171a41baa7f34a765e4ff327",
                "groupIcon": "python",
                "menuName": "操作日志",
                "hasPermission": true
              }
            ],
            "functionList": [],
            "id": "27314225ff4b4e5aba5665e43d6a2841",
            "groupUuid": "27314225ff4b4e5aba5665e43d6a2841",
            "groupName": "日志管理",
            "groupIcon": "check",
            "hasPermission": true
          }
        ],
        "functionList": [],
        "id": "a073d4a8a82c4cfab81bb7a7c61994f7",
        "groupUuid": "a073d4a8a82c4cfab81bb7a7c61994f7",
        "groupName": "权限中心",
        "groupIcon": "xitong",
        "hasPermission": false
      }
    ],
    "user": {
      "id": 23,
      "uuid": "a483313fcceb4f47961931f02f03fd8c",
      "gmtCreate": *************,
      "gmtModified": *************,
      "account": "byh",
      "userName": "byh",
      "salt": "44ebff8345e143d7ba61710b7572cc7f",
      "orgUuid": "a8202aea546f48979754bdd45c471b08",
      "roleUuids": "[\"ee8dbc99831b4a9cb17578b51bbb09e0\",\"cfb9c519fde64476bc6ca01ba324a6a6\"]",
      "roleUuidsSelected": "[\"ee8dbc99831b4a9cb17578b51bbb09e0\",\"cfb9c519fde64476bc6ca01ba324a6a6\"]",
      "avatar": "male1",
      "expiration": *************,
      "gender": 0,
      "appName": "[\"test_add\",\"bug404503\",\"init\",\"necoTestApp\",\"JYTest\"]",
      "status": 0,
      "updateBy": "fujun.cai",
      "lang": "cn",
      "theme": "default",
      "layout": "default",
      "simplified": 1,
      "tokenMD5": "EEBD7D2CA464DB860FDA437BBE8020EB",
      "verificationCode": "",
      "tryTime": 0,
      "tryDate": *************,
      "updatePwdTime": *************,
      "firstLogin": "1",
      "isDelete": 0,
      "portalInfo": "[]",
      "updatePwdStatus": false
    }
  }
}