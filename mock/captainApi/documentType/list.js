module.exports = {
  "success": true,
  "code": 0,
  "msg": "成功",
  "data": [
    {
      "uuid": "d8ee07d861f141e89fcc95a5a336ed99",
      "name": "DZZXF2",
      "displayName": "贷中_同盾简单接口_同步",
      "contentType": "APPLICATION_JSON",
      "status": null,
      "gmtModify": "2024-11-18 15:15:44",
      "gmtCreate": "2024-11-18 15:15:44",
      "createBy": "youyue.chang(youyue.chang)",
      "modifyBy": "youyue.chang(youyue.chang)",
      "sample": null
    },
    {
      "uuid": "5e40f4f59c0a4400abbc2d87add106de",
      "name": "jsonTest",
      "displayName": "jsonTest",
      "contentType": "APPLICATION_JSON",
      "status": "ENABLED",
      "gmtModify": "2024-11-11 10:42:10",
      "gmtCreate": "2024-11-11 10:42:10",
      "createBy": "fujun.cai(fujun.cai)",
      "modifyBy": "fujun.cai(fujun.cai)",
      "sample": null
    },
    {
      "uuid": "8a690103bf004ee0be1699f750adcac3",
      "name": "caseWsbw",
      "displayName": "case外数报文",
      "contentType": "APPLICATION_TEXT",
      "status": "ENABLED",
      "gmtModify": "2024-11-06 17:31:50",
      "gmtCreate": "2024-11-06 17:31:50",
      "createBy": "zhb(zhb)",
      "modifyBy": "zhb(zhb)",
      "sample": null
    }
  ]
}