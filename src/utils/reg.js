import I18N from '@/utils/I18N';
export const nameReg = /^[\da-zA-Z\u4e00-\u9fa5\s-\%]+[\da-zA-Z\u4e00-\u9fa5_\s-\%]*$/;

// 名称由数字、英文、中文、下划线组成，且不能以下划线开头
export const nameRegCheck = (value) =>
    new Promise((resolve) =>
        resolve(!value || nameReg.test(value) ? false : I18N.utils.reg.zhiChiYouShuZi)
    );

// 数字、字母、下划线，补充输入空格、横线输入标准
export const regularName = /^[a-zA-Z0-9_\u4e00-\u9fa5\s\.-]+$/;

export const regularNameMsg = I18N.utils.reg.jieDianMingChengZhi;
