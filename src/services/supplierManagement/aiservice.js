import { getUrl, deleteEmptyObjItem } from '../../utils/common';
import request from '../../utils/request';
import { getHeader } from '@/utils/common';

const getChatData = (params, signal) => {
    return request(
        '/noahAgentApi/ai/chat/test',
        {
            method: 'POST',
            body: params,
            headers: {
                ...getHeader(),
                'Content-Type': 'application/json',
                Accept: '*/*',
                Authorization:
                    'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoiNjhjNmMzN2YtYmRmOC00YWM5LTkyNmQtZDI1Mjg1YWJmOGI4IiwiZXhwIjoxNzQ0MTg1MTIxLCJpc3MiOiJTRUxGX0hPU1RFRCIsInN1YiI6IkNvbnNvbGUgQVBJIFBhc3Nwb3J0In0.W9mK_6TsUW14UIgJ2CE - r8n64hxDy43ImuH - NryGDFs'
            },
            originRes: true,
            ...(signal || {})
        },
        true,
        () => {}
    );
};
const addAiMockData = (params) => {
    return request(
        '/bridgeApi/serviceConfig/addAiMockData',
        {
            method: 'POST',
            body: params
        },
        true
    );
};
const getAddAiMockDataStatus = () => {
    return request(
        '/bridgeApi/serviceConfig/getAddAiMockDataStatus',
        {
            method: 'get'
        },
        true
    );
};

const stopAddAiMockData = (params) => {
    return request(
        '/bridgeApi/serviceConfig/stopAddAiMockData',
        {
            method: 'POST',
            body: params
        },
        true
    );
};

export default { getChatData, addAiMockData, getAddAiMockDataStatus, stopAddAiMockData };
