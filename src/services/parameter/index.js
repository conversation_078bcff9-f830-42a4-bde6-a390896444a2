import { getHeader, getUrl, deleteEmptyObjItem } from '../../utils/common';
import request from '../../utils/request';

// begin
const documentTypePage = async (params) => {
    return request(
        getUrl('/datalandApi/documentType/list', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
///documentType/get
const getDocumentType = async (params) => {
    return request(
        getUrl('/datalandApi/documentType/get', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const documentTypeGet = async (params) => {
    return request(
        getUrl('/captainApi/documentType/get', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const fieldTree = async (params) => {
    return request(
        getUrl('/datalandApi/field/tree', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const documentTypeList = async (params) => {
    return request(
        getUrl('/captainApi/documentType/list', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const templatePage = async (params) => {
    return request(
        getUrl('/captainApi/template/page', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const templateOne = async (params) => {
    return request(
        getUrl('/captainApi/template/get', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const templateCategoryTree = async (params) => {
    return request(
        getUrl('/captainApi/templateCategory/tree', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const getFieldNodes = async (params) => {
    return request(
        getUrl('/captainApi/field/nodes', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const getVisualization = async (params) => {
    return request(
        getUrl('/captainApi/template/visualization/get', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const getFunctor = async (params) => {
    return request(
        getUrl('/captainApi/template/functor', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const getFields = async (params) => {
    return request(
        getUrl('/captainApi/field/fields', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const getSelectEnum = async (params) => {
    return request(
        getUrl('/captainApi/template/selectEnum', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const getScriptTemplate = async (params) => {
    return request(
        getUrl('/captainApi/common/getScriptTemplate', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const templateTree = async (params) => {
    return request(
        getUrl('/captainApi/template/tree', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const queryComponentReference = async (params) => {
    return request(
        getUrl('/captainApi/captain/relation/checkComponentReference', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const indexTree = async (params) => {
    return request(
        getUrl('/captainApi/indexCategory/tree', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const indexInPackage = async (params) => {
    return request(
        getUrl('/captainApi/index/inPackage', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const templateGet = async (params) => {
    return request(
        getUrl('/captainApi/template/get', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const templateList = async (params) => {
    return request(
        getUrl('/captainApi/template/list', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
const indexPage = async (params) => {
    return request(
        getUrl('/captainApi/index/page', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};

const documentTypeCreate = async (params) => {
    return request(
        '/datalandApi/documentType/create',
        {
            method: 'POST',
            headers: { ...getHeader(), 'Content-Type': 'application/json' },
            body: { ...params }
        },
        true
    );
};

const documentTypeUpdate = async (params) => {
    return request(
        '/captainApi/documentType/update',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const documentTypeExport = async (params) => {
    return request(
        '/captainApi/documentType/export',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const fieldUpdate = async (params) => {
    console.log('params', params, { ...params });
    return request(
        '/datalandApi/field/update',
        {
            method: 'POST',
            headers: { ...getHeader(), 'Content-Type': 'application/json' },
            body: { ...params }
        },
        true
    );
};
const fieldReorder = async (params) => {
    return request(
        '/captainApi/field/reorder',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const fieldDelete = async (params) => {
    return request(
        '/captainApi/field/delete',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const fieldCreate = async (params) => {
    return request(
        '/captainApi/field/create',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const documentTypeDelete = async (params) => {
    return request(
        '/datalandApi/documentType/delete',
        {
            method: 'DELETE',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};

const checkTimeMethod = async (params) => {
    return request(
        getUrl('/datalandApi/field/check_effect_time', params),
        {
            method: 'GET',
            headers: getHeader()
        },
        true
    );
};
///documentType/update_status
const updateStatus = async (params) => {
    return request(
        '/datalandApi/documentType/update_status',
        {
            method: 'PUT',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateTest = async (params) => {
    return request(
        '/captainApi/template/test',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateTestJavassist = async (params) => {
    return request(
        '/captainApi/template/test/javassist',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateDelete = async (params) => {
    return request(
        '/captainApi/template/delete',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateCreate = async (params) => {
    return request(
        '/captainApi/template/create',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateExport = async (params) => {
    return request(
        '/captainApi/template/export',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateImport = async (params) => {
    return request(
        '/captainApi/template/import',
        {
            method: 'POST',
            dataType: 'formdata',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateBatchEnable = async (params) => {
    return request(
        '/captainApi/template/batchEnable',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateBatchDisable = async (params) => {
    return request(
        '/captainApi/template/batchDisable',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateUpdate = async (params) => {
    return request(
        '/captainApi/template/update',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateEnable = async (params) => {
    return request(
        '/captainApi/template/enable',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateDisable = async (params) => {
    return request(
        '/captainApi/template/disable',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateCategoryReorder = async (params) => {
    return request(
        '/captainApi/templateCategory/reorder',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateCategoryCreate = async (params) => {
    return request(
        '/captainApi/templateCategory/create',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateCategoryDelete = async (params) => {
    return request(
        '/captainApi/templateCategory/delete',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const templateCategoryUpdate = async (params) => {
    return request(
        '/captainApi/templateCategory/update',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const createVisualization = async (params) => {
    return request(
        '/captainApi/template/visualization/create',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const updateVisualization = async (params) => {
    return request(
        '/captainApi/template/visualization/update',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const batchCheckComponentReference = async (params) => {
    return request(
        '/captainApi/captain/relation/batchCheckComponentReference',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};

const indexAddPackage = async (params) => {
    return request(
        '/captainApi/index/addPackage',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexAddPackageByCondition = async (params) => {
    return request(
        '/captainApi/index/addPackageByCondition',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexPackageList = async (params) => {
    return request(
        '/captainApi/indexPackage/list',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexCreate = async (params) => {
    return request(
        '/captainApi/index/create',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexBatchCreate = async (params) => {
    return request(
        '/captainApi/index/batchCreate',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexBatchDisable = async (params) => {
    return request(
        '/captainApi/index/batchDisable',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexBatchEnable = async (params) => {
    return request(
        '/captainApi/index/batchEnable',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexBatchDisableByCondition = async (params) => {
    return request(
        '/captainApi/index/batchDisableByCondition',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexBatchEnableByCondition = async (params) => {
    return request(
        '/captainApi/index/batchEnableByCondition',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexExport = async (params) => {
    return request(
        '/captainApi/index/export',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexImport = async (params) => {
    return request(
        '/captainApi/index/import',
        {
            method: 'POST',
            headers: getHeader(),
            dataType: 'formdata',
            body: { ...params }
        },
        true
    );
};
const indexUpdate = async (params) => {
    return request(
        '/captainApi/index/update',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexEnable = async (params) => {
    return request(
        '/captainApi/index/enable',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexDisable = async (params) => {
    return request(
        '/captainApi/index/disable',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexDelete = async (params) => {
    return request(
        '/captainApi/index/delete',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};

const indexTest = async (params) => {
    return request(
        '/captainApi/index/version/test',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};

const indexCategoryCreate = async (params) => {
    return request(
        '/captainApi/indexCategory/create',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};

const indexCategoryReorder = async (params) => {
    return request(
        '/captainApi/indexCategory/reorder',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexCategoryDelete = async (params) => {
    return request(
        '/captainApi/indexCategory/delete',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const indexCategoryUpdate = async (params) => {
    return request(
        '/captainApi/indexCategory/update',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
const reconstruction = async (params) => {
    return request(
        '/datalandApi/documentType/reconstruction',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};
///documentType/runDelayTaskNow
const runDelayTaskNow = async (params) => {
    return request(
        '/datalandApi/documentType/runDelayTaskNow',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};

//end

// 生成一次性token
const auth = async (params) => {
    return request(
        '/bridgeApi/user/getAuthCode',
        {
            method: 'POST',
            headers: getHeader(),
            body: { ...params }
        },
        true
    );
};

export default {
    auth,
    documentTypePage,
    documentTypeGet,
    documentTypeCreate,
    documentTypeUpdate,
    documentTypeExport,
    fieldUpdate,
    fieldReorder,
    fieldDelete,
    fieldCreate,
    fieldTree,
    documentTypeDelete,
    checkTimeMethod,
    updateStatus,
    documentTypeList,
    templatePage,
    templateTest,
    templateTestJavassist,
    templateOne,
    templateDelete,
    templateCreate,
    templateExport,
    templateImport,
    templateBatchEnable,
    templateBatchDisable,
    templateUpdate,
    templateEnable,
    templateDisable,
    templateCategoryTree,
    templateCategoryReorder,
    templateCategoryCreate,
    templateCategoryDelete,
    templateCategoryUpdate,
    getFieldNodes,
    getVisualization,
    createVisualization,
    updateVisualization,
    getFunctor,
    getFields,
    getSelectEnum,
    getScriptTemplate,
    templateTree,
    queryComponentReference,
    batchCheckComponentReference,
    indexTree,
    indexAddPackage,
    indexAddPackageByCondition,
    indexPackageList,
    indexInPackage,
    templateGet,
    templateList,
    indexPage,
    indexCreate,
    indexBatchCreate,
    indexBatchDisable,
    indexBatchEnable,
    indexBatchDisableByCondition,
    indexBatchEnableByCondition,
    getDocumentType,
    indexExport,
    indexImport,
    indexUpdate,
    indexEnable,
    indexDisable,
    indexDelete,
    indexTest,
    indexCategoryCreate,
    indexCategoryReorder,
    indexCategoryDelete,
    indexCategoryUpdate,
    reconstruction,
    runDelayTaskNow
};
