import I18N from '@/utils/I18N';
import { Form, Input, Row, Col, Table, Modal, Alert, Button, message, Ellipsis } from 'tntd';
import { useEffect, useState, useMemo } from 'react';
import { nameReg, nameRegCheck, regularName } from '@/utils/reg';

import AceEditor from 'react-ace';
import 'ace-builds/src-noconflict/mode-mysql';
import 'ace-builds/src-noconflict/theme-terminal';
import 'ace-builds/src-noconflict/ext-language_tools';
import SqlContent from '../SqlContent';
import Params from './Params.js';

const SqlQuery = (props) => {
    const { form, dataItem, clear, onSqlChange, allRealTablesDatasource, datasourceList, onTableChange, refreshResult, ...rest } = props;
    const { getFieldDecorator, setFieldsValue, getFieldValue } = form;

    const allTableSource = useMemo(() => {
        const dataSource = (allRealTablesDatasource || []).map(({ databaseName, realTable, datasourceId }) => {
            return {
                key: `${databaseName}.${realTable}`,
                tableName: `${databaseName}.${realTable}`,
                table: realTable,
                database: databaseName,
                datasourceId
            };
        });
        return dataSource;
    }, [allRealTablesDatasource]);

    useEffect(() => {
        if (clear !== 0) {
            setFieldsValue({
                sqlContent: ''
            });
        }
    }, [clear]);

    return (
        <>
            <Row gutter={16}>
                <Col span={24}>
                    <Form.Item className="form-item" label={I18N.components.modifymodal.mingCheng}>
                        {getFieldDecorator('nodeName', {
                            initialValue: dataItem?.nodeName,
                            rules: [
                                {
                                    required: true,
                                    message: I18N.routeservicenode.index.qingShuRuMingCheng
                                },
                                {
                                    validator: (rules, value, callback) => {
                                        if (value && !nameReg.test(value)) {
                                            callback(I18N.sqlset.sqlquery.zhiChiYouShuZi);
                                            return;
                                        }
                                        callback();
                                    }
                                }
                            ]
                        })(<Input maxLength={50} placeholder={I18N.routeservicenode.index.qingShuRuMingCheng} allowClear />)}
                    </Form.Item>
                </Col>
            </Row>
            <Row gutter={16}>
                <Col span={24}>
                    <Form.Item className="form-item" label={I18N.sqlset.sqlquery.canShuPeiZhi}>
                        {getFieldDecorator('sqlParams', {
                            initialValue: dataItem?.sqlParams || [{ type: 'date' }]
                        })(<Params {...rest} onChange={() => refreshResult()} />)}
                    </Form.Item>
                </Col>
            </Row>
            <Row gutter={16}>
                <Col span={24}>
                    <Form.Item className="form-item sql-params-form-item" wrapperCol={{ span: 24 }}>
                        {getFieldDecorator('sqlContent', {
                            initialValue: dataItem?.sqlContent,
                            rules: [
                                {
                                    required: true,
                                    message: I18N.sqlset.sqlquery.qingShuRuSQ
                                }
                            ]
                        })(
                            <SqlContent
                                {...rest}
                                dataItem={dataItem}
                                allDataSourceList={datasourceList || []}
                                parentForm={form}
                                allTableSource={allTableSource || []}
                                datasourceKey="dataSourceId"
                                onTableChange={onTableChange}
                                example={
                                    I18N.sqlset.sqlquery.chaXunYuJuS
                                }
                                placeholder={
                                    I18N.sqlset.sqlquery.zhiChiShuRuYi
                                }
                                clear={clear}
                                onChange={() => refreshResult()}
                            />
                        )}
                    </Form.Item>
                </Col>
            </Row>
        </>
    );
};

export default SqlQuery;
