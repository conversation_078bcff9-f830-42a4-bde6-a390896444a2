import I18N from '@/utils/I18N';
import { Input, Row, Col, Icon, Tooltip, Popover, Select, Button, message } from 'tntd';
import TooltipSelect from '@tntd/tooltip-select';
import Clipboard from 'react-clipboard.js';
import { TYPE_MAP } from '@/constants/common';
import './Params.less';

const InputGroup = Input.Group;
const { Option } = Select;

export default (props) => {
    const { onChange, value = [{ type: 'date', expr: undefined, name: undefined, value: undefined }], disabled, systemFieds } = props;
    const add = (i) => {
        const newV = [...value];
        newV.splice(i + 1, 0, { type: 'date', expr: undefined, name: undefined, value: undefined });
        onChange(newV);
    };

    const del = (i) => {
        const newV = [...value];
        newV.splice(i, 1);
        onChange(newV);
    };

    const changeField = (e, i, key) => {
        const newV = [...value];
        if (key === 'type') {
            newV[i][key] = e;
            newV[i]['expr'] = undefined;
            newV[i]['name'] = undefined;
            newV[i]['value'] = undefined;
        } else if (key === 'name') {
            newV[i][key] = e;
        } else if (['expr', 'value'].includes(key)) {
            newV[i][key] = e.target.value;
        }
        onChange(newV);
    };

    const clipboardHandle = () => {
        message.success(I18N.appservicelist.fuZhiChengGong); // 复制成功
    };

    return (
        <>
            {Array.isArray(value) && value.length
                ? value?.map((v, i) => {
                    return (
                        <Row type="flex" align="middle" className="param-row" key={`placeholder${i}`}>
                            <Col className="param-col">
                                <InputGroup compact>
                                    <Select
                                        value={v.type}
                                        dropdownMatchSelectWidth={false}
                                        onChange={(e) => changeField(e, i, 'type')}
                                        style={{ width: '140px' }}
                                        placeholder={I18N.components.bulktemplatemodal.qingXuanZeLeiXing}>
                                        <Option value="date">{I18N.sqlset.params.shiJianBiaoDaShi}</Option>
                                        <Option value="constant">{I18N.filter.index.changliang}</Option>
                                        <Option value="variable">{I18N.addmodify.serviceinput.bianLiang}</Option>
                                    </Select>
                                    {['date', 'constant'].includes(v.type) && (
                                        <Input
                                            disabled={disabled}
                                            allowClear
                                            onChange={(e) => changeField(e, i, 'expr')}
                                            key={i}
                                            value={v.expr || undefined}
                                            placeholder={I18N.sqlset.params.qingShuRuCanShu}
                                            style={{ flex: 1 }}
                                        />
                                    )}
                                    {v.type === 'variable' && (
                                        <>
                                            <div style={{ position: 'relative', width: '251px' }}>
                                                <TooltipSelect
                                                    placeholder={I18N.filter.index.qingxuanzexitongziduan}
                                                    style={{ width: '100%', maxWidth: 250 }}
                                                    value={v.name}
                                                    onChange={(e) => changeField(e, i, 'name')}
                                                    showSearch
                                                    isVirtual
                                                    dropdownMatchSelectWidth={false}
                                                    filterOption={(inputValue, option) => {
                                                        const { props } = option;
                                                        const str = props.children[1];
                                                        return !!str.includes(inputValue);
                                                    }}
                                                    disabled={disabled}
                                                    dropdownStyle={{ width: 350 }}>
                                                    {systemFieds
                                                        ?.filter((v) => v.selectType === 'FIELD_SYSTEM')
                                                        ?.map(({ name, dName, type }) => {
                                                            return (
                                                                <Option key={name} value={name}>
                                                                    <sup style={{ color: 'blue' }}>{TYPE_MAP[type]?.displayName}</sup>
                                                                    {`${dName}【${name}】`}
                                                                </Option>
                                                            );
                                                        })}
                                                </TooltipSelect>
                                                <div
                                                    style={{
                                                        position: 'absolute',
                                                        right: '25px',
                                                        top: '6px',
                                                        color: '#03A9F4'
                                                    }}>
                                                    <Clipboard
                                                        data-clipboard-text={v.name}
                                                        onSuccess={() => clipboardHandle()}
                                                        style={{
                                                            border: 'none',
                                                            outline: 'none',
                                                            cursor: 'pointer',
                                                            padding: 0,
                                                            backgroundColor: '#ffffff'
                                                        }}>
                                                        <Icon type="copy" />
                                                    </Clipboard>
                                                </div>
                                            </div>
                                            <Input
                                                disabled={disabled}
                                                allowClear
                                                onChange={(e) => changeField(e, i, 'value')}
                                                key={i}
                                                value={v.value || undefined}
                                                placeholder={I18N.sqlset.params.qingShuRuBianLiang}
                                                style={{ flex: 1 }}
                                            />
                                        </>
                                    )}
                                </InputGroup>
                            </Col>
                            <Col className="opera-icon-col">
                                {!disabled && <Icon type="plus-square" onClick={() => add(i)} />}
                                {!disabled && <Icon type="delete" onClick={() => del(i)} />}
                                {i === 0 && (
                                    <Popover
                                        placement="left"
                                        overlayStyle={{ width: '650px' }}
                                        title={I18N.sqlset.params.canShuPeiZhi}
                                        content={
                                            <>
                                                <div className="mb10">
                                                    {I18N.sqlset.params.yiChangLiangBuXu}<code>output_table=test_table_name</code>
                                                </div>
                                                <div className="mb10">
                                                    {I18N.sqlset.params.erShiJianBiaoDa}<div className="mb5"> {I18N.sqlset.params.shiJianGeShiFu}</div>
                                                    <div className="mb5"> {I18N.sqlset.params.shiJianLeiXingY}</div>
                                                    <div className="mb5">
                                                        {I18N.sqlset.params.jiaHaoHuoZheJian}</div>
                                                </div>
                                                <div>{I18N.sqlset.params.sanBianLiangXuYao}</div>
                                            </>
                                        }>
                                        <Icon type="question-circle" className="question-info" />
                                    </Popover>
                                )}
                            </Col>
                        </Row>
                    );
                })
                : null}
            {Array.isArray(value) && !value.length ? (
                <Button icon="plus" type="primary" disabled={disabled} onClick={() => add(-1)}>
                    {I18N.sqlset.params.tianJiaCanShuPei}</Button>
            ) : null}
        </>
    );
};
