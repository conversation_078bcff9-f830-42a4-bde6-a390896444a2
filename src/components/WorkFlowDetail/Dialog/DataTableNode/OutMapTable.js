import I18N from '@/utils/I18N';
import { useState, forwardRef, useImperativeHandle, useEffect } from 'react';
import { Table, Select, Icon, Tag, Row } from 'tntd';
import TooltipSelect from '@tntd/tooltip-select';
import { TYPE_MAP, TYPE_NUM_MAP } from '@/constants/common';

const { Column } = Table;
const { Option } = Select;

const defaultMappingField = {
    mappingName: undefined,
    fieldName: undefined
};

export default forwardRef((props, ref) => {
    const { systemFieds, value: mappingFields = [], onChange, disabled, dataTable } = props;

    let realTableSchema = [
        {
            patrition: false,
            type: 1,
            typeName: 'STRING',
            name: 'data_collection_json'
        }
    ].concat(props.realTableSchema || []);

    const [pageInfo, setPageInfo] = useState({ page: 1, pageSize: 10 });

    const handleIndex = (index) => {
        const page = Math.min(pageInfo?.page, Math.ceil(mappingFields?.length / pageInfo.pageSize));
        return ((page || 1) - 1) * (pageInfo.pageSize || 10) + index;
    };

    useImperativeHandle(ref, () => ({
        pageInfo
    }));

    const removeField = (index) => {
        // if (mappingFields.length <= 1) {
        //     message.warning('至少保留一条');
        //     return;
        // }
        const newMappingFields = [...mappingFields];
        newMappingFields.splice(index, 1);

        onChange(newMappingFields);
    };

    const handleFieldChange = (index, key, value) => {
        const newMappingFields = [...mappingFields];
        newMappingFields[index][key] = value;

        mappingFields[index][key] = value;
        onChange(newMappingFields);
    };

    const addField = () => {
        const newMappingFields = [...mappingFields];
        newMappingFields.push({ ...defaultMappingField });

        onChange(newMappingFields);
    };

    const handleServiceParamChange = (index) => {
        index = handleIndex(index);
        return (value) => {
            handleFieldChange(index, 'mappingName', value);
        };
    };

    const handleSystemFieldChange = (index) => {
        index = handleIndex(index);
        return (value) => {
            handleFieldChange(index, 'fieldName', value);
        };
    };

    return (
        <>
            <Table
                title={() => (
                    <Row>
                        {I18N.outputparams.index.chuCanZiDuan}<Tag color="blue" style={{ marginLeft: '10px' }}>
                            {I18N.featureservicenode.outmaptable.shuChu}</Tag>
                    </Row>
                )}
                pagination={{
                    current: pageInfo.page,
                    pageSize: pageInfo.pageSize,
                    total: mappingFields?.length,
                    onChange: (page, pageSize) => {
                        setPageInfo({ page, pageSize });
                    }
                }}
                rowKey={(e, i) => i}
                size="small"
                className="m-table-table"
                dataSource={!!dataTable ? mappingFields : []}>
                <Column
                    title={I18N.datatablenode.outmaptable.chuCanZiDuanBiao}
                    width={250}
                    dataIndex="mappingName"
                    key="mappingName"
                    render={(val, record, index) => {
                        return (
                            <div className="eleNameDiv" style={{ position: 'relative' }}>
                                <TooltipSelect
                                    placeholder={I18N.datatablenode.outmaptable.qingXuanZeChuCan}
                                    style={{ width: '100%', maxWidth: 250 }}
                                    dropdownStyle={{ width: 350 }}
                                    value={val}
                                    onChange={handleServiceParamChange(index)}
                                    showSearch
                                    isVirtual
                                    dropdownMatchSelectWidth={false}
                                    disabled={disabled}
                                    filterOption={(inputValue, option) => {
                                        const { props } = option;
                                        const str = props.children[1];
                                        return !!str.includes(inputValue);
                                    }}>
                                    {realTableSchema?.map(({ name, type, comment }) => {
                                        let disabled = false;
                                        let isSelected = mappingFields.some((item) => item.mappingName === name);

                                        return (
                                            <Option key={name} value={name} disabled={disabled || isSelected} type={type}>
                                                {/* <Tooltip placement="topLeft" title={`${name}${comment ? `(${comment})` : ''}`}> */}
                                                <sup style={{ color: 'blue' }}>{TYPE_NUM_MAP[type]?.displayName}</sup>
                                                {`${name}${comment ? `(${comment})` : ''}`}
                                                {/* </Tooltip> */}
                                            </Option>
                                        );
                                    })}
                                </TooltipSelect>
                            </div>
                        );
                    }}
                />
                <Column
                    title={I18N.datatablenode.outmaptable.yingSheXiTongZi}
                    width={250}
                    dataIndex="fieldName"
                    key="fieldName"
                    render={(val, record, index) => {
                        return (
                            <div className="eleNameDiv" style={{ position: 'relative' }}>
                                <TooltipSelect
                                    placeholder={I18N.filter.index.qingxuanzexitongziduan}
                                    style={{ width: '100%', maxWidth: 250 }}
                                    value={val}
                                    onChange={handleSystemFieldChange(index)}
                                    showSearch
                                    isVirtual
                                    dropdownMatchSelectWidth={false}
                                    filterOption={(inputValue, option) => {
                                        const { props } = option;
                                        const str = `${props.displayName}【${props.fieldCode}】`;
                                        return !!str.includes(inputValue);
                                    }}
                                    disabled={disabled}
                                    dropdownStyle={{ width: 350 }}>
                                    {systemFieds
                                        ?.filter((v) => v.selectType === 'FIELD_SYSTEM')
                                        ?.map(({ name, dName, type }) => {
                                            let isSelected = mappingFields.some((item) => item.fieldName === name);
                                            return (
                                                <Option key={name} value={name} disabled={isSelected}>
                                                    <sup style={{ color: 'blue' }}>{TYPE_MAP[type]?.displayName}</sup>
                                                    {`${dName}【${name}】`}
                                                </Option>
                                            );
                                        })}
                                </TooltipSelect>
                            </div>
                        );
                    }}
                />

                <Column
                    title={I18N.addmodify.serviceapi.caoZuo}
                    width={60}
                    dataIndex="operate"
                    textAlign="center"
                    key="operate"
                    render={(text, record, index) => {
                        return (
                            !record.disabled && (
                                <span
                                    style={{ cursor: 'pointer', color: 'rgb(46, 129, 247)' }}
                                    onClick={() => {
                                        removeField(handleIndex(index));
                                    }}>
                                    <Icon type="delete" />
                                </span>
                            )
                        );
                    }}
                />
            </Table>
            {!disabled && !!dataTable && (
                <div
                    className="u-add-policy"
                    onClick={() => {
                        setPageInfo({
                            ...pageInfo,
                            page: Math.ceil((mappingFields?.length + 1) / pageInfo.pageSize)
                        });
                        addField();
                    }}>
                    <Icon type="plus" />
                    {I18N.components.indextable.tianJia}</div>
            )}
        </>
    );
});
