import I18N from '@/utils/I18N';
import { useState, useEffect, forwardRef, useImperativeHandle, useMemo } from 'react';
import { Tag, Table, Row, Icon, Input, Tooltip, Select, Ellipsis } from 'tntd';
import VirtualSelect from '@tntd/ant3-virtual-select';
import TooltipSelect from '@tntd/tooltip-select';
import { TYPE_MAP } from '@/constants/common';

const { Option } = Select;

export default forwardRef((props, ref) => {
    const { value = [], onChange, disabled, systemFieds } = props;

    const [inPageInfo, setInPageInfo] = useState({ page: 1, pageSize: 10 });

    const addField = () => {
        const newValue = value?.slice();
        newValue.push({});
        onChange && onChange(newValue);
    };

    const handleInputChange = (v, i) => {
        const newValue = value?.slice();
        i = ((inPageInfo?.page || 1) - 1) * (inPageInfo.pageSize || 10) + i;
        newValue[i]['mappingName'] = v;
        onChange && onChange(newValue);
    };

    const deleteItem = (i) => {
        const newValue = value?.slice();
        i = ((inPageInfo?.page || 1) - 1) * (inPageInfo.pageSize || 10) + i;
        newValue.splice(i, 1);
        onChange && onChange(newValue);
    };

    return (
        <div className="dataTable-table">
            <Table
                title={() => (
                    <Row>
                        {I18N.featureservicenode.inmaptable.ruCanZiDuan}<Tag color="blue" style={{ marginLeft: '10px' }}>
                            {I18N.featureservicenode.inmaptable.shuRu}</Tag>
                    </Row>
                )}
                size="small"
                dataSource={value}
                pagination={{
                    pageSize: inPageInfo.pageSize,
                    current: inPageInfo.page,
                    total: value?.length,
                    onChange: (page, pageSize) => {
                        setInPageInfo({ page, pageSize });
                    }
                }}
                columns={[
                    {
                        title: I18N.datatablenode.inmaptable.ruCanZiDuanBiao,
                        dataIndex: 'mappingName',
                        // width: 350,
                        ellipsis: true,
                        render: (text, record, i) => {
                            return (
                                <TooltipSelect
                                    isVirtual
                                    placeholder={I18N.filter.index.qingxuanzexitongziduan}
                                    style={{ width: '100%', maxWidth: 350 }}
                                    dropdownStyle={{ width: 350 }}
                                    value={text}
                                    onChange={(e) => handleInputChange(e, i)}
                                    showSearch
                                    dropdownMatchSelectWidth={false}
                                    filterOption={(inputValue, option) => {
                                        const { props } = option;
                                        const str = `${props.displayName}【${props.fieldCode}】`;
                                        return !!str.includes(inputValue);
                                    }}
                                    disabled={disabled}>
                                    {systemFieds
                                        ?.filter((v) => v.selectType === 'FIELD_SYSTEM')
                                        ?.map(({ name, dName, type }) => {
                                            let isSelected = value.some((item) => item.mappingName === name);
                                            return (
                                                <Option key={name} value={name} disabled={isSelected}>
                                                    <sup style={{ color: 'blue' }}>{TYPE_MAP[type]?.displayName}</sup>
                                                    {`${dName}【${name}】`}
                                                </Option>
                                            );
                                        })}
                                </TooltipSelect>
                            );
                        }
                    },
                    {
                        title: I18N.addmodify.serviceapi.caoZuo,
                        dataIndex: 'type',
                        key: 'type',
                        // align: 'center',
                        width: 90,
                        // fixed: 'right',
                        render: (text, record, i) => {
                            return <Icon type="delete" style={{ color: '#3484F4' }} onClick={() => deleteItem(i)} />;
                        }
                    }
                ]}
                rowKey={(e, i) => i}
            />
            <div
                className="u-add-policy"
                onClick={() => {
                    setInPageInfo({
                        ...inPageInfo,
                        page: Math.ceil((value?.length + 1) / inPageInfo.pageSize)
                    });
                    addField();
                }}>
                <Icon type="plus" />
                {I18N.components.indextable.tianJia}</div>
        </div>
    );
});
