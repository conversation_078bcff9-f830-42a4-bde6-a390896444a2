import I18N from '@/utils/I18N';
import { useEffect, useRef, memo } from 'react';
import { Popover, Button } from 'tntd';
import AceEditor from '@/components/AceEditor';
import sqlExampleIcon from '@/sources/images/common/sql-example.svg';
import './SqlJob.less';

const exapmleStr =
    I18N.sqlcontent.sqljob.chuangJianFenQuBiao;

const SqlJob = memo(
    ({ allTableSource, value, onChange, disabled, leftPanelControl, isOpen, onRun, loading, onRef, example, placeholder, height }) => {
        const aceEditor = useRef();
        useEffect(() => {
            if (allTableSource?.length && aceEditor.current) {
                aceEditor.current.completers = []; // 重置
                aceEditor.current.completers.push({
                    getCompletions(editor, session, pos, prefix, callback) {
                        callback(
                            null,
                            [...allTableSource]?.map((v) => ({
                                value: v?.tableName,
                                name: String(v.tableName),
                                meta: I18N.detail.components.shuJuBiao
                            }))
                        );
                    }
                });
            }
        }, [allTableSource]);

        useEffect(() => {
            setTimeout(() => {
                aceEditor?.current?.resize();
            }, 1000);
        }, [isOpen]);

        return (
            <>
                <div className="sql-editor-container">
                    <AceEditor
                        mode="sql"
                        theme="cobalt"
                        fontSize="12"
                        name="sqlContent"
                        value={value}
                        onChange={(v) => {
                            // console.log(v);
                            onChange(v);
                        }}
                        onLoad={(editor) => {
                            onRef && onRef(editor);
                        }}
                        width="100%"
                        height="340px"
                        onRef={(ref) => {
                            aceEditor.current = ref;
                        }}
                        enableBasicAutocompletion={true}
                        enableLiveAutocompletion={true}
                        enableSnippets={true}
                        tabSize={4}
                        editorProps={{
                            $blockScrolling: Infinity
                        }}
                        readOnly={disabled}
                        setOptions={{
                            useSoftTabs: true,
                            displayIndentGuides: true
                        }}
                        placeholder={placeholder}
                    />
                    {!disabled && (
                        <>
                            <div className="sql-run-btn-wrap">
                                <Button
                                    onClick={onRun}
                                    loading={loading}
                                    className={`sql-run-btn ${loading ? 'readonly' : ''}`}
                                    size="small">
                                    {loading ? I18N.sqlcontent.sqljob.ceShiZhong : I18N.common.test}
                                </Button>
                            </div>
                            <div
                                className="shrink-control"
                                onClick={() => {
                                    leftPanelControl && leftPanelControl();
                                }}>
                                <span className={`arrow ${isOpen ? 'left' : 'right'}`} />
                            </div>
                        </>
                    )}

                    <Popover
                        placement="topLeft"
                        content={
                            <AceEditor
                                mode="sql"
                                theme="cobalt"
                                fontSize="12"
                                width="100%"
                                height={height || '200px'}
                                value={example || exapmleStr}
                                readOnly={true}
                            />
                        }
                        overlayStyle={{ width: 600 }}
                        overlayClassName="sql-example-overlay">
                        <a className="sql-example-btn">
                            <img src={sqlExampleIcon} />
                        </a>
                    </Popover>
                </div>
            </>
        );
    }
);

export default SqlJob;
