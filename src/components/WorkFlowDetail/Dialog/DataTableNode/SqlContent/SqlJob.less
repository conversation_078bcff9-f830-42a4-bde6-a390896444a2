.sql-editor-container{
	position: relative;
}
.sql-editor-container, .sql-example-overlay {
	.ace_scrollbar-v,.ace_scrollbar-h{
        &::-webkit-scrollbar {/*滚动条整体样式*/
            width: 6px;     /*高宽分别对应横竖滚动条的尺寸*/
            height: 6px;
        }
        &::-webkit-scrollbar-thumb {/*滚动条里面小方块*/
            border-radius: 10px;
            background:  #1F3A5A;
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            // background: #f00 ;
        }
        &::-webkit-scrollbar-track {/*滚动条里面轨道*/
            -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
            border-radius: 10px;
            // background:  #1F3A5A;
        }
    }
}

.sql-example-overlay {

	.ant-popover-inner-content {
		padding: 0;
		background: black;
	}
	.ant-popover-arrow {
		display: none;
	}
}

.sql-example-btn {
	position: absolute;
	right: 16px;
	bottom: 9px;
	height: 24px;
	z-index: 99;
	& img {
		vertical-align: baseline;
	}
}
.sql-run-btn-wrap{
	width:100%;
	height: 40px;
	background: #112C4B;
	.sql-run-btn{
		border: 1px solid rgba(178,190,205,1);
		font-size: 12px;
		color: #FFFFFF;
		background: transparent;
		margin-left: 16px;
		padding: 0 12px;
		&.readonly{
			opacity: .6;
		}

	}
}
.shrink-control{
	width: 10px;
	height: 60px;
	background: #2B3F5E;
	border-radius: 0px 2px 2px 0px;
	position: absolute;
	top:50%;
	margin-top:-30px;
	left: 0;
	z-index: 9;
	display: flex;
    align-items: center;
	cursor: pointer;
	.arrow{
		display: block;
		width: 6px;
		height: 6px;
		transform: rotate(45deg);
		&.right{
			border-top: 1.2px solid rgba(209,223,230,1);
			border-right: 1.2px solid rgba(209,223,230,1);
		}
		&.left{
			border-bottom: 1.2px solid rgba(209,223,230,1);
			border-left: 1.2px solid rgba(209,223,230,1);
			margin-left: 3px;
		}
	}
}
