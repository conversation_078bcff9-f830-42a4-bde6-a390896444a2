import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Table, Row, Tooltip, Button, Icon, Spin } from 'tntd';
import CopyIcon from '@/components/CopyIcon';
import service from '../../../service';
import SelectTable from './SelectTable';
import './TableSource.less';
export const dataTypeMap = {
    1: I18N.addmodify.serviceinput.ziFuXing,
    2: I18N.models.global.zhengShuXing,
    3: I18N.addmodify.serviceinput.xiaoShuXing,
    4: I18N.addmodify.serviceinput.riQiXing,
    5: I18N.models.global.meiJuXing,
    6: I18N.addmodify.serviceinput.buErXing
};
export default (props) => {
    const { allTableSource = [], allDataSourceList, parentForm, datasourceKey, onTableChange, jdbcTables = [], dataItem = {} } = props;
    const [selectTableVisible, setSelectTableVisible] = useState(false);
    const [dataSource, setDataSource] = useState([]);
    const [value, setValue] = useState([]);
    const [fieldMap, setFieldMap] = useState({});

    const fetchFields = ({ table, database }) => {
        if (fieldMap[table]?.length) {
            return;
        }
        service
            .getRealTableSchema({
                partition: true,
                isSqlTable: 0,
                searchValue: table,
                databaseName: database,
                datasourceId:
                    datasourceKey === 'dataSourceId'
                        ? parentForm.getFieldValue(datasourceKey)
                        : allDataSourceList?.find((v) => v.name === parentForm.getFieldValue(datasourceKey))?.id
            })
            .then((res) => {
                setFieldMap({
                    ...(fieldMap || {}),
                    [table]: res.data || []
                });
            });
    };

    useEffect(() => {
        if (Array.isArray(jdbcTables) && allTableSource.length) {
            const _data = allTableSource.filter((v) => jdbcTables.includes(v.key));
            setDataSource(_data);
        }
    }, [jdbcTables, allTableSource]);

    const tableFields = (record) => {
        if (!fieldMap[record?.table]) {
            return <Spin size="small" className="table-source-load" />;
        }
        return (
            <ul className="data-process-table-ul">
                {fieldMap[record?.table]?.map((v) => {
                    const upperType = dataTypeMap[v?.type]?.toUpperCase();
                    return (
                        <Tooltip
                            title={
                                <>
                                    {!!v?.partition && <sup style={{ color: 'rgb(82, 98, 199)', marginRight: '4px' }}>{I18N.sqlcontent.tablesource.fenQu}</sup>}
                                    {v?.name}({upperType})
                                </>
                            }
                            placement="topLeft">
                            <li key={v?.name}>
                                <Row type="flex" align="middle" className="contain-copy-icon">
                                    <div className="sub-table-data-name-info">
                                        {v?.name}
                                        <label className="sub-table-data-type">{upperType}</label>
                                    </div>
                                    {!!v?.partition && <span className="partition-field" />}
                                    <CopyIcon className="copy-icon sub-table-data-copy" value={v?.name} />
                                </Row>
                            </li>
                        </Tooltip>
                    );
                })}
            </ul>
        );
    };

    const expandIcon = ({ expanded }) => {
        if (expanded) {
            return <Icon type="caret-down" />;
        }
        return <Icon type="caret-right" />;
    };

    return (
        <>
            <Table
                className="data-process-table"
                pagination={false}
                showHeader={false}
                size="small"
                expandRowByClick={true}
                columns={[
                    {
                        dataIndex: 'tableName',
                        key: 'tableName',
                        ellipsis: true,
                        render: (tableName) => {
                            return (
                                <Tooltip title={tableName} placement="topLeft">
                                    <Row type="flex" align="middle" className="contain-copy-icon">
                                        <div className="table-data-name-info">{tableName}</div>
                                        <CopyIcon className="copy-icon table-data-copy" value={tableName} />
                                    </Row>
                                </Tooltip>
                            );
                        }
                    }
                ]}
                rowKey="tableName"
                expandedRowRender={tableFields}
                dataSource={dataSource}
                scroll={{ y: 330 }}
                expandIcon={(record) => expandIcon(record)}
                onExpand={(expanded, record) => {
                    if (expanded) {
                        fetchFields(record);
                    }
                }}
                footer={() => (
                    <Button size="small" disabled={dataItem?.id} onClick={() => setSelectTableVisible(true)}>
                        {I18N.sqlcontent.tablesource.xuanZeBiao}</Button>
                )}
            />
            <SelectTable
                value={value}
                dataSource={allTableSource}
                visible={selectTableVisible}
                onCancel={() => setSelectTableVisible(false)}
                onOk={(data) => {
                    setValue(data);
                    const allTableSourceMap = {};
                    allTableSource?.map((v) => {
                        allTableSourceMap[v.key] = v;
                    });
                    const curDataSource = [];
                    data?.map((key) => {
                        if (allTableSourceMap[key]) {
                            curDataSource.push(allTableSourceMap[key]);
                        }
                    });
                    setDataSource(curDataSource);
                    onTableChange && onTableChange(curDataSource);
                }}
            />
        </>
    );
};
