import I18N from '@/utils/I18N';
import { useState, useEffect, useCallback, useRef } from 'react';
import { Row, Col, message } from 'tntd';
import { find } from 'lodash';
import TableSource from './TableSource';
import SqlJob from './SqlJob';
import './index.less';

export default (props) => {
    const {
        allTableSource = [],
        disabled,
        parentForm,
        datasourceKey,
        allDataSourceList,
        height,
        example,
        placeholder,
        clear,
        loading,
        onTest
    } = props;
    const aceEditor = useRef();

    useEffect(() => {
        if (clear !== 0) {
            aceEditor.current.setValue('');
        }
    }, [clear]);

    const onRun = () => {
        const dataSourceId = parentForm.getFieldValue(datasourceKey);
        if (!dataSourceId) {
            message.error(I18N.sqlcontent.index.qingXianXuanZeShu);
        }
        onTest();
    };

    // 控制左侧是否展示
    const [isOpen, setIsOpen] = useState(true);
    const leftPanelControl = () => {
        setIsOpen(!isOpen);
    };

    return (
        <>
            <Row type="flex" className="mt12">
                {!disabled && (
                    <Col className="left-panel" style={!isOpen && { width: 0 }}>
                        <TableSource {...props} allTableSource={allTableSource} />
                    </Col>
                )}
                <Col className="right-panel">
                    <SqlJob
                        onRef={(ref) => {
                            aceEditor.current = ref;
                        }}
                        {...props}
                        isOpen={isOpen}
                        allTableSource={allTableSource}
                        leftPanelControl={leftPanelControl}
                        onRun={onRun}
                        loading={loading}
                        height={height}
                        example={example}
                        placeholder={placeholder}
                    />
                </Col>
            </Row>
        </>
    );
};
