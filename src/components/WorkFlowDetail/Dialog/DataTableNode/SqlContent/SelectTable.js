import I18N from '@/utils/I18N';
import { useState } from 'react';
import { Transfer, Table, Modal, Tooltip, Tag } from 'tntd';
import difference from 'lodash/difference';

const leftTableColumns = [
    {
        title: I18N.sqlcontent.selecttable.biaoMingCheng,
        dataIndex: 'tableName',
        ellipsis: true,
        render: (v, record) => {
            return (
                <Tooltip title={v} placement="topLeft">
                    <sup style={{ marginRight: '2px', color: '#5262C7' }}>{record?.registered ? I18N.sqlcontent.selecttable.yiZhuCe : I18N.sqlcontent.selecttable.weiZhuCe}</sup>
                    {v}
                </Tooltip>
            );
        }
    }
];
const rightTableColumns = [
    {
        title: I18N.sqlcontent.selecttable.biaoMingCheng,
        dataIndex: 'tableName',
        ellipsis: true,
        render: (v, record) => {
            return (
                <Tooltip title={v} placement="topLeft">
                    <sup style={{ marginRight: '2px', color: '#5262C7' }}>{record?.registered ? I18N.sqlcontent.selecttable.yiZhuCe : I18N.sqlcontent.selecttable.weiZhuCe}</sup>
                    {v}
                </Tooltip>
            );
        }
    }
];

export default (props) => {
    const { visible, onOk, onCancel, dataSource, value } = props;
    const [targetKeys, setTargetKeys] = useState(value || []);

    const TableTransfer = ({ leftColumns, rightColumns, ...restProps }) => (
        <Transfer {...restProps} showSelectAll={false}>
            {({ direction, filteredItems, onItemSelectAll, onItemSelect, selectedKeys: listSelectedKeys }) => {
                const columns = direction === 'left' ? leftColumns : rightColumns;
                const rowSelection = {
                    columnWidth: '30px',
                    onSelectAll: (selected, selectedRows) => {
                        const treeSelectedKeys = selectedRows.filter((item) => !item.disabled).map(({ key }) => key);
                        const diffKeys = selected
                            ? difference(treeSelectedKeys, listSelectedKeys)
                            : difference(listSelectedKeys, treeSelectedKeys);
                        onItemSelectAll(diffKeys, selected);
                    },
                    onSelect: ({ key }, selected) => {
                        onItemSelect(key, selected);
                    },
                    selectedRowKeys: listSelectedKeys
                };

                return (
                    <Table
                        rowSelection={rowSelection}
                        columns={columns}
                        dataSource={filteredItems}
                        size="small"
                        rowKey="tableName"
                        onRow={({ key }) => ({
                            onClick: () => {
                                onItemSelect(key, !listSelectedKeys.includes(key));
                            }
                        })}
                    />
                );
            }}
        </Transfer>
    );

    const onChange = (nextTargetKeys) => {
        setTargetKeys(nextTargetKeys);
    };
    return (
        <Modal
            title={I18N.sqlcontent.selecttable.xuanZeBiao}
            visible={visible}
            maskClosable={false}
            width={900}
            zIndex={1004}
            centered
            onCancel={onCancel}
            onOk={() => {
                onOk(targetKeys);
                onCancel();
            }}>
            <TableTransfer
                dataSource={dataSource}
                targetKeys={targetKeys}
                showSearch={true}
                filterOption={(inputValue, item) => item.tableName.indexOf(inputValue) !== -1}
                leftColumns={leftTableColumns}
                rightColumns={rightTableColumns}
                onChange={onChange}
            />
        </Modal>
    );
};
