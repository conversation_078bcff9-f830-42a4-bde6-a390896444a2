import I18N from '@/utils/I18N';
import { useEffect, useState, useRef } from 'react';
import { Modal, Button, Select, Spin, Form, message, Row, Col, Tag, Card, Input, Table, Radio } from 'tntd';
import { find } from 'lodash';
import TooltipSelect from '@tntd/tooltip-select';
import DataConvert from '@/components/WorkFlowEditor/DefaultDataConvert';
import { sliceName } from '@/components/WorkFlowEditor';
import { booleanList } from '@/constants';
import Filter from '@/components/Filter';
import InMapTable from './InMapTable';
import OutMapTable from './OutMapTable';
import OutMapTableNew from './OutMapTableNew';
import IndicatorsCascader from '@/components/IndicatorsCascader';
import service from '../../service';
import './index.less';
import { useGetRuleField } from '../../hooks/workflow';
import LogConsole from './ConsolePanel/LogConsole';
import SqlQuery from './SqlSet/SqlQuery';

const { Option } = Select;
const InputGroup = Input.Group;

const formItemLayout = {
    labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
    },
    wrapperCol: {
        xs: { span: 24 },
        sm: { span: 19 }
    }
};

// 数据集信息[C_S_DATAJSON]
const fixedField = {
    mappingName: 'data_collection_json',
    fieldName: ''
};

export default Form.create({ name: 'DataTableNode' })((props) => {
    const { form, onCancel, dialogShowInfo, editor, disabled, ...rest } = props;
    const { getFieldDecorator, validateFields, setFieldsValue, resetFields, getFieldsValue } = form;

    const { dataTable } = getFieldsValue();

    const { type, nodeId } = dialogShowInfo || {};
    const { data } = editor?.schema?.data?.nodesMap[nodeId] || {};

    const visible = type === 'DataTableServiceNode';

    let { ruleFieldList } = useGetRuleField();
    //动态字段
    let activeWord = [
        {
            dName: I18N.datatablenode.index.dongTaiBiaoMing,
            selectType: 'FIELD_SYSTEM',
            type: 'STRING',
            systemField: true,
            enumTypeValues: [],
            name: 'S_S_DYNAMICTABLENAME',
            sourceName: I18N.addmodify.inputlist.xiTongZiDuan,
            sourceKey: 'FIELD_SYSTEM',
            bizType: 'field'
        }
    ];
    let contextFields = {};

    const inTableRef = useRef();
    const outTableRef = useRef();

    const [loading, setLoading] = useState(false);
    const [dataSourceType, setDataSourceType] = useState('');
    const [dataTableType, setDataTableType] = useState('zhuce');
    const [dataTableName, setDataTableName] = useState('');
    const [variableModel, setVariableModel] = useState(false);
    const [contextField, setContextField] = useState({});
    const [testColumns, setTestColumns] = useState([]);
    const [testData, setTestData] = useState([]);
    const [testResult, setTestResult] = useState(false);
    const [outTableType, setOutTableType] = useState(1);
    const [outTableNum, setOutTableNum] = useState(null);

    const [dataTableInfo, setDataTableInfo] = useState({});
    const [allDataSource, setAllDataSource] = useState([]);
    const [allRealTables, setAllRealTables] = useState([]);
    const [realTableFields, setrRealTableFields] = useState([]);

    const [messages, setMessages] = useState([]);
    const [resData, setResData] = useState([]);
    const [logColumns, setLogColumns] = useState([]);
    const [showLog, setShowLog] = useState(false);
    const [activeKey, setActiveKey] = useState('log');
    const [logLoading, setLogLoading] = useState(false);
    const [configType, setConfigType] = useState('filterSet');
    const [dataItem, setDataItem] = useState({});
    const [jdbcTables, setJdbcTables] = useState([]);
    const [clear, setClear] = useState(0); // 选择数据源清空

    const getAllRealTables = async (id, type) => {
        const obj = {
            datasourceId: id,
            tableType: type,
            curPage: 1,
            pageSize: 19999,
            current: 1
        };

        return service.getAllRealTables(obj).then((res) => {
            if (res.success) {
                setAllRealTables(res?.data?.list || []);
                return res;
            }
            message.error(res.message);
        });
    };

    const getAllDataSource = () => {
        service
            .getAllDataSource()
            .then((res) => {
                if (res.success) {
                    setAllDataSource(res.data || []);
                }
            })
            .catch((res) => {
                message.error(res.message);
            });
    };

    const getRealTableSchema = async (obj) => {
        const { isSqlTable, realTable, schemas, datasourceId, databaseName } = obj;

        const params = {
            isSqlTable,
            datasourceId,
            schemas
        };

        if (isSqlTable) {
            params['searchValue'] = realTable;
        } else {
            params['databaseName'] = databaseName;
            params['searchValue'] = realTable;
        }

        return service
            .getRealTableSchema(params)
            .then((res) => {
                if (res.success) {
                    setrRealTableFields(res?.data || []);
                    return res;
                }
            })
            .catch((e) => {
                message.error(e.message);
            })
            .finally(() => {});
    };

    const refreshResult = () => {
        setActiveKey('log');
        setShowLog(false);
        setMessages([]);
        setResData([]);
        setLogColumns([]);
        setTestResult(false);
    };

    const initData = async () => {
        resetFields();
        getAllDataSource();

        setActiveKey('log');
        setShowLog(false);
        setMessages([]);
        setResData([]);
        setLogColumns([]);
        setTestResult(false);

        console.log('editor.schema.data.nodesMap[nodeId]: ', editor.schema.data.nodesMap[nodeId]);
        const { data, incomingFields, outgoingFields, name } = editor.schema.data.nodesMap[nodeId] || {};
        const {
            dataSourceId,
            type,
            dataTable,
            dataSourceType,
            dataTableId,
            dataTableType,
            filterCondition,
            contextField,
            outTableType,
            limitNum,
            showLog,
            messages,
            resData,
            logColumns,
            nodeName,
            sqlParams,
            previewTables,
            sqlContent
        } = data || {};

        let initValue = {
            type: 'filterSet',
            dataSourceId: undefined,
            dataTable: undefined,
            filterCondition: undefined,
            incomingFields: [],
            outgoingFields: []
        };

        // 初始化数据
        if (dataSourceId) {
            setDataTableType(dataTableType);
            setDataTableName(dataTable);
            setShowLog(false);
            setMessages(messages);
            setResData(resData);
            setLogColumns(logColumns);
            setTestResult(true);
            setContextField(contextField);
            setOutTableType(outTableType);
            setOutTableNum(limitNum);
            setLoading(true);
            setDataSourceType(dataSourceType);
            setDataTableInfo({ dataTableName: name, dataTableId });
            setConfigType(type || 'filterSet');
            const _jdbcTables = previewTables ? previewTables?.split(',') : [];
            setJdbcTables(_jdbcTables);

            const res = await getAllRealTables(dataSourceId, dataSourceType);
            const realTables = res?.data?.list || [];
            const curRealTable = realTables.find((i) => i.tableName === dataTable || i.realTable === dataTable) || {};
            await getRealTableSchema({ ...curRealTable });

            initValue = {
                dataSourceId
            };
            if (type === 'sqlSet') {
                initValue = {
                    ...initValue,
                    type,
                    nodeName,
                    sqlParams: sqlParams.map(({ type, name, value }) => {
                        return {
                            type,
                            expr: ['date', 'constant'].includes(type) ? `${name}=${value}` : undefined,
                            name: type === 'variable' ? name : undefined,
                            value: type === 'variable' ? value : undefined
                        };
                    }),
                    sqlContent,
                    incomingFields,
                    outgoingFields
                };
            } else {
                initValue = {
                    ...initValue,
                    type: 'filterSet',
                    dataTable,
                    filterCondition,
                    incomingFields,
                    outgoingFields
                };
            }
            setLoading(false);
        } else {
            setConfigType('filterSet');
            setDataSourceType('');
            setAllRealTables([]);
            setrRealTableFields([]);
        }
        console.log('initValue: ', initValue);
        setDataItem(initValue);
        setFieldsValue(initValue);
        setClear(0);
    };

    useEffect(() => {
        if (visible) {
            initData();
            return () => {
                resetFields();

                setDataSourceType('');
                setDataTableInfo({});
                setAllRealTables([]);
                setrRealTableFields([]);
                setConfigType('filterSet');
                setClear(0);
            };
        }
    }, [visible]);

    const commitModal = () => {
        validateFields((errors, data) => {
            const { dataTableId } = dataTableInfo;
            let inputParams = data?.sqlParams
                ?.filter((v) => v.type === 'variable')
                .map((item) => {
                    return {
                        value: item?.value,
                        fieldName: item?.name,
                        mappingName: item?.name
                    };
                });
            let filterSetInput = [];
            data?.filterCondition?.group?.map((v) => {
                v?.group?.map((item) => {
                    if (item.isConstant === 'false') {
                        filterSetInput.push({
                            fieldName: item?.value,
                            mappingName: item?.value
                        });
                    }
                });
            });
            if (!errors) {
                if (!testResult) {
                    return message.warning(I18N.datatablenode.index.ruCanCeShiWei);
                }
                let params = {
                    limitNum: outTableNum, //
                    outTableType, //
                    dataSourceId: data.dataSourceId, //
                    dataSourceType, //
                    showLog, //
                    type: configType,

                    messages: [],
                    resData: [],
                    logColumns,
                    testResult
                };
                if (configType === 'filterSet') {
                    editor.schema.data.nodesMap[nodeId].data = {
                        ...params,
                        isBatch: outTableType !== 1,
                        rowkey: data.incomingFields?.map((i) => i.mappingName)?.join(',') || '',
                        dataTableType,
                        dataTable: dataTableName,
                        dataTableId,
                        contextField,
                        filterCondition: data.filterCondition
                    };
                    // editor.schema.data.nodesMap[nodeId].incomingFields = data.incomingFields;
                    // editor.schema.data.nodesMap[nodeId].outgoingFields = data.outgoingFields;
                    // editor.schema.data.nodesMap[nodeId].name = sliceName(dataTableName);
                    editor.schema.data.nodesMap[nodeId].name = dataTableName;
                    editor.graph.node.nodes[nodeId].shape.select('text.flow-txt-node').node.innerHTML = sliceName(dataTableName);
                } else if (configType === 'sqlSet') {
                    editor.schema.data.nodesMap[nodeId].data = {
                        ...params,
                        sqlParams: data.sqlParams?.map(({ type, expr, name, value }) => ({
                            type,
                            name: type === 'variable' ? name : expr?.split('=')?.[0],
                            value: type === 'variable' ? value : expr?.split('=')?.[1]
                        })),
                        nodeName: data.nodeName,
                        sqlContent: data.sqlContent,
                        previewTables: jdbcTables && jdbcTables?.join(','),
                        contextField: {}
                    };
                    editor.schema.data.nodesMap[nodeId].name = data.nodeName;
                    editor.graph.node.nodes[nodeId].shape.select('text.flow-txt-node').node.innerHTML = data.nodeName;
                }
                editor.schema.data.nodesMap[nodeId].incomingFields = data.incomingFields || inputParams || filterSetInput;
                editor.schema.data.nodesMap[nodeId].outgoingFields = data.outgoingFields;
                DataConvert.rmNodeError(editor.graph.node.nodes[nodeId]);

                setShowLog(false);
                onCancel();
            }
        });
    };

    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.addmodify.index.quXiao}
        </Button>,
        <Button type="primary" onClick={commitModal} key="ok">
            {I18N.childflownode.index.queDing}
        </Button>
    ];

    const footerCancelDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.businesschannel.guanBi}
        </Button>
    ];

    const checkoutFilterCondition = (arr) => {
        let isNot = 0;
        arr?.map((v) => {
            if (
                v.group.some(
                    (item) =>
                        (['isEmpty', 'notEmpty'].includes(item.operator) && !item.key) ||
                        (!['isEmpty', 'notEmpty'].includes(item.operator) &&
                            (!item.key || !item.operator || !item.isConstant || !item.value))
                )
            ) {
                isNot += 1;
            }
        });
        return isNot;
    };

    const onRun = () => {
        const { dataSourceId, filterCondition } = getFieldsValue();
        if (!dataSourceId || !dataSourceType || !dataTableName || !filterCondition) {
            return message.warning(I18N.datatablenode.index.zhiXingTiaoJianTian);
        }
        if (checkoutFilterCondition(filterCondition?.group || [])) {
            return message.warning(I18N.datatablenode.index.zhiXingTiaoJianTian);
        }
        let hasVariable = [];
        filterCondition?.group?.map((v) => {
            v.group.map((item) => {
                if (
                    item.isConstant === 'false' &&
                    !['isEmpty', 'notEmpty'].includes(item.operator) &&
                    !hasVariable.some((e) => e.value === item.value)
                ) {
                    hasVariable.push(item);
                }
            });
        });

        contextFields = {};
        if (hasVariable.length || dataTableType === 'dongtai') {
            let columns =
                dataTableType === 'zhuce'
                    ? []
                    : [
                          {
                              title: I18N.constants.index.shuJuBiao,
                              key: 'S_S_DYNAMICTABLENAME',
                              render: () => {
                                  return (
                                      <TooltipSelect
                                          placeholder={I18N.datatablenode.index.qingXuanZeShuJu}
                                          style={{ width: 200 }}
                                          isVirtual
                                          dropdownMatchSelectWidth={false}
                                          optionFilterProp="children"
                                          showSearch
                                          onChange={(e) => {
                                              contextFields.S_S_DYNAMICTABLENAME = e;
                                              setContextField(contextFields);
                                          }}>
                                          {allRealTables?.map((item) => {
                                              return (
                                                  <Option key={item.realTable} value={item.tableName} item={item}>
                                                      {item.tableName}
                                                  </Option>
                                              );
                                          })}
                                      </TooltipSelect>
                                  );
                              }
                          }
                      ];
            hasVariable.map((v) => {
                const item = find(ruleFieldList, (it) => it.name === v.value) || {};
                columns.push({
                    title: item.dName + `【${item.name}】`,
                    key: v.value,
                    render: () => {
                        let dom;
                        let changeFun = (value) => {
                            contextFields[v.value] = value;
                            setContextField(contextFields);
                        };
                        if (['BOOLEAN'].includes(item.type)) {
                            dom = (
                                <Select placeholder={I18N.addmodify.qingXuanZe} style={{ width: 200 }} onChange={(e) => changeFun(e)}>
                                    {booleanList.map((v) => (
                                        <Option key={v} value={v}>
                                            {v}
                                        </Option>
                                    ))}
                                </Select>
                            );
                        } else if (['ENUM'].includes(item.type)) {
                            dom = (
                                <Select placeholder={I18N.addmodify.qingXuanZe} style={{ width: 200 }} onChange={(e) => changeFun(e)}>
                                    {(item.enumTypeValues || []).map((v) => (
                                        <Option key={v.value} value={v.value}>
                                            {v.description}
                                        </Option>
                                    ))}
                                </Select>
                            );
                        } else {
                            dom = (
                                <Input
                                    placeholder={I18N.addmodifymodal.index.qingShuRu}
                                    style={{ width: 200 }}
                                    onChange={(e) => changeFun(e.target.value)}
                                />
                            );
                        }
                        return dom ? dom : '--';
                    }
                });
            });
            setTestColumns(columns);
            setTestData([{}]);
            setVariableModel(true);
        } else {
            getDataTableTest('filterSet');
        }
    };

    const getDataTableTest = async (type) => {
        setVariableModel(false);
        setTestColumns([]);
        setTestData([]);
        setContextField({});
        setLogLoading(true);
        setMessages([]);
        setResData([]);
        setLogColumns([]);
        setActiveKey('log');
        const { dataSourceId, filterCondition, sqlParams, sqlContent } = getFieldsValue();
        let params = {};
        if (type === 'filterSet') {
            params = {
                dataSourceId,
                dataSourceType,
                dataTable: dataTableName,
                filterCondition: JSON.stringify(filterCondition),
                contextField
            };
        } else {
            params = {
                dataSourceId,
                dataSourceType,
                sqlParams: sqlParams?.map(({ type, expr, name, value }) => ({
                    type,
                    name: type === 'variable' ? name : expr?.split('=')?.[0],
                    value: type === 'variable' ? value : expr?.split('=')?.[1]
                })),
                sqlContent,
                previewTables: jdbcTables && jdbcTables?.join(','),
                contextField: {}
            };
        }
        return service.getDataTableTest(params).then((res) => {
            setLogLoading(false);
            setShowLog(true);
            if (res.success) {
                setTestResult(true);
                setMessages([res.data?.report]);
                let result = res.data?.result || [];
                setResData(result);
                let columns = [];
                (Object.keys(result[0] || {}) || []).map((v) => {
                    let item = {
                        title: v,
                        dataIndex: v,
                        key: v
                    };
                    columns.push(item);
                });
                setLogColumns(columns);
            } else {
                setTestResult(false);
                setMessages([
                    {
                        type: 'error',
                        msg: res.message
                    }
                ]);
                setResData([]);
                setLogColumns([]);
            }
        });
    };

    // 判断执行条件是否所有数据都填写
    const validateGroup = (group) => {
        for (const item of group) {
            if (item.group) {
                // 递归检查子组
                if (!validateGroup(item.group)) {
                    return false;
                }
            } else {
                // 检查每条数据的isConstant, key, operator, value
                const { isConstant, key, operator, value } = item;

                if (isConstant === undefined || key === undefined || operator === undefined || value === undefined) {
                    if ((operator === 'isEmpty' || operator === 'notEmpty') && !value) {
                        // 选择为空和不为空的时候无需在意value是否有值
                        return true;
                    }
                    return false;
                }
            }
        }
        return true;
    };

    const handleTableChange = (val) => {
        const _jdbcTables = (val || []).map((item) => item.key);
        setJdbcTables(_jdbcTables);
    };

    return (
        <Modal
            title={I18N.constants.index.shuJuBiao}
            visible={visible}
            maskClosable={false}
            zIndex={1002}
            width={1000}
            onCancel={onCancel}
            footer={disabled ? footerCancelDom : footerDom}>
            <Form {...formItemLayout} className="dataTable-form" onValuesChange={() => refreshResult()}>
                <Form.Item label={I18N.datatablenode.index.xuanZeShuJuYuan} className="left">
                    {getFieldDecorator('dataSourceId', {
                        rules: [
                            {
                                required: true,
                                message: I18N.datatablenode.index.qingXuanZeShuJu2
                            }
                        ]
                    })(
                        <TooltipSelect
                            onChange={(e, { props: { type } }) => {
                                setDataSourceType(type);
                                getAllRealTables(e, type);
                                setFieldsValue({
                                    outgoingFields: [],
                                    dataTable: undefined,
                                    filterCondition: undefined
                                });
                                setDataTableType('zhuce');
                                setDataTableName(undefined);
                                setrRealTableFields([]);
                                setMessages([]);
                                setResData([]);
                                setShowLog(false);
                                setContextField({});
                                setOutTableType(1);
                                setClear((count) => count + 1);
                            }}
                            placeholder={I18N.datatablenode.index.qingXuanZeShuJu2}
                            isVirtual
                            optionFilterProp="children"
                            showSearch
                            dropdownMatchSelectWidth={false}
                            style={{ width: '300px' }}
                            disabled={disabled}>
                            {allDataSource
                                ?.filter((i) => i.type === 'Mysql' || i.type === 'Inceptor')
                                ?.map((item) => {
                                    return (
                                        <Option key={item.id} value={item.id} id={item.id} type={item.type} name={item.name}>
                                            <sup style={{ color: '#5B8FF9' }}>{item.type} </sup>
                                            {item.name}
                                        </Option>
                                    );
                                })}
                        </TooltipSelect>
                    )}
                </Form.Item>
                {/* {!['Db2', 'hive', 'inceptor'].includes(dataSourceType) && (
                    <Form.Item label="选择数据表" className="right">
                        {getFieldDecorator('dataTable', {
                            rules: [
                                {
                                    required: true,
                                    message: '请选择数据表'
                                }
                            ]
                        })(
                            <TooltipSelect
                                placeholder="请选择数据表"
                                isVirtual
                                dropdownMatchSelectWidth={false}
                                optionFilterProp="children"
                                showSearch
                                style={{ width: '300px' }}
                                disabled={disabled}
                                onChange={(e, { props: { item } }) => {
                                    setDataTableInfo({ dataTableName: item.realTable, dataTableId: item.id });
                                    getRealTableSchema({ ...item });
                                    setDataTableName(item.realTable);
                                    setFieldsValue({
                                        outgoingFields: e ? [{ ...fixedField }] : []
                                    });
                                }}>
                                {allRealTables?.map((item) => {
                                    return (
                                        <Option key={item.realTable} value={item.realTable} item={item}>
                                            {item.realTable}
                                        </Option>
                                    );
                                })}
                            </TooltipSelect>
                        )}
                    </Form.Item>
                )} */}

                <Spin className="globalSpin" spinning={loading}>
                    {/* {['Db2', 'hive', 'inceptor'].includes(dataSourceType) ? ( */}
                    <Card
                        title={
                            <Row>
                                <Col span={18}>
                                    {I18N.featureservicenode.inmaptable.ruCanZiDuan}
                                    <Tag color="blue" style={{ marginLeft: '10px' }}>
                                        {I18N.featureservicenode.inmaptable.shuRu}
                                    </Tag>
                                </Col>
                                <Col span={6}>
                                    <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
                                        {getFieldDecorator('type', {
                                            initialValue: dataItem.type || 'filterSet',
                                            rules: [
                                                {
                                                    required: true
                                                }
                                            ]
                                        })(
                                            <Radio.Group
                                                onChange={(e) => {
                                                    let val = e.target.value;
                                                    setConfigType(val);
                                                    setMessages([]);
                                                    setResData([]);
                                                    setShowLog(false);
                                                    if (val === 'filterSet') {
                                                        setDataTableType('zhuce');
                                                    }
                                                }}
                                                disabled={disabled}>
                                                <Radio value="filterSet">{I18N.components.batchtemplate.tiaoJianPeiZhi}</Radio>
                                                <Radio value="sqlSet">{I18N.datatablenode.index.sQLJiaoBen}</Radio>
                                            </Radio.Group>
                                        )}
                                    </Form.Item>
                                </Col>
                            </Row>
                        }
                        size="small"
                        dataSource={[]}
                        pagination={false}
                        columns={[]}>
                        {configType === 'filterSet' ? (
                            <>
                                <Form.Item
                                    label={
                                        <span>
                                            <b
                                                style={{
                                                    color: '#D96156',
                                                    marginRight: '4px',
                                                    fontSize: '14px',
                                                    fontFamily: 'SimSun, sans-serif'
                                                }}>
                                                *
                                            </b>
                                            {I18N.datatablenode.index.xuanZeShuJuBiao}
                                        </span>
                                    }
                                    labelCol={{ span: 3 }}
                                    wrapperCol={{ span: 21 }}>
                                    <InputGroup compact>
                                        <Select
                                            value={dataTableType}
                                            disabled={disabled}
                                            onChange={(e) => {
                                                setDataTableType(e);
                                                setDataTableName(e === 'zhuce' ? undefined : 'S_S_DYNAMICTABLENAME');
                                                setFieldsValue({
                                                    outgoingFields: [],
                                                    filterCondition: undefined
                                                });
                                                setrRealTableFields([]);
                                                setMessages([]);
                                                setResData([]);
                                                setShowLog(false);
                                                setContextField({});
                                                setOutTableType(1);
                                            }}
                                            style={{ width: 120 }}>
                                            <Option value="zhuce">{I18N.datatablenode.index.zhuCeBiao}</Option>
                                            <Option value="dongtai">{I18N.datatablenode.index.dongTaiBiao}</Option>
                                        </Select>
                                        {dataTableType === 'zhuce' ? (
                                            <TooltipSelect
                                                placeholder={I18N.datatablenode.index.qingXuanZeShuJu}
                                                isVirtual
                                                dropdownMatchSelectWidth={false}
                                                optionFilterProp="children"
                                                showSearch
                                                style={{ width: '300px' }}
                                                disabled={disabled}
                                                value={dataTableName}
                                                onChange={(e, { props: { item } }) => {
                                                    setDataTableInfo({ dataTableName: item.tableName, dataTableId: item.id });
                                                    getRealTableSchema({ ...item });
                                                    setDataTableName(e);
                                                    setFieldsValue({
                                                        outgoingFields: [],
                                                        filterCondition: undefined
                                                    });
                                                    setrRealTableFields([]);
                                                    setMessages([]);
                                                    setResData([]);
                                                    setShowLog(false);
                                                    setContextField({});
                                                    setOutTableType(1);
                                                }}>
                                                {allRealTables?.map((item) => {
                                                    return (
                                                        <Option key={item.tableName} value={item.tableName} item={item}>
                                                            {item.tableName}
                                                        </Option>
                                                    );
                                                })}
                                            </TooltipSelect>
                                        ) : (
                                            <IndicatorsCascader
                                                options={activeWord}
                                                fieldNames={{ label: 'dName', value: 'name', children: 'data' }}
                                                placeholder={I18N.onecondition.index.qingXuanZe}
                                                showSearch
                                                disabled={true}
                                                value={'S_S_DYNAMICTABLENAME'}
                                                style={{ width: '100%', maxWidth: 233 }}
                                            />
                                        )}
                                    </InputGroup>
                                </Form.Item>
                                <Form.Item
                                    label={I18N.exclusiveline.index.zhiXingTiaoJian}
                                    labelCol={{ span: 3 }}
                                    wrapperCol={{ span: 21 }}>
                                    {getFieldDecorator('filterCondition', {
                                        rules: [
                                            {
                                                validator: (rule, value, callback) => {
                                                    if (!value || (value?.group && value.group.length === 0)) {
                                                        callback(I18N.exclusiveline.index.qingTianJiaZhiXing);
                                                    } else if (value?.group && value.group.length > 0) {
                                                        const flag = validateGroup(value?.group);
                                                        if (!flag) {
                                                            callback(I18N.datatablenode.index.zhiXingTiaoJianYou);
                                                        }
                                                    }
                                                    callback();
                                                }
                                            }
                                        ]
                                    })(
                                        <Filter
                                            disabled={disabled}
                                            dataTableType={dataTableType}
                                            realTableFields={realTableFields}
                                            systemFieds={ruleFieldList}
                                            onRun={onRun}
                                            setMessages={setMessages}
                                            setResData={setResData}
                                            setShowLog={setShowLog}
                                            setTestResult={setTestResult}
                                            setContextField={setContextField}
                                        />
                                    )}
                                </Form.Item>
                            </>
                        ) : (
                            <SqlQuery
                                {...rest}
                                form={form}
                                systemFieds={ruleFieldList}
                                dataItem={{ ...dataItem, ...data }}
                                allRealTablesDatasource={allRealTables}
                                jdbcTables={jdbcTables}
                                onTableChange={handleTableChange}
                                onTest={() => getDataTableTest('sqlSet')}
                                clear={clear}
                                loading={logLoading}
                                refreshResult={refreshResult}
                            />
                        )}
                        <LogConsole
                            messages={messages}
                            resData={resData}
                            logColumns={logColumns}
                            showLog={showLog}
                            activeKey={activeKey}
                            onActiveKeyChange={setActiveKey}
                            logLoading={logLoading}
                            testResult={testResult}
                        />
                    </Card>
                    {/* ) : (
                        <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className="table-form-tiem">
                            {getFieldDecorator('incomingFields', {
                                rules: [
                                    {
                                        validator: (rule, value, callback) => {
                                            // 校验 mappingName 是否为空
                                            value?.map((item) => {
                                                if (!item.mappingName) {
                                                    callback('入参字段标识不能为空');
                                                }
                                            });
                                            callback();
                                        }
                                    }
                                ],
                                validateTrigger: 'onSubmit'
                            })(<InMapTable visible={visible} disabled={disabled} systemFieds={ruleFieldList} ref={inTableRef} />)}
                        </Form.Item>
                    )} */}
                    <Form.Item labelCol={{ span: 0 }} wrapperCol={{ span: 24 }} className="mt10 table-form-tiem">
                        {getFieldDecorator('outgoingFields', {
                            rules: [
                                {
                                    validator: (rule, value, callback) => {
                                        if (!value || !value.length) {
                                            callback(I18N.datatablenode.index.chuCanZiDuanBu);
                                        }
                                        // 校验 mappingName, fieldName 是否为空
                                        value?.map((item) => {
                                            if (!item.mappingName) {
                                                callback(I18N.datatablenode.index.chuCanZiDuanBiao);
                                            }
                                            if (!item.fieldName) {
                                                callback(I18N.datatablenode.index.xiTongZiDuanBu);
                                            }
                                        });
                                        callback();
                                    }
                                }
                            ],
                            validateTrigger: 'onSubmit'
                        })(
                            // ['Db2', 'hive', 'inceptor'].includes(dataSourceType) ? (
                            <OutMapTableNew
                                visible={visible}
                                disabled={disabled}
                                systemFieds={ruleFieldList}
                                ref={outTableRef}
                                realTableSchema={realTableFields}
                                dataTableType={dataTableType}
                                outTableType={outTableType}
                                configType={configType}
                                setOutTableType={setOutTableType}
                                outTableNum={outTableNum}
                                setOutTableNum={setOutTableNum}
                            />
                            // ) : (
                            //     <OutMapTable
                            //         visible={visible}
                            //         disabled={disabled}
                            //         systemFieds={ruleFieldList}
                            //         ref={outTableRef}
                            //         realTableSchema={realTableFields}
                            //         dataTable={dataTable}
                            //     />
                            // )
                        )}
                    </Form.Item>
                </Spin>

                <Modal
                    title={I18N.common.test}
                    visible={variableModel}
                    maskClosable={false}
                    zIndex={1003}
                    width={800}
                    onCancel={() => {
                        setVariableModel(false);
                        setTestColumns([]);
                        setTestData([]);
                        setContextField({});
                    }}
                    footer={[
                        <Button
                            type="primary"
                            onClick={() => {
                                if (testColumns.some((v) => !contextField[v.key])) {
                                    return message.warning(I18N.datatablenode.index.qingTianXieZiDuan);
                                }
                                getDataTableTest('filterSet');
                            }}
                            key="ceshi">
                            {I18N.common.test}
                        </Button>
                    ]}>
                    <Table columns={testColumns} dataSource={testData} scroll={{ x: 'auto' }} />
                </Modal>
            </Form>
        </Modal>
    );
});
