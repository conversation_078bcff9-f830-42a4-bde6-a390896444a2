.res-table {
	position: relative;
    overflow: hidden;
	margin: 0;
    padding-bottom: 36px;
    .table-header {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        color: rgba(0, 0, 0, 0.85);
        background:  #FAFAFA;
        padding: 0 6px;
		line-height: 36px;
        position: relative;
        font-size: 12px;
        z-index: 1;
        // border-right: 1px solid #E1E6EE ;
        border-bottom: 1px solid #E1E6EE ;

		.resize-col{
			width:10px;
			height: 36px;
			display: block;
            top:0;
			right:-5px;
			cursor: ew-resize;
			position: absolute;
		}
    }
    .table-content {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding: 0 6px;
        font-size: 12px;
        background: #fff;
        line-height: 36px;
        border-bottom: 1px solid #E1E6EE;
        // border-right: 1px solid #ececec;
    }
    .ant-table-body {
        .table-cell {
            word-break: keep-all;
            white-space: nowrap;
            overflow: hidden;
            width: 100%;
        }
    }
    .ant-table-thead > tr > th .table-head-row {
        display: inline-block;
        vertical-align: top;
        word-break: keep-all;
        white-space: nowrap;
        // width: 87px;
        overflow: hidden;
    }

    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
        padding: 6px;
    }
    .react-resizable {
        position: relative;
        .react-resizable-handle {
            touch-action: none;
            position: absolute;
            width: 10px;
            height: 36px;
            right: -5px;
            cursor: ew-resize;
            top: 0;
        }
    }

    .res-table-toolbar {
		width: 100%;
        align-items: center;
        position: absolute;
        bottom: 0;
        justify-content: space-between;
		.ant-pagination{
			text-align: right;
			width: 100%;
		}
		.ant-pagination-total-text{
			float: left;
		}
    }
}
