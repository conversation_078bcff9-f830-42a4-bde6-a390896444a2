import I18N from '@/utils/I18N';
import { Component, Fragment } from 'react';
import { Pagination, Icon, Row, Descriptions, Col, Tooltip, Modal } from 'tntd';
import { Resizable } from 'react-resizable';
import './resTable.less';
import ReactVirtualSizeTable from '@/components/react-virtual-size-table';

export default class ResTable extends Component {
	state = {
		current: 1,
		pageSize: 10,
		widths: []
	};

	onPageChange = (current, pageSize) => {
		this.setState({ current, pageSize });
	};

	getTableData(data) {
		const { current, pageSize } = this.state;
		return data.slice((current - 1) * pageSize, current * pageSize);
	}

	onDoubleClick(record, columnNames) {
		const des = [];
		for (let x of columnNames) {
			des.push(
				<Descriptions.Item label={x}>
					<span style={{ overflow: 'auto', width: '100%' }}>{record[x]}</span>
				</Descriptions.Item>
			);
		}
		Modal.info({
			title: I18N.consolepanel.restable.shuJuXiangQing,
			width: 600,
			content: (
				<Descriptions bordered size="small" column={1}>
					{des}
				</Descriptions>
			)
		});
	}

	onResize = (index, value) => {
		const { widths } = this.state;
		widths[index] = value.size.width;
		this.setState({
			widths: { ...widths }
		});
	};

	renderCell = ({ columnIndex, key, rowIndex, style }, columnNames, tableData, widthArray) => {
		const { widths } = this.state;
		const field = columnNames[columnIndex];
		const text =
			rowIndex === 0
				? tableData[0][columnIndex]
				: tableData[rowIndex] && tableData[rowIndex][field];
		return (
			<Fragment key={key}>
				{rowIndex === 0 ? (
					<Resizable
						width={widths[columnIndex] || widthArray[columnIndex] || 140}
						height={36}
						onResize={(e, value) => {
							this.onResize(columnIndex, value);
						}}
					>
						<div style={style} className="table-header">
							{text}
						</div>
					</Resizable>
				) : (
					<Tooltip placement="topLeft" title={text}>
						<div
							style={style}
							className="table-content"
							onDoubleClick={() =>
								this.onDoubleClick(tableData[rowIndex], columnNames)
							}
						>
							{text}
						</div>
					</Tooltip>
				)}
			</Fragment>
		);
	};

	renderTable() {
		const {
			dataSource: { columnNames = [], data }
		} = this.props;
		const { widths, current, pageSize } = this.state;

		const tableData = this.getTableData(data);
		tableData.unshift(columnNames);
		const width = document.getElementsByClassName('res-table')[0]
			? document.getElementsByClassName('res-table')[0].clientWidth
			: 0;

		const needLimitColumns = !((width / columnNames?.length) > 140);

		// 同步
		const widthArray = columnNames.map(() => needLimitColumns ? 140 : (width / columnNames?.length));
		for (let key in widths) {
			widthArray[key] = widths[key];
		}
		return (
			<div className={'res-table'}>
				<ReactVirtualSizeTable
					onCell={(value) => this.renderCell(value, columnNames, tableData, widthArray)}
					columnCount={columnNames.length}
					widths={widthArray}
					height={252}
					fixHead={true}
					rowCount={tableData.length}
					rowHeight={36}
					width={width}
					style={{ width: '100%', padding: '0 20px' }}
				/>

				<Row type="flex" style={{ justifyContent: 'right' }} className="res-table-toolbar">
					<Pagination
						style={{ marginRight: 10, marginLeft: 10 }}
						pageSize={pageSize}
						total={data.length}
						current={current}
						showSizeChanger
						showQuickJumper
						onShowSizeChange={this.onPageChange}
						showTotal={(total) => I18N.template(I18N.consolepanel.restable.gongTOTA, { val1: total })}
						onChange={this.onPageChange}
						size="small"
					/>
				</Row>
			</div>
		);
	}

	render() {
		return <Fragment>{this.renderTable()}</Fragment>;
	}
}
