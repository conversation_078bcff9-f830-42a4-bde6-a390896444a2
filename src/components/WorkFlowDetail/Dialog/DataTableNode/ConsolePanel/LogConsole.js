import I18N from '@/utils/I18N';
import { Ta<PERSON>, Divider, Table } from 'tntd';
import {useMemo} from 'react';
import './LogConsole.less';

const { TabPane } = Tabs;

const LogConsole = ({ messages, resData, showLog, activeKey, onActiveKeyChange, logLoading, logColumns, testResult }) => {
    const renderMessage = (msg, i) => {
        if (msg.type === 'img') {
            return <img key={i} src={msg.src} />;
        } else if (msg.type === 'error') {
            return (
                <p key={i} style={{ color: 'red' }}>
                    {msg.msg}
                </p>
            );
        } else if (msg.type === 'warn') {
            return (
                <p key={i} style={{ color: 'orange' }}>
                    {msg.msg}
                </p>
            );
        } else if (msg.type === 'link') {
            return (
                <p
                    key={i}
                    dangerouslySetInnerHTML={{
                        __html: msg.msg
                    }}
                />
            );
        }
        return (
            <pre style={{ whiteSpace: 'pre-wrap' }} key={i}>
                {msg}
            </pre>
        );
    };

    const onChange = (activeKey) => {
        onActiveKeyChange(activeKey);
    };

    // 执行结果提示
    const resultNode = useMemo(() => {
        let color;
        let msg;
        if (logLoading) {
            color = '1890ff';
            msg = I18N.consolepanel.logconsole.ceShiZhong;
        } else if (testResult) {
            color = '#52c41a';
            msg = I18N.consolepanel.logconsole.ceShiChengGong;
        } else {
            color = '#f5222d';
            msg = I18N.consolepanel.logconsole.ceShiShiBai;
        }
        return <span style={{color: color}}>{msg}</span>
    }, [logLoading, testResult])

    // 处理结果tab的表结构
    const washResultColumns = (columns) => {
        // 如果返回值是boolean类型，则展示对应的true或者false
        return columns.map((item) => {
            return {
                ...item,
                render: (text) => {
                    return typeof text === 'boolean' ? text.toString() : text
                }
            }
        })
    }

    return (
        <div className="log-console-wrap">
            {showLog ? (
                <>
                    <Divider dashed style={{ fontSize: 14 }}>
                        {I18N.testservicemodal.index.ceShiJieGuo}</Divider>
                    <Tabs
                        hideAdd
                        className="log-bottom"
                        type="editable-card"
                        onChange={onChange}
                        activeKey={activeKey || 'log'}
                        style={{ height: '100%', border: '1px solid rgb(235, 238, 245)', paddingRight: '10px' }}
                        tabBarExtraContent={resultNode}>
                        <TabPane key="log" tab={I18N.components.offlinemodal.riZhi} closable={false}>
                            <div
                                className="log-console"
                                style={{
                                    overflow: 'auto',
                                    padding: 10
                                }}>
                                {(messages || []).map((msg, i) => {
                                    return renderMessage(msg, i);
                                })}
                            </div>
                        </TabPane>
                        <TabPane key="result" tab={I18N.modal.publishresult.jieGuo} visible={testResult} closable={false} style={{ overflowX: 'hidden' }}>
                            <Table columns={washResultColumns(logColumns)} dataSource={resData} scroll={{ x: 'auto' }} />
                        </TabPane>
                    </Tabs>
                </>
            ) : null}
        </div>
    );
};

export default LogConsole;
