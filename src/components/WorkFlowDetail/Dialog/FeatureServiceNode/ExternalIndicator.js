import I18N from '@/utils/I18N';
import { useState, useEffect, forwardRef, useImperativeHandle } from 'react';
import { Tag, Table, Row, Input, Select, Ellipsis, Icon } from 'tntd';
import { clone, cloneDeep } from 'lodash';
import TooltipSelect from '@tntd/tooltip-select';
import VirtualSelect from '@tntd/ant3-virtual-select';
import TdTag from '@/components/TdTag';
import { useGetGlobalStore } from '@/utils/utils';
import { DATA_TYPE_MAP } from '@/constants';
import './index.less';
const Option = Select.Option;

export default forwardRef((props, ref) => {
    const { onChange, value, outIndicator, indexList } = props;
    const [featureList, setFeatureList] = useState([]);
    useEffect(() => {
        onChange(outIndicator);
        setFeatureList(outIndicator);
    }, [outIndicator]);
    const defaultValue = {
        dName: undefined,
        name: undefined
    };
    // useEffect(()=>{
    //     setFeatureList(initInput);
    // },[initInput])
    const addField = () => {
        const newList = cloneDeep(featureList);
        newList.push(defaultValue);
        setFeatureList(newList);
    };
    const onTableChange = (val, index) => {
        let arr = val.split('&&');
        let newIndicator = cloneDeep(featureList);
        newIndicator[index].name = arr[0];
        newIndicator[index].dName = arr[1];
        setFeatureList(newIndicator);
        onChange(newIndicator);
    };
    return (
        <div className="feature-service-table">
            <Table
                title={() => (
                    <Row>
                        {I18N.featureservicenode.externalindicator.waiShuZhiBiao}
                        <Tag color="orange" style={{ marginLeft: '10px' }}>
                            {I18N.featureservicenode.outmaptable.shuChu}
                        </Tag>
                    </Row>
                )}
                size="small"
                dataSource={featureList}
                pagination={false}
                columns={[
                    {
                        title: I18N.components.monitoringanalyses.zhiBiaoMingCheng,
                        dataIndex: 'dName',
                        width: 360,
                        ellipsis: true,
                        render: (text, record, index) => {
                            return (
                                <TooltipSelect
                                    isVirtual={true}
                                    value={featureList[index].name ? featureList[index].name + '&&' + featureList[index].dName : ''}
                                    showSearch
                                    placeholder={I18N.components.indexquality.qingXuanZeZhiBiao} // 请选择要素名
                                    dropdownMatchSelectWidth={false}
                                    maxWidth={800}
                                    // optionFilterProp="children"
                                    filterOption={(input, option) =>
                                        option?.props?.children?.join()?.toLowerCase().indexOf(input.toLowerCase()) >= 0
                                    }
                                    onChange={(val) => onTableChange(val, index)}
                                    // onFocus={() => this.focusHandle()}
                                >
                                    {indexList?.map((item, index) => {
                                        return (
                                            <VirtualSelect.Option
                                                value={item.code + '&&' + item.displayName}
                                                key={index}
                                                disabled={featureList.filter((v) => v.name === item.code).length !== 0}
                                                title={`${item.displayName}`}>
                                                <TdTag data={item} showSourceName={false} />
                                                {item.displayName}
                                            </VirtualSelect.Option>
                                        );
                                    })}
                                </TooltipSelect>
                            );
                        }
                    },
                    {
                        title: I18N.components.monitoringanalyses.zhiBiaoBiaoZhi,
                        dataIndex: 'name',
                        key: 'name',
                        width: 360,
                        ellipsis: true,
                        render: (text) => {
                            return <Ellipsis title={text} />;
                        }
                    },
                    {
                        title: I18N.addmodify.serviceapi.caoZuo,
                        dataIndex: 'field',
                        key: 'field',
                        width: 250,
                        render: (text, record, index) => {
                            return (
                                <span
                                    style={{ cursor: 'pointer', color: 'rgb(46, 129, 247)' }}
                                    onClick={() => {
                                        const newList = cloneDeep(featureList);
                                        newList.splice(index, 1);
                                        setFeatureList(newList);
                                    }}>
                                    <Icon type="delete" />
                                </span>
                            );
                        }
                    }
                ]}
            />
            <div className="box-add">
                <div
                    className="box-div"
                    onClick={() => {
                        addField();
                    }}>
                    <Icon type="plus-circle" />
                    <span style={{ marginLeft: '7px' }}>{I18N.featureservicenode.externalindicator.tianJiaYiHangShu}</span>
                </div>
            </div>
        </div>
    );
});
