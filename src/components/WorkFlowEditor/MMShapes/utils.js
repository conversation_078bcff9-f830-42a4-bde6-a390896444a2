//根据节点名称变换节点形状
export const changeShapesAndName = (shape, displayName) => {
    let textW = getTextWidth(displayName, '12px');
    //是否换行
    let isWrap = textW > maxRectWidth - 40;
    if (textW >= maxRectWidth - 40) {
        isWrap = true;
    } else {
        //节点最小宽度为100即文本宽度最小为100-40（icon宽度）
        textW = Math.max(textW, 60);
    }
    let nodeWidth = isWrap ? maxRectWidth : textW + 40;
    shape.select('rect.flow-icon-node').attr({
        x: 0,
        y: 0,
        width: nodeWidth,
        height: isWrap ? 40 : 28,
        rx: 15,
        ry: 15
    });
    //修改图标以及图标文本的位置
    shape
        .select('g')
        .select('circle')
        .attr({
            cy: isWrap ? 20 : 14
        });
    shape
        .select('g')
        .select('text')
        .attr({
            y: isWrap ? 21 : 15
        });
    //修改右上角锚点
    shape[2].attr({
        width: 18,
        height: 18,
        x: nodeWidth - 14,
        y: -4
    });
    //修改文本节点
    shape[3].attr({
        width: isWrap ? maxRectWidth - 40 : textW + 3,
        height: isWrap ? 30 : 15,
        x: 28,
        y: isWrap ? 4 : 6
    });
    shape.select('.wrap-txt-node').attr({
        width: textW
    });
    //修改文本
    shape.select('.wrap-txt-node').node.innerHTML = displayName;
};
let canvas = document.createElement('canvas');
let context = canvas.getContext('2d');
export function getTextWidth(text, fontSize) {
    context.font = `${fontSize} sans-serif`; // 根据需要更改字体
    return context.measureText(text).width;
}
export const maxRectWidth = 130;
