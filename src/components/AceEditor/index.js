import React, { Component, Fragment } from 'react';
import ace from 'brace';
import 'brace/mode/sql';
import 'brace/snippets/sql';
import 'brace/snippets/text';
import 'brace/mode/java';
import 'brace/snippets/java';
import 'brace/ext/language_tools';
import 'brace/theme/textmate';
import 'brace/theme/cobalt';
import 'brace/theme/eclipse';
import 'brace/theme/terminal';
import 'brace/theme/xcode';
import 'brace/theme/monokai';
import 'brace/ext/searchbox';
import './index.less';
const Range = ace?.acequire ? ace?.acequire('ace/range')?.Range : ace?.Range;
export default class ReactAce extends Component {
    componentDidMount() {
        const {
            className,
            onBeforeLoad,
            onValidate,
            mode,
            focus,
            theme,
            fontSize,
            value,
            defaultValue,
            cursorStart,
            showGutter = true,
            wrapEnabled,
            showPrintMargin,
            keyboardHandler,
            scrollMargin = [0, 0, 0, 0],
            onLoad,
            commands,
            annotations,
            markers,
            placeholder
        } = this.props;

        this.editor = ace.edit(this.refEditor);
        if (onBeforeLoad) {
            onBeforeLoad(ace);
        }

        this.editor.getSession().setMode(`ace/mode/${mode}`);
        this.editor.setFontSize(fontSize);

        if (defaultValue || value) {
            this.editor.getSession().setValue(!defaultValue ? value : defaultValue, cursorStart);
        }
        const availableOptions = this.editor.$options;
        for (const key in this.props) {
            if (availableOptions.hasOwnProperty(key) && key !== 'mode' && key !== 'theme') {
                this.editor.setOption(key, this.props[key]);
            }
        }
        this.editor.renderer.setShowGutter(showGutter);
        this.editor.getSession().setUseWrapMode(wrapEnabled);
        this.editor.setShowPrintMargin(showPrintMargin);
        this.editor.on('focus', this.onFocus);
        this.editor.on('blur', this.onBlur);
        this.editor.on('copy', this.onCopy);
        this.editor.on('paste', this.onPaste);
        this.editor.on('change', this.onChange);
        this.editor.on('input', this.onInput);
        if (placeholder) {
            this.updatePlaceholder(this.editor, placeholder);
        }
        this.editor.getSession().selection.on('changeSelection', this.onSelectionChange);
        this.editor.getSession().selection.on('changeCursor', this.onCursorChange);
        if (onValidate) {
            this.editor.getSession().on('changeAnnotation', () => {
                const annotations = this.editor.getSession().getAnnotations();
                this.props.onValidate(annotations);
            });
        }
        this.editor.renderer.setScrollMargin(scrollMargin[0], scrollMargin[1], scrollMargin[2], scrollMargin[3]);
        this.editor.session.on('changeScrollTop', this.onScroll);
        this.editor.getSession().setAnnotations(annotations || []);
        if (markers && markers.length > 0) {
            this.handleMarkers(markers);
        }

        this.handleOptions(this.props);

        if (Array.isArray(commands)) {
            commands.forEach((command) => {
                if (typeof command.exec === 'string') {
                    this.editor.commands.bindKey(command.bindKey, command.exec);
                } else {
                    this.editor.commands.addCommand(command);
                }
            });
        }

        if (keyboardHandler) {
            this.editor.setKeyboardHandler(`ace/keyboard/${keyboardHandler}`);
        }

        if (className) {
            this.refEditor.className += ` ${className}`;
        }

        if (onLoad) {
            onLoad(this.editor);
        }

        this.editor.resize();
        if (theme) {
            this.editor.setTheme(`ace/theme/${theme}`);
        }

        if (focus) {
            this.editor.focus();
        }
        this.props.onRef && this.props.onRef(this.editor);
    }

    componentWillUnmount() {
        this.editor.destroy();
        this.editor = null;
    }

    onChange = (event) => {
        if (this.props.onChange && !this.silent) {
            const value = this.editor.getValue();
            this.props.onChange(value, event);
        }
    };

    onSelectionChange = (event) => {
        if (this.props.onSelectionChange) {
            const value = this.editor.getSelection();
            this.props.onSelectionChange(value, event);
        }
    };

    onCursorChange = (event) => {
        if (this.props.onCursorChange) {
            const value = this.editor.getSelection();
            this.props.onCursorChange(value, event);
        }
    };

    onInput = (event) => {
        if (this.props.onInput) {
            this.props.onInput(event);
        }
        if (this.props.placeholder) {
            this.updatePlaceholder();
        }
    };

    onFocus = (event) => {
        if (this.props.onFocus) {
            this.props.onFocus(event, this.editor);
        }
    };

    onBlur = (event) => {
        if (this.props.onBlur) {
            this.props.onBlur(event, this.editor);
        }
    };

    onCopy = (text) => {
        if (this.props.onCopy) {
            this.props.onCopy(text);
        }
    };

    onPaste = (text) => {
        if (this.props.onPaste) {
            this.props.onPaste(text);
        }
    };

    onScroll = (event) => {
        if (this.props.onScroll) {
            this.props.onScroll(this.editor);
        }
    };

    handleOptions(props) {
        if (!props.setOptions) return;
        const setOptions = Object.keys(props.setOptions);
        for (let y = 0; y < setOptions.length; y++) {
            this.editor.setOption(setOptions[y], props.setOptions[setOptions[y]]);
        }
    }

    handleMarkers(markers) {
        // remove foreground markers
        let currentMarkers = this.editor.getSession().getMarkers(true);
        for (const i in currentMarkers) {
            if (currentMarkers.hasOwnProperty(i)) {
                this.editor.getSession().removeMarker(currentMarkers[i].id);
            }
        }
        // remove background markers except active line marker and selected word marker
        currentMarkers = this.editor.getSession().getMarkers(false);
        for (const i in currentMarkers) {
            if (
                currentMarkers.hasOwnProperty(i) &&
                currentMarkers[i].clazz !== 'ace_active-line' &&
                currentMarkers[i].clazz !== 'ace_selected-word'
            ) {
                this.editor.getSession().removeMarker(currentMarkers[i].id);
            }
        }
        // add new markers
        markers.forEach(({ startRow, startCol, endRow, endCol, className, type, inFront = false }) => {
            const range = new Range(startRow, startCol, endRow, endCol);
            this.editor.getSession().addMarker(range, className, type, inFront);
        });
    }

    updatePlaceholder() {
        // Adapted from https://stackoverflow.com/questions/26695708/how-can-i-add-placeholder-text-when-the-editor-is-empty

        const { editor } = this;
        const { placeholder } = this.props;

        const showPlaceholder = !editor.session.getValue().length;
        let node = editor.renderer.placeholderNode;
        if (!showPlaceholder && node) {
            editor.renderer.scroller.removeChild(editor.renderer.placeholderNode);
            editor.renderer.placeholderNode = null;
        } else if (showPlaceholder && !node) {
            node = editor.renderer.placeholderNode = document.createElement('div');
            node.textContent = placeholder || '';
            node.className = 'ace_comment ace_placeholder';
            node.style.padding = '0 9px';
            node.style.position = 'absolute';
            node.style.zIndex = '3';
            editor.renderer.scroller.appendChild(node);
        } else if (showPlaceholder && node) {
            node.textContent = placeholder;
        }
    }

    updateRef = (item) => {
        this.refEditor = item;
    };

    componentDidUpdate(prevProps) {
        const { props } = this;
        if (prevProps.readOnly !== props.readOnly) {
            this.editor.setOption('readOnly', props.readOnly);
        }
        if (prevProps.name !== props.name) {
            this.editor.setOption('name', props.name);
        }
        if (props.mode !== prevProps.mode) {
            this.editor.getSession().setMode(`ace/mode/${props.mode}`);
        }
        if (props.theme !== prevProps.theme) {
            this.editor.setTheme(`ace/theme/${props.theme}`);
        }
        if (props.fontSize !== prevProps.fontSize) {
            this.editor.setFontSize(props.fontSize);
        }
        if (prevProps.height !== this.props.height || prevProps.width !== this.props.width) {
            this.editor.resize();
        }

        if (prevProps.placeholder !== props.placeholder) {
            this.updatePlaceholder();
        }
    }

    render() {
        const { name, width, height, style } = this.props;
        const divStyle = { width, height, ...style };
        return <div ref={this.updateRef} id={name} style={divStyle} />;
    }
}
