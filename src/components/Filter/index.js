import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Select, Input, Tooltip, Icon, Row, Col, Radio, message } from 'tntd';
import { find, get, set, filter, cloneDeep } from 'lodash';
import cn from 'classnames';
import { operatorMap, logicEnum, logicEnumNew } from '@/constants';
import TooltipSelect from '@tntd/tooltip-select';
import { TYPE_MAP } from '@/constants/common';
import './index.less';

const Option = Select.Option;
const defaultValue = {
    logic: 'AND',
    group: [
        {
            logic: 'AND',
            group: [{ isConstant: 'false' }]
        }
    ]
};
const validLength = (value, max = 200) => {
    if (value?.length > max) {
        message.warning(I18N.filter.index.canShuZhiChangDu + max);
        return false;
    }
    return true;
};
const validFormat = (value, format = /^[a-z][a-z0-9_]*$/) => {
    if (format.test(value)) {
        return true;
    }
    return false;
};

/**
 * value: {logic: '', group: [{}]}
 */
function FilterInput({ value: pValue, onChange, className, isEn, useNewInteraction = false, onRun, ...otherProps }) {
    let [value, setValue] = useState(pValue);
    useEffect(() => {
        setValue(pValue);
    }, [pValue]);
    const showFilter = value && value.group && value.group.length > 0;
    const changeValue = () => {
        const newValue = { ...value };
        setValue(newValue);
        onChange(newValue);
        otherProps.setMessages([]);
        otherProps.setResData([]);
        otherProps.setShowLog(false);
        otherProps.setContextField({});
        otherProps.setTestResult(false);
    };

    const operatorWidth = isEn ? 65 : 55;

    return (
        <div className={cn('filter-input', className)}>
            {showFilter && (
                <>
                    {useNewInteraction && (
                        <Radio.Group
                            onChange={(e) => {
                                // onChange({ ...value, logic: e.target.value });
                                set(value, 'logic', e.target.value);
                                changeValue(value);
                            }}
                            value={value.logic || ''}
                            disabled={otherProps.disabled}
                            className="mb10">
                            {logicEnumNew.map(({ value, label, disabled }) => (
                                <Radio value={value} key={value} disabled={disabled}>
                                    {label}
                                </Radio>
                            ))}
                        </Radio.Group>
                    )}
                    <div className="filter-input-warp">
                        {!useNewInteraction && value.group.length > 1 && (
                            <div className="operator" style={{ width: operatorWidth }}>
                                <Select
                                    dropdownMatchSelectWidth={false}
                                    onChange={(logic) => {
                                        set(value, 'logic', logic);
                                        changeValue(value);
                                    }}
                                    value={value.logic || ''}
                                    disabled={otherProps.disabled}>
                                    {logicEnum.map(({ label, value }) => (
                                        <Option key={value} value={value}>
                                            {label}
                                        </Option>
                                    ))}
                                </Select>
                            </div>
                        )}
                        <div
                            className={cn('enter-group-warp', {
                                'filter-list': !useNewInteraction && value.group?.length > 1
                            })}>
                            {(value.group || []).map((cond, i) => (
                                <EnterGroupCondition
                                    {...otherProps}
                                    useNewInteraction={useNewInteraction}
                                    sortNum={i + 1}
                                    operatorWidth={operatorWidth}
                                    key={i}
                                    value={cond}
                                    onChange={(v) => {
                                        value.group[i] = v;
                                        changeValue(value);
                                    }}
                                    onDelete={() => {
                                        value.group.splice(i, 1);
                                        changeValue(value);
                                    }}
                                />
                            ))}
                        </div>
                    </div>
                </>
            )}
            {!otherProps.disabled && (
                <div className="add">
                    <a
                        onClick={() => {
                            onRun();
                        }}>
                        <b style={{ color: '#D96156', marginRight: '4px', fontSize: '14px', fontFamily: 'SimSun, sans-serif' }}>*</b>
                        {I18N.filter.index.zhixingtiaojianceshi}
                    </a>
                    <a
                        className="ml20"
                        onClick={() => {
                            const showFilter = value && value.group && value.group.length > 0;
                            if (showFilter) {
                                value.group = [
                                    ...get(value, 'group', []),
                                    {
                                        logic: 'AND',
                                        group: [{ isConstant: 'false' }]
                                    }
                                ];
                            } else {
                                value = cloneDeep(defaultValue);
                            }
                            changeValue(value);
                        }}>
                        {I18N.filter.index.zengJiaTiaoJianZu}
                    </a>
                </div>
            )}
        </div>
    );
}

export function EnterGroupCondition({
    value: sValue,
    onChange,
    onDelete,
    showAdd = false,
    showAddOr = true,
    disabledOperator = false,
    operatorWidth,
    useNewInteraction = false,
    sortNum,
    ...otherProps
}) {
    const value = sValue ? sValue : showAddOr ? { logic: 'OR', group: [] } : { logic: 'AND', group: [] };
    const { group = [], logic } = value;
    return (
        <>
            <div
                className={cn('enter-group', {
                    flex: showAddOr,
                    'filter-list-item': !useNewInteraction
                })}>
                {useNewInteraction && <div className="enter-group-index">{sortNum}</div>}
                {group.length > 1 && showAddOr && (
                    <div className="operator" style={{ width: operatorWidth }}>
                        <Select
                            dropdownMatchSelectWidth={false}
                            onChange={(v) => onChange({ ...value, logic: v })}
                            value={logic || ''}
                            disabled={disabledOperator || otherProps.disabled}>
                            {logicEnum.map(({ label, value }) => (
                                <Option key={value} value={value}>
                                    {label}
                                </Option>
                            ))}
                        </Select>
                    </div>
                )}
                {!showAddOr && (
                    <Radio.Group
                        onChange={(e) => {
                            onChange({ ...value, logic: e.target.value });
                        }}
                        value={logic || ''}>
                        {logicEnumNew.map(({ value, label, disabled }) => (
                            <Radio value={value} key={value} disabled={disabled}>
                                {label}
                            </Radio>
                        ))}
                    </Radio.Group>
                )}
                <ul
                    className={cn('border-dashed', {
                        'filter-list': group.length > 1,
                        'filter-list-no-after': !showAddOr
                    })}>
                    {group.map((item, index) => (
                        <li
                            key={index}
                            className={cn({
                                'filter-list-item': group.length > 1
                            })}>
                            <EnterCondition
                                filterIndex={index}
                                {...otherProps}
                                value={item}
                                onChange={(v) => {
                                    const newValue = [...group];
                                    newValue[index] = v;
                                    onChange({ ...value, group: newValue });
                                }}
                                onDelete={() => {
                                    if (value.group.length <= 1 && onDelete) {
                                        onDelete();
                                    } else {
                                        onChange({
                                            ...value,
                                            group: filter(value.group, (v, i) => index !== i)
                                        });
                                    }
                                }}
                                onAdd={() => {
                                    onChange({ ...value, group: [...value.group, { isConstant: 'false' }] });
                                }}
                            />
                        </li>
                    ))}
                </ul>
            </div>
            {(!group || group.length === 0) && showAdd && !otherProps.disabled && (
                <a
                    onClick={() => {
                        // value.group.push({});
                        onChange({ ...value, group: [...value.group, {}] });
                    }}>
                    <Icon type="plus-square" /> {I18N.filter.index.zengJiaTiaoJian}
                </a>
            )}
        </>
    );
}

export function EnterCondition({ value, onChange, onDelete, onAdd, ...otherProps }) {
    const { fields = [], realTableFields, dataTableType, disabled, systemFieds } = otherProps;
    let operatorList = [];
    let dataType = '';
    if (value.key) {
        dataType = get(find(realTableFields, (it) => it.name === value.key) || {}, 'typeName', '') || 'OTHER';
        if (dataType) {
            if (dataTableType === 'zhuce') {
                for (const item in operatorMap) {
                    // 如果dataType的文案中包含了operatorMap中的某个key则直接匹配
                    if (dataType.toUpperCase().includes(item)) {
                        operatorList = operatorMap[item] || {};
                        break;
                    }
                }
            } else {
                operatorList = get(operatorMap, 'OTHER', '') || {}
            }
            // operatorList = get(operatorMap, dataTableType === 'zhuce' ? dataType.toUpperCase() : 'OTHER', '') || {};
        }
    }

    const triggerChange = (changedValue) => {
        onChange({ ...value, ...changedValue });
    };

    const changeRightValue = (e) => {
        triggerChange({
            value: e && e.target ? e.target.value : e
        });
    };

    // 判断是否是布尔值
    const checkBoolean = (_key) => {
        const cur = realTableFields.find(item => item.name === _key)
        if (cur && cur.typeName.toUpperCase().includes('BOOLEAN') ) {
            return true
        } 
            return false
        
    }

    return (
        <div className="enter-item">
            <Row gutter={10}>
                <Col span={6}>
                    {dataTableType === 'zhuce' ? (
                        <Select
                            dropdownMatchSelectWidth={false}
                            allowClear={false}
                            placeholder={I18N.filter.index.qingxuanzebiaoziduan}
                            value={value.key}
                            onChange={(v) => {
                                const selectItem = find(fields, (it) => it.value === v) || {};
                                const isChangeType = v && dataType !== get(selectItem, 'dataType');
                                triggerChange({
                                    key: v,
                                    operator: isChangeType ? undefined : value.operator,
                                    value: isChangeType ? undefined : value.value
                                });
                            }}
                            showSearch
                            optionFilterProp="children"
                            disabled={disabled}
                            dropdownClassName="max-width-800">
                            {realTableFields
                                ?.filter(({ disabled }) => !disabled)
                                .map(({ name, typeName }) => (
                                    <Option key={name} value={name} title={name}>
                                        <sup style={{ color: 'blue' }}>{TYPE_MAP[typeName]?.displayName || typeName}</sup>
                                        {name}
                                    </Option>
                                ))}
                        </Select>
                    ) : (
                        <Input
                            placeholder={I18N.filter.index.qingshurubiaoziduan}
                            value={value.key}
                            onChange={(e) => {
                                const val = e?.target?.value;
                                if (val && !validFormat(val, /^[a-z][a-z0-9_]*$/)) {
                                    return message.warning(I18N.filter.index.biaoZiDuanMingJin);
                                }
                                triggerChange({
                                    key: val,
                                    operator: undefined,
                                    value: undefined
                                });
                            }}
                            disabled={disabled}
                        />
                    )}
                </Col>
                <Col span={4}>
                    <Select
                        dropdownMatchSelectWidth={false}
                        value={value.operator}
                        onChange={(value) => {
                            triggerChange({
                                operator: value
                            });
                        }}
                        placeholder={I18N.filter.index.qingxuanzetiaojian}
                        disabled={disabled}>
                        {Object.keys(operatorList).map((key) => (
                            <Option key={key} value={key}>
                                {operatorList[key]}
                            </Option>
                        ))}
                    </Select>
                </Col>
                {['isEmpty', 'notEmpty'].includes(value.operator) ? null : (
                    <>
                        <Col span={5}>
                            <Select
                                dropdownMatchSelectWidth={false}
                                value={value.isConstant}
                                onChange={(value) => {
                                    triggerChange({
                                        isConstant: value,
                                        value: undefined
                                    });
                                }}
                                placeholder={I18N.filter.index.qingxuanzetiaojian}
                                disabled={disabled}>
                                <Option value="false">{I18N.filter.index.bianliang}</Option>
                                <Option value="true">{I18N.filter.index.changliang}</Option>
                            </Select>
                        </Col>
                        <Col span={7}>
                            {value.isConstant === 'false' ? (
                                <TooltipSelect
                                    isVirtual
                                    placeholder={I18N.filter.index.qingxuanzexitongziduan}
                                    dropdownStyle={{ width: 350 }}
                                    value={value.value}
                                    onChange={changeRightValue}
                                    showSearch
                                    dropdownMatchSelectWidth={false}
                                    filterOption={(inputValue, option) => {
                                        const { props } = option;
                                        const str = props.children[1];
                                        return !!str.includes(inputValue);
                                    }}
                                    disabled={disabled}>
                                    {systemFieds
                                        ?.filter((v) => v.selectType === 'FIELD_SYSTEM')
                                        ?.map(({ name, dName, type }) => {
                                            return (
                                                <Option key={name} value={name}>
                                                    <sup style={{ color: 'blue' }}>{TYPE_MAP[type]?.displayName}</sup>
                                                    {`${dName}【${name}】`}
                                                </Option>
                                            );
                                        })}
                                </TooltipSelect>
                            ) : 
                            !checkBoolean(value.key) ? (
                                <Input
                                    autoComplete="off"
                                    value={value.value}
                                    maxLength={200}
                                    onChange={(e) => {
                                        const val = e?.target?.value;
                                        if (!validLength(val)) return;
                                        changeRightValue(val);
                                    }}
                                    placeholder={I18N.filter.index.qingShuRu}
                                    disabled={disabled}
                                />
                            ) : (
                                <Select
                                    style={{ width: '75%' }}
                                    value={value.value}
                                    onChange={changeRightValue}
                                    showSearch
                                    optionFilterProp="children"
                                    disabled={disabled}
                                    placeholder={I18N.filter.index.qingXuanZe}
                                    dropdownMatchSelectWidth={false}>
                                    <Option value="1">{I18N.filter.index.yes}</Option>
                                    <Option value="0">{I18N.filter.index.no}</Option>
                                </Select>
                            )}
                        </Col>
                    </>
                )}
                {!disabled && (
                    <Col span={2}>
                        <div className="mw-50">
                            <Tooltip placement="top" title={I18N.filter.index.tianJiaTiaoJian}>
                                <a onClick={() => onAdd && onAdd()} className="mr5 lh-28 operate-btn">
                                    <Icon type="plus-square" />
                                </a>
                            </Tooltip>
                            <Tooltip placement="top" title={I18N.filter.index.shanChuCiTiaoJian}>
                                <a onClick={() => onDelete && onDelete()} className="lh-28 operate-btn">
                                    <Icon type="delete" />
                                </a>
                            </Tooltip>
                        </div>
                    </Col>
                )}
            </Row>
        </div>
    );
}
export default FilterInput;
