.filter-input {
  &-warp {
    display: flex;
  }
  .add {
    background: rgba(241, 242, 245, 0.5);
    border-radius: 2px;
    padding: 4px 12px 4px 0;
    line-height: 20px;
    // margin: 8px 0;
    margin: 4px 0;
  }
}
.filter-list-no-after {
  &::after {
    display: none;
  }
}
.filter-list {
  position: relative;
  &::after {
    position: absolute;
    content: " ";
    width: 20px;
    height: 1px;
    background-color: #B2BECD;
    left: -40px;
    top: 50%;
    transform: translate(0px, -50%);
  }
  > .filter-list-item {
    position: relative;
    &::before {
      position: absolute;
      content: " ";
      width: 20px;
      height: 1px;
      background-color: #B2BECD;
      left: -20px;
      top: 50%;
      transform: translate(0px, -50%);
    }
    &::after {
      position: absolute;
      content: " ";
      width: 1px;
      height: 100%;
      background-color: #B2BECD;
      left: -20px;
      top: 0;
    }
    &:first-child {
      &::after {
        height: 50%;
        top: 50%;
      }
    }
    &:last-child {
      &::after {
        height: 50%;
      }
    }
  }
}
.enter-group {
  &-warp,
  .border-dashed {
    flex: 1;
    margin-bottom: 0;
    min-width: 0;
    > li {
      line-height: 36px;
    }
    .mw-50 {
      min-width: 50px;
    }
  }
  &.flex {
    display: flex;
    align-items: center;
  }
}
.filter-input,
.enter-group {
  &-index {
    width: 26px;
    height: 26px;
    line-height: 26px;
    background: #497BD0;
    border-radius: 2px;
    font-size: 14px;
    color: #FFFFFF;
    letter-spacing: 0;
    text-align: center;
    font-weight: 500;
    margin-right: 10px;
  }
  .operator {
    width: 55px;
    margin-right: 40px;
    position: relative;
    > .ant-select {
      position: absolute;
      top: 50%;
      transform: translate(0, -50%);
    }
  }
  .operate-btn {
    color: #17233D;
  }
}
.array-item:nth-child(even) .filter-input .operator&:before {
  background: #fafafa;
}
.filter-input-detail {
  color: #455064;
  p {
    margin-bottom: 5px;
    color: #455064;
  }
  .c-blue {
    margin: 0 4px;
  }
  .enter-item-detail {
    word-break: break-all;
  }
}
