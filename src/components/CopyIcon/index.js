import { useState } from 'react';
import { Icon } from 'tntd';
import copy from 'copy-to-clipboard';
import './index.less';

export default (props) => {
	const { value } = props;
	const [hasCopy, setHasCopy] = useState(false);
    const handleCopy = (innerText) => {
        copy(innerText);
        setHasCopy(!hasCopy);
        setTimeout(() => {
            setHasCopy(false);
        }, 1000);
    };
	return (
		<>
			{
				hasCopy
				? (
					<Icon
						{...props}
						type= 'check'
						style={{
							color: '#07C790'
						}}
					/>
				)
				: (
					<span
						{...props}
						className={
							`${props?.className || ''} table-icon-copy`
						}
						onClick={(e)=>{
							e.stopPropagation();
							handleCopy(value);
						}}
					/>
				)
			}
		</>
	);

};

