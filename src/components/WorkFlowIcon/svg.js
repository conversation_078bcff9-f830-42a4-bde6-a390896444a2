import Action from './svg/action.svg';
import ChampionChallenger from './svg/championChallenger.svg';
import ContinueSupplement from './svg/continueSupplement.svg';
import DecisionTool from './svg/decisionTool.svg';
import End from './svg/end.svg';
import Function from './svg/function.svg';
import Judgment from './svg/judgment.svg';
import Model from './svg/model.svg';
import Parallel from './svg/parallel.svg';
import ProcessTemplate from './svg/processTemplate.svg';
import Ruleset from './svg/ruleset.svg';
import ScoreCard from './svg/scoreCard.svg';
import Start from './svg/start.svg';
import Strategy from './svg/strategy.svg';
import SubStrategy from './svg/subStrategy.svg';
import ThirdPartyService from './svg/thirdPartyService.svg';

export const workFlowSvgMap = {
    Action,
    ChampionChallenger,
    ContinueSupplement,
    DecisionTool,
    End,
    Function,
    Judgment,
    Model,
    Parallel,
    ProcessTemplate,
    Ruleset,
    ScoreCard,
    Start,
    Strategy,
    SubStrategy,
    ThirdPartyService
};
