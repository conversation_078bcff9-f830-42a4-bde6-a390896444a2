import I18N from '@/utils/I18N';
import React, { useEffect, useState } from 'react';
import { Select, Input, Tooltip } from 'tntd';
import { clone, cloneDeep } from 'lodash';
import { DATA_TYPE_MAP } from '@/constants';
const Option = Select.Option;
const InputParams = (props) => {
    const { value, onChange, inputParams } = props;
    const [curValue, setCurValue] = useState({});
    useEffect(() => {
        setCurValue(value || {});
    }, [value]);
    const onSelectChange = (v) => {
        let data = cloneDeep(curValue);
        data.inputParamField = v;
        onChange(data);
    };
    const onInputChange = (e) => {
        let data = cloneDeep(curValue);
        data.inputParamFieldValue = e.target.value;
        onChange(data);
    };
    return (
        <>
            <Select
                style={{ marginBottom: '8px' }}
                placeholder={I18N.searchdrawer.index.qingXuanZeRuCan}
                value={curValue?.inputParamField}
                onChange={onSelectChange}>
                {(inputParams || []).map((item) => {
                    // 已经选择的需要禁用
                    return (
                        <Option
                            key={item.field}
                            value={item.field}
                            datatype={item.dataType}
                            displayname={item.displayName}
                            title={`${item.displayName}【${item.field}】`}>
                            <Tooltip>
                                <sup style={{ color: 'blue' }}> {DATA_TYPE_MAP[item.dataType].displayName} </sup>
                                {item.displayName + '【' + item.field + '】'}
                            </Tooltip>
                        </Option>
                    );
                })}
            </Select>
            <Input value={curValue?.inputParamFieldValue} placeholder={I18N.addmodify.flowlimit.shuRuZhi} onChange={onInputChange} />
        </>
    );
};

export default InputParams;
