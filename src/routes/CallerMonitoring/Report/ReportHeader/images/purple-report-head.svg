<?xml version="1.0" encoding="UTF-8"?>
<svg width="463px" height="200px" viewBox="0 0 463 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>紫色-右</title>
    <defs>
        <linearGradient x1="24.9044309%" y1="63.3502786%" x2="2.07919555%" y2="9.41768775%" id="linearGradient-1">
            <stop stop-color="#6392F9" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#5FA9FD" stop-opacity="0.0630463287" offset="23.8696376%"></stop>
            <stop stop-color="#5FA8FD" stop-opacity="0.0594892084" offset="46.1734794%"></stop>
            <stop stop-color="#A8D5FF" stop-opacity="0.350952175" offset="81.0232736%"></stop>
            <stop stop-color="#EBF3FF" offset="100%"></stop>
        </linearGradient>
        <rect id="path-2" x="0" y="0" width="463" height="200"></rect>
        <radialGradient cx="50%" cy="50%" fx="50%" fy="50%" r="94.3556566%" gradientTransform="translate(0.500000,0.500000),scale(0.528926,1.000000),rotate(180.000000),scale(1.000000,0.534987),translate(-0.500000,-0.500000)" id="radialGradient-4">
            <stop stop-color="#560FFA" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#102EFA" stop-opacity="0.0905539773" offset="86.9208916%"></stop>
            <stop stop-color="#2A22FD" stop-opacity="0.0990767045" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="4.37879927%" y1="51.3671973%" x2="81.4604653%" y2="55.223033%" id="linearGradient-5">
            <stop stop-color="#E5E7FF" offset="0%"></stop>
            <stop stop-color="#9391FC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-1.43816547%" y1="43.9628027%" x2="98.7244422%" y2="47.9046814%" id="linearGradient-6">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="45.2965373%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="4.87058477%" y1="48.1519032%" x2="99.261055%" y2="46.8788541%" id="linearGradient-7">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="39.609925%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.99168284" offset="55.1146222%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="4.87058477%" y1="47.8237362%" x2="99.261055%" y2="46.3246315%" id="linearGradient-8">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="39.609925%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.99168284" offset="55.1146222%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="4.87058477%" y1="48.1632511%" x2="99.261055%" y2="46.8980189%" id="linearGradient-9">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="39.609925%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.99168284" offset="55.1146222%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="3.8628494%" y1="52.4035177%" x2="81.82287%" y2="56.4849755%" id="linearGradient-10">
            <stop stop-color="#CBC9FF" offset="0%"></stop>
            <stop stop-color="#7981FA" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="100%" y1="52.6614446%" x2="9.20807301%" y2="46.326947%" id="linearGradient-11">
            <stop stop-color="#937BFF" offset="0%"></stop>
            <stop stop-color="#DBD3FF" offset="22.2191871%"></stop>
            <stop stop-color="#C7C1FF" offset="50.4452579%"></stop>
            <stop stop-color="#E4E0FF" offset="81.4057037%"></stop>
            <stop stop-color="#FAF2FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="17.3743562%" y1="52.5471103%" x2="85.9614578%" y2="53.1223394%" id="linearGradient-12">
            <stop stop-color="#9AA2FA" offset="0%"></stop>
            <stop stop-color="#5F43CC" offset="100%"></stop>
        </linearGradient>
        <radialGradient cx="95.9790473%" cy="43.1897366%" fx="95.9790473%" fy="43.1897366%" r="260.646676%" gradientTransform="translate(0.959790,0.431897),scale(0.308000,1.000000),rotate(-176.714405),translate(-0.959790,-0.431897)" id="radialGradient-13">
            <stop stop-color="#B3B9F2" offset="0%"></stop>
            <stop stop-color="#9F85FA" offset="54.802229%"></stop>
            <stop stop-color="#D7CDFF" offset="100%"></stop>
        </radialGradient>
        <linearGradient x1="99.7264678%" y1="50.0178743%" x2="45.8731585%" y2="53.5369771%" id="linearGradient-14">
            <stop stop-color="#9834C7" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#5E387C" stop-opacity="0.827851836" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="13.5250273%" y1="52.6277228%" x2="19.2514485%" y2="48.8273467%" id="linearGradient-15">
            <stop stop-color="#CDACF9" offset="0%"></stop>
            <stop stop-color="#7C64D4" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="44.8884406%" y1="9.04717924%" x2="66.4478655%" y2="95.0475628%" id="linearGradient-16">
            <stop stop-color="#C3B4FC" offset="0%"></stop>
            <stop stop-color="#D8C1FF" offset="11.9183677%"></stop>
            <stop stop-color="#E9E4FF" offset="39.1889444%"></stop>
            <stop stop-color="#B38AFF" offset="64.4421984%"></stop>
            <stop stop-color="#A676DC" offset="78.6849869%"></stop>
            <stop stop-color="#592AEA" offset="100%"></stop>
        </linearGradient>
        <path d="M3.19744231e-14,1.7408297e-13 L41.7295148,1.7408297e-13 C44.295518,0.448543863 46.1526557,2.25517887 47.300928,5.41990501 C48.4492003,8.58463115 53.2017717,25.1294207 61.5586422,55.0542737 L22.257229,55.7116397 C19.2531545,55.3153522 17.0663085,52.5667017 15.696691,47.465688 C14.3270736,42.3646743 11.5320153,28.7116652 7.31151638,6.50666083 C7.12128188,4.6079966 6.39673831,3.09852573 5.13788565,1.97824824 C3.87903299,0.857970739 2.16640444,0.19855466 3.19744231e-14,1.7408297e-13 Z" id="path-17"></path>
        <filter x="-58.3%" y="-63.8%" width="216.5%" height="227.6%" filterUnits="objectBoundingBox" id="filter-19">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="17.1157187%" x2="50%" y2="98.3270384%" id="linearGradient-20">
            <stop stop-color="#E5D4FF" offset="0%"></stop>
            <stop stop-color="#9970F0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0.967847054%" x2="50%" y2="97.7575688%" id="linearGradient-21">
            <stop stop-color="#F0D2FF" offset="0%"></stop>
            <stop stop-color="#F7EDFF" offset="26.3111888%"></stop>
            <stop stop-color="#F8D4FE" offset="64.2668957%"></stop>
            <stop stop-color="#CBC2FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="95.9195626%" y1="50%" x2="33.9405535%" y2="50%" id="linearGradient-22">
            <stop stop-color="#FAE6FF" offset="0%"></stop>
            <stop stop-color="#FEFDFF" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-23" points="46.8971482 14.5259824 48.0344575 20.0015418 21.6705726 20.8040646 20.5332633 15.3285052"></polygon>
        <filter x="-12.7%" y="-39.8%" width="125.5%" height="211.5%" filterUnits="objectBoundingBox" id="filter-24">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.101999563 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="95.9195626%" y1="50%" x2="33.9405535%" y2="50%" id="linearGradient-25">
            <stop stop-color="#FAE6FF" offset="0%"></stop>
            <stop stop-color="#FEFDFF" offset="100%"></stop>
        </linearGradient>
        <polygon id="path-26" points="48.5359996 22.3909253 49.3879047 26.7805269 23.0240198 27.5830497 22.1721146 23.1934481"></polygon>
        <filter x="-12.9%" y="-48.1%" width="125.7%" height="234.8%" filterUnits="objectBoundingBox" id="filter-27">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.101999563 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <polygon id="path-28" points="49.8087743 28.9363799 50.6606794 33.3259815 24.2967945 34.1285042 23.4448893 29.7389027"></polygon>
        <filter x="-12.9%" y="-48.1%" width="125.7%" height="234.8%" filterUnits="objectBoundingBox" id="filter-29">
            <feOffset dx="0" dy="1" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="1" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.101999563 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
        <linearGradient x1="73.1631002%" y1="45.5597138%" x2="38.6144371%" y2="121.146785%" id="linearGradient-30">
            <stop stop-color="#AFAAFB" offset="0%"></stop>
            <stop stop-color="#7267E3" offset="65.2589598%"></stop>
            <stop stop-color="#4E48C8" offset="81.801792%"></stop>
            <stop stop-color="#5148CB" offset="90.3354458%"></stop>
            <stop stop-color="#474AC2" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="9.76591422%" x2="0.894613199%" y2="88.794328%" id="linearGradient-31">
            <stop stop-color="#DCC5F9" offset="0%"></stop>
            <stop stop-color="#D0C5FC" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="1.92124709%" x2="50%" y2="97.8422544%" id="linearGradient-32">
            <stop stop-color="#FAF3FF" offset="0%"></stop>
            <stop stop-color="#E3E0FF" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="-3.03152827%" y1="17.2890896%" x2="100%" y2="17.2890896%" id="linearGradient-33">
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="0%"></stop>
            <stop stop-color="#FFFFFF" offset="55.6025403%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="37.2179087%" y1="33.0092044%" x2="55.5942546%" y2="79.3259542%" id="linearGradient-34">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#FFFFFF" stop-opacity="0.287368881" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="83.6443297%" y1="84.1662967%" x2="28.1758728%" y2="22.6101803%" id="linearGradient-35">
            <stop stop-color="#022DDB" stop-opacity="0.033517264" offset="0%"></stop>
            <stop stop-color="#C5C3FF" stop-opacity="0.118908435" offset="47.7218094%"></stop>
            <stop stop-color="#DED6FF" offset="100%"></stop>
        </linearGradient>
        <filter x="-18.9%" y="-19.0%" width="137.7%" height="138.0%" filterUnits="objectBoundingBox" id="filter-36">
            <feGaussianBlur stdDeviation="10" in="SourceGraphic"></feGaussianBlur>
        </filter>
    </defs>
    <g id="V1.5.0" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="台式电脑1">
            <path d="M284,150 L284,155 L279,155 L279,150 L284,150 Z M294,150 L294,155 L289,155 L289,150 L294,150 Z M305,150 L305,155 L300,155 L300,150 L305,150 Z M315,150 L315,155 L310,155 L310,150 L315,150 Z M325,150 L325,155 L320,155 L320,150 L325,150 Z M336,150 L336,155 L331,155 L331,150 L336,150 Z M346,150 L346,155 L341,155 L341,150 L346,150 Z M356,150 L356,155 L351,155 L351,150 L356,150 Z M367,150 L367,155 L362,155 L362,150 L367,150 Z M377,150 L377,155 L372,155 L372,150 L377,150 Z M388,150 L388,155 L383,155 L383,150 L388,150 Z M398,150 L398,155 L393,155 L393,150 L398,150 Z M408,150 L408,155 L403,155 L403,150 L408,150 Z M419,150 L419,155 L414,155 L414,150 L419,150 Z M429,150 L429,155 L424,155 L424,150 L429,150 Z M439,150 L439,155 L434,155 L434,150 L439,150 Z M450,150 L450,155 L445,155 L445,150 L450,150 Z M460,150 L460,155 L455,155 L455,150 L460,150 Z M377,140 L377,145 L372,145 L372,140 L377,140 Z M305,140 L305,145 L300,145 L300,140 L305,140 Z M388,140 L388,145 L383,145 L383,140 L388,140 Z M336,140 L336,145 L331,145 L331,140 L336,140 Z M398,140 L398,145 L393,145 L393,140 L398,140 Z M294,140 L294,145 L289,145 L289,140 L294,140 Z M408,140 L408,145 L403,145 L403,140 L408,140 Z M346,140 L346,145 L341,145 L341,140 L346,140 Z M419,140 L419,145 L414,145 L414,140 L419,140 Z M315,140 L315,145 L310,145 L310,140 L315,140 Z M429,140 L429,145 L424,145 L424,140 L429,140 Z M356,140 L356,145 L351,145 L351,140 L356,140 Z M439,140 L439,145 L434,145 L434,140 L439,140 Z M284,140 L284,145 L279,145 L279,140 L284,140 Z M450,140 L450,145 L445,145 L445,140 L450,140 Z M367,140 L367,145 L362,145 L362,140 L367,140 Z M460,140 L460,145 L455,145 L455,140 L460,140 Z M325,140 L325,145 L320,145 L320,140 L325,140 Z M325,129 L325,134 L320,134 L320,129 L325,129 Z M336,129 L336,134 L331,134 L331,129 L336,129 Z M408,129 L408,134 L403,134 L403,129 L408,129 Z M367,129 L367,134 L362,134 L362,129 L367,129 Z M294,129 L294,134 L289,134 L289,129 L294,129 Z M419,129 L419,134 L414,134 L414,129 L419,129 Z M429,129 L429,134 L424,134 L424,129 L429,129 Z M315,129 L315,134 L310,134 L310,129 L315,129 Z M377,129 L377,134 L372,134 L372,129 L377,129 Z M439,129 L439,134 L434,134 L434,129 L439,129 Z M346,129 L346,134 L341,134 L341,129 L346,129 Z M305,129 L305,134 L300,134 L300,129 L305,129 Z M450,129 L450,134 L445,134 L445,129 L450,129 Z M388,129 L388,134 L383,134 L383,129 L388,129 Z M284,129 L284,134 L279,134 L279,129 L284,129 Z M460,129 L460,134 L455,134 L455,129 L460,129 Z M356,129 L356,134 L351,134 L351,129 L356,129 Z M398,129 L398,134 L393,134 L393,129 L398,129 Z M398,119 L398,124 L393,124 L393,119 L398,119 Z M377,119 L377,124 L372,124 L372,119 L377,119 Z M419,119 L419,124 L414,124 L414,119 L419,119 Z M325,119 L325,124 L320,124 L320,119 L325,119 Z M429,119 L429,124 L424,124 L424,119 L429,119 Z M356,119 L356,124 L351,124 L351,119 L356,119 Z M439,119 L439,124 L434,124 L434,119 L439,119 Z M315,119 L315,124 L310,124 L310,119 L315,119 Z M294,119 L294,124 L289,124 L289,119 L294,119 Z M367,119 L367,124 L362,124 L362,119 L367,119 Z M450,119 L450,124 L445,124 L445,119 L450,119 Z M388,119 L388,124 L383,124 L383,119 L388,119 Z M284,119 L284,124 L279,124 L279,119 L284,119 Z M305,119 L305,124 L300,124 L300,119 L305,119 Z M460,119 L460,124 L455,124 L455,119 L460,119 Z M336,119 L336,124 L331,124 L331,119 L336,119 Z M346,119 L346,124 L341,124 L341,119 L346,119 Z M408,119 L408,124 L403,124 L403,119 L408,119 Z M356,109 L356,114 L351,114 L351,109 L356,109 Z M315,109 L315,114 L310,114 L310,109 L315,109 Z M346,109 L346,114 L341,114 L341,109 L346,109 Z M419,109 L419,114 L414,114 L414,109 L419,109 Z M429,109 L429,114 L424,114 L424,109 L429,109 Z M450,109 L450,114 L445,114 L445,109 L450,109 Z M388,109 L388,114 L383,114 L383,109 L388,109 Z M377,109 L377,114 L372,114 L372,109 L377,109 Z M439,109 L439,114 L434,114 L434,109 L439,109 Z M408,109 L408,114 L403,114 L403,109 L408,109 Z M336,109 L336,114 L331,114 L331,109 L336,109 Z M325,109 L325,114 L320,114 L320,109 L325,109 Z M460,109 L460,114 L455,114 L455,109 L460,109 Z M398,109 L398,114 L393,114 L393,109 L398,109 Z M367,109 L367,114 L362,114 L362,109 L367,109 Z M305,109 L305,114 L300,114 L300,109 L305,109 Z M294,109 L294,114 L289,114 L289,109 L294,109 Z M284,109 L284,114 L279,114 L279,109 L284,109 Z M294,98 L294,103 L289,103 L289,98 L294,98 Z M460,98 L460,103 L455,103 L455,98 L460,98 Z M377,98 L377,103 L372,103 L372,98 L377,98 Z M356,98 L356,103 L351,103 L351,98 L356,98 Z M388,98 L388,103 L383,103 L383,98 L388,98 Z M305,98 L305,103 L300,103 L300,98 L305,98 Z M346,98 L346,103 L341,103 L341,98 L346,98 Z M398,98 L398,103 L393,103 L393,98 L398,98 Z M419,98 L419,103 L414,103 L414,98 L419,98 Z M450,98 L450,103 L445,103 L445,98 L450,98 Z M439,98 L439,103 L434,103 L434,98 L439,98 Z M325,98 L325,103 L320,103 L320,98 L325,98 Z M408,98 L408,103 L403,103 L403,98 L408,98 Z M284,98 L284,103 L279,103 L279,98 L284,98 Z M429,98 L429,103 L424,103 L424,98 L429,98 Z M367,98 L367,103 L362,103 L362,98 L367,98 Z M315,98 L315,103 L310,103 L310,98 L315,98 Z M336,98 L336,103 L331,103 L331,98 L336,98 Z M294,88 L294,93 L289,93 L289,88 L294,88 Z M346,88 L346,93 L341,93 L341,88 L346,88 Z M408,88 L408,93 L403,93 L403,88 L408,88 Z M367,88 L367,93 L362,93 L362,88 L367,88 Z M429,88 L429,93 L424,93 L424,88 L429,88 Z M284,88 L284,93 L279,93 L279,88 L284,88 Z M336,88 L336,93 L331,93 L331,88 L336,88 Z M419,88 L419,93 L414,93 L414,88 L419,88 Z M315,88 L315,93 L310,93 L310,88 L315,88 Z M398,88 L398,93 L393,93 L393,88 L398,88 Z M388,88 L388,93 L383,93 L383,88 L388,88 Z M450,88 L450,93 L445,93 L445,88 L450,88 Z M460,88 L460,93 L455,93 L455,88 L460,88 Z M377,88 L377,93 L372,93 L372,88 L377,88 Z M439,88 L439,93 L434,93 L434,88 L439,88 Z M325,88 L325,93 L320,93 L320,88 L325,88 Z M356,88 L356,93 L351,93 L351,88 L356,88 Z M305,88 L305,93 L300,93 L300,88 L305,88 Z M450,77 L450,82 L445,82 L445,77 L450,77 Z M356,77 L356,82 L351,82 L351,77 L356,77 Z M367,77 L367,82 L362,82 L362,77 L367,77 Z M388,77 L388,82 L383,82 L383,77 L388,77 Z M305,77 L305,82 L300,82 L300,77 L305,77 Z M325,77 L325,82 L320,82 L320,77 L325,77 Z M346,77 L346,82 L341,82 L341,77 L346,77 Z M408,77 L408,82 L403,82 L403,77 L408,77 Z M429,77 L429,82 L424,82 L424,77 L429,77 Z M439,77 L439,82 L434,82 L434,77 L439,77 Z M315,77 L315,82 L310,82 L310,77 L315,77 Z M377,77 L377,82 L372,82 L372,77 L377,77 Z M336,77 L336,82 L331,82 L331,77 L336,77 Z M294,77 L294,82 L289,82 L289,77 L294,77 Z M398,77 L398,82 L393,82 L393,77 L398,77 Z M284,77 L284,82 L279,82 L279,77 L284,77 Z M419,77 L419,82 L414,82 L414,77 L419,77 Z M460,77 L460,82 L455,82 L455,77 L460,77 Z M346,67 L346,72 L341,72 L341,67 L346,67 Z M336,67 L336,72 L331,72 L331,67 L336,67 Z M284,67 L284,72 L279,72 L279,67 L284,67 Z M398,67 L398,72 L393,72 L393,67 L398,67 Z M388,67 L388,72 L383,72 L383,67 L388,67 Z M294,67 L294,72 L289,72 L289,67 L294,67 Z M367,67 L367,72 L362,72 L362,67 L367,67 Z M439,67 L439,72 L434,72 L434,67 L439,67 Z M315,67 L315,72 L310,72 L310,67 L315,67 Z M429,67 L429,72 L424,72 L424,67 L429,67 Z M305,67 L305,72 L300,72 L300,67 L305,67 Z M460,67 L460,72 L455,72 L455,67 L460,67 Z M356,67 L356,72 L351,72 L351,67 L356,67 Z M419,67 L419,72 L414,72 L414,67 L419,67 Z M408,67 L408,72 L403,72 L403,67 L408,67 Z M325,67 L325,72 L320,72 L320,67 L325,67 Z M450,67 L450,72 L445,72 L445,67 L450,67 Z M377,67 L377,72 L372,72 L372,67 L377,67 Z M367,57 L367,62 L362,62 L362,57 L367,57 Z M388,57 L388,62 L383,62 L383,57 L388,57 Z M377,57 L377,62 L372,62 L372,57 L377,57 Z M356,57 L356,62 L351,62 L351,57 L356,57 Z M325,57 L325,62 L320,62 L320,57 L325,57 Z M429,57 L429,62 L424,62 L424,57 L429,57 Z M439,57 L439,62 L434,62 L434,57 L439,57 Z M450,57 L450,62 L445,62 L445,57 L450,57 Z M419,57 L419,62 L414,62 L414,57 L419,57 Z M284,57 L284,62 L279,62 L279,57 L284,57 Z M408,57 L408,62 L403,62 L403,57 L408,57 Z M305,57 L305,62 L300,62 L300,57 L305,57 Z M398,57 L398,62 L393,62 L393,57 L398,57 Z M315,57 L315,62 L310,62 L310,57 L315,57 Z M346,57 L346,62 L341,62 L341,57 L346,57 Z M294,57 L294,62 L289,62 L289,57 L294,57 Z M336,57 L336,62 L331,62 L331,57 L336,57 Z M460,57 L460,62 L455,62 L455,57 L460,57 Z M284,46 L284,51 L279,51 L279,46 L284,46 Z M305,46 L305,51 L300,51 L300,46 L305,46 Z M356,46 L356,51 L351,51 L351,46 L356,46 Z M315,46 L315,51 L310,51 L310,46 L315,46 Z M325,46 L325,51 L320,51 L320,46 L325,46 Z M377,46 L377,51 L372,51 L372,46 L377,46 Z M367,46 L367,51 L362,51 L362,46 L367,46 Z M294,46 L294,51 L289,51 L289,46 L294,46 Z M408,46 L408,51 L403,51 L403,46 L408,46 Z M398,46 L398,51 L393,51 L393,46 L398,46 Z M450,46 L450,51 L445,51 L445,46 L450,46 Z M336,46 L336,51 L331,51 L331,46 L336,46 Z M346,46 L346,51 L341,51 L341,46 L346,46 Z M419,46 L419,51 L414,51 L414,46 L419,46 Z M429,46 L429,51 L424,51 L424,46 L429,46 Z M460,46 L460,51 L455,51 L455,46 L460,46 Z M388,46 L388,51 L383,51 L383,46 L388,46 Z M439,46 L439,51 L434,51 L434,46 L439,46 Z M388,36 L388,41 L383,41 L383,36 L388,36 Z M356,36 L356,41 L351,41 L351,36 L356,36 Z M377,36 L377,41 L372,41 L372,36 L377,36 Z M305,36 L305,41 L300,41 L300,36 L305,36 Z M419,36 L419,41 L414,41 L414,36 L419,36 Z M408,36 L408,41 L403,41 L403,36 L408,36 Z M460,36 L460,41 L455,41 L455,36 L460,36 Z M284,36 L284,41 L279,41 L279,36 L284,36 Z M367,36 L367,41 L362,41 L362,36 L367,36 Z M325,36 L325,41 L320,41 L320,36 L325,36 Z M336,36 L336,41 L331,41 L331,36 L336,36 Z M429,36 L429,41 L424,41 L424,36 L429,36 Z M439,36 L439,41 L434,41 L434,36 L439,36 Z M294,36 L294,41 L289,41 L289,36 L294,36 Z M398,36 L398,41 L393,41 L393,36 L398,36 Z M450,36 L450,41 L445,41 L445,36 L450,36 Z M315,36 L315,41 L310,41 L310,36 L315,36 Z M346,36 L346,41 L341,41 L341,36 L346,36 Z M336,26 L336,31 L331,31 L331,26 L336,26 Z M346,26 L346,31 L341,31 L341,26 L346,26 Z M367,26 L367,31 L362,31 L362,26 L367,26 Z M377,26 L377,31 L372,31 L372,26 L377,26 Z M294,26 L294,31 L289,31 L289,26 L294,26 Z M408,26 L408,31 L403,31 L403,26 L408,26 Z M450,26 L450,31 L445,31 L445,26 L450,26 Z M398,26 L398,31 L393,31 L393,26 L398,26 Z M284,26 L284,31 L279,31 L279,26 L284,26 Z M356,26 L356,31 L351,31 L351,26 L356,26 Z M325,26 L325,31 L320,31 L320,26 L325,26 Z M315,26 L315,31 L310,31 L310,26 L315,26 Z M419,26 L419,31 L414,31 L414,26 L419,26 Z M429,26 L429,31 L424,31 L424,26 L429,26 Z M388,26 L388,31 L383,31 L383,26 L388,26 Z M460,26 L460,31 L455,31 L455,26 L460,26 Z M439,26 L439,31 L434,31 L434,26 L439,26 Z M305,26 L305,31 L300,31 L300,26 L305,26 Z M336,15 L336,20 L331,20 L331,15 L336,15 Z M450,15 L450,20 L445,20 L445,15 L450,15 Z M346,15 L346,20 L341,20 L341,15 L346,15 Z M398,15 L398,20 L393,20 L393,15 L398,15 Z M388,15 L388,20 L383,20 L383,15 L388,15 Z M315,15 L315,20 L310,20 L310,15 L315,15 Z M429,15 L429,20 L424,20 L424,15 L429,15 Z M419,15 L419,20 L414,20 L414,15 L419,15 Z M284,15 L284,20 L279,20 L279,15 L284,15 Z M305,15 L305,20 L300,20 L300,15 L305,15 Z M377,15 L377,20 L372,20 L372,15 L377,15 Z M294,15 L294,20 L289,20 L289,15 L294,15 Z M408,15 L408,20 L403,20 L403,15 L408,15 Z M460,15 L460,20 L455,20 L455,15 L460,15 Z M325,15 L325,20 L320,20 L320,15 L325,15 Z M356,15 L356,20 L351,20 L351,15 L356,15 Z M367,15 L367,20 L362,20 L362,15 L367,15 Z M439,15 L439,20 L434,20 L434,15 L439,15 Z M336,5 L336,10 L331,10 L331,5 L336,5 Z M325,5 L325,10 L320,10 L320,5 L325,5 Z M346,5 L346,10 L341,10 L341,5 L346,5 Z M356,5 L356,10 L351,10 L351,5 L356,5 Z M367,5 L367,10 L362,10 L362,5 L367,5 Z M377,5 L377,10 L372,10 L372,5 L377,5 Z M388,5 L388,10 L383,10 L383,5 L388,5 Z M398,5 L398,10 L393,10 L393,5 L398,5 Z M408,5 L408,10 L403,10 L403,5 L408,5 Z M419,5 L419,10 L414,10 L414,5 L419,5 Z M429,5 L429,10 L424,10 L424,5 L429,5 Z M439,5 L439,10 L434,10 L434,5 L439,5 Z M294,5 L294,10 L289,10 L289,5 L294,5 Z M450,5 L450,10 L445,10 L445,5 L450,5 Z M305,5 L305,10 L300,10 L300,5 L305,5 Z M460,5 L460,10 L455,10 L455,5 L460,5 Z M315,5 L315,10 L310,10 L310,5 L315,5 Z M284,5 L284,10 L279,10 L279,5 L284,5 Z" id="形状结合备份-3" fill="url(#linearGradient-1)" opacity="0.389194222" transform="translate(369.500000, 80.000000) scale(-1, 1) translate(-369.500000, -80.000000) "></path>
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-2"></use>
            </mask>
            <g id="蒙版"></g>
            <g id="编组-48备份" mask="url(#mask-3)">
                <g transform="translate(228.000000, 80.500000)">
                    <g id="编组-18" transform="translate(0.000000, 23.000000)">
                        <g id="编组-10" transform="translate(0.241379, 0.000000)">
                            <ellipse id="椭圆形" fill="url(#radialGradient-4)" cx="183" cy="75" rx="121" ry="64"></ellipse>
                            <ellipse id="椭圆形" fill="#CCC4FF" cx="182" cy="70" rx="89" ry="42"></ellipse>
                            <ellipse id="椭圆形" fill="url(#linearGradient-5)" cx="183.101933" cy="66.8995225" rx="68.088291" ry="28.1356574"></ellipse>
                            <path d="M81.2508526,37.6384388 C81.2508526,73.6886741 126.095329,102.913164 181.413793,102.913164 C236.732258,102.913164 281.576734,73.6886741 281.576734,37.6384388" id="路径" stroke="url(#linearGradient-6)" opacity="0.208893532"></path>
                            <path d="M81.2508526,37.6384388 C81.2508526,73.6886741 126.095329,102.913164 181.413793,102.913164 C236.732258,102.913164 281.576734,73.6886741 281.576734,37.6384388" id="路径" stroke="url(#linearGradient-7)" opacity="0.185501453" transform="translate(181.413793, 70.275801) rotate(3.000000) translate(-181.413793, -70.275801) "></path>
                            <path d="M0.413793103,10.7758014 C0.413793103,81.4682494 81.4502534,138.775801 181.413793,138.775801 C281.377333,138.775801 362.413793,81.4682494 362.413793,10.7758014" id="路径" stroke="url(#linearGradient-8)" opacity="0.185501453"></path>
                            <path d="M24.4137931,40.2758014 C24.4137931,96.6088459 94.7050874,142.275801 181.413793,142.275801 C268.122499,142.275801 338.413793,96.6088459 338.413793,40.2758014" id="路径备份" stroke="url(#linearGradient-9)" opacity="0.185501453" transform="translate(181.413793, 91.275801) rotate(3.000000) translate(-181.413793, -91.275801) "></path>
                            <g id="编组-5" transform="translate(122.891626, 29.667351)">
                                <path d="M59.6475938,0.0931034483 C90.8422917,0.0931034483 116.443954,10.4850377 119.072734,23.7267082 L119.295188,23.7270557 L119.295188,27.1033346 L119.239762,27.103951 C117.881265,40.8773229 91.720649,51.8627131 59.6475938,51.8627131 C27.5745385,51.8627131 1.41392214,40.8773229 0.0554250881,27.103951 L0,27.1033346 L0,23.7270557 L0.22245393,23.7267082 C2.85123333,10.4850377 28.4528959,0.0931034483 59.6475938,0.0931034483 Z" id="形状结合" fill="url(#linearGradient-10)"></path>
                                <path d="M59.5463054,0.0874763168 C92.3520196,0.0874763168 118.946305,10.4042251 118.946305,23.1305798 C118.946305,35.8569344 92.3520196,46.1736832 59.5463054,46.1736832 C26.7405913,46.1736832 0.146305419,35.8569344 0.146305419,23.1305798 C0.146305419,10.4042251 26.7405913,0.0874763168 59.5463054,0.0874763168 Z M61.5945813,7.25644183 C38.9699508,7.25644183 20.629064,13.9050133 20.629064,22.1064418 C20.629064,30.3078704 38.9699508,36.9564418 61.5945813,36.9564418 C84.2192117,36.9564418 102.560099,30.3078704 102.560099,22.1064418 C102.560099,13.9050133 84.2192117,7.25644183 61.5945813,7.25644183 Z" id="形状结合" fill="url(#linearGradient-11)"></path>
                            </g>
                            <path d="M147.651004,48.8927018 L148.254495,48.8919781 C151.382672,43.1298879 166.298582,38.7638651 184.227359,38.7638651 C202.156135,38.7638651 217.072046,43.1298879 220.200222,48.8919781 L220.803714,48.8927018 L220.803714,51.1435544 C220.803714,57.980668 204.427922,63.5232437 184.227359,63.5232437 C164.026796,63.5232437 147.651004,57.980668 147.651004,51.1435544 L147.651004,51.1435544 L147.651004,48.8927018 Z" id="形状结合" fill="url(#linearGradient-12)"></path>
                            <ellipse id="椭圆形" fill="url(#radialGradient-13)" cx="184.227359" cy="50.0293823" rx="36.5763547" ry="11.2655172"></ellipse>
                            <ellipse id="椭圆形" fill="url(#linearGradient-14)" cx="200" cy="50" rx="18" ry="7"></ellipse>
                            <g id="编组-49" transform="translate(189.252598, 29.426633) scale(-1, 1) translate(-189.252598, -29.426633) translate(153.252598, 0.426633)">
                                <g id="编组-47">
                                    <path d="M1.95426206,12.5415727 L48.8749249,11.6237578 C49.0645918,5.8013207 47.5707792,2.0514518 44.3934871,0.374151095 L3.65858404,0.47018527 C1.93658131,0.97506528 0.906741667,2.44951888 0.569065106,4.89354607 C0.231388545,7.33757326 0.693120863,9.88691547 1.95426206,12.5415727 Z" id="路径-36" fill="url(#linearGradient-15)"></path>
                                    <g id="路径-42" transform="translate(3.548740, 0.374151)">
                                        <mask id="mask-18" fill="white">
                                            <use xlink:href="#path-17"></use>
                                        </mask>
                                        <use id="蒙版" fill="url(#linearGradient-16)" xlink:href="#path-17"></use>
                                        <path d="M21.8968049,-6.30078436 C22.2624267,-5.70060345 33.1879432,-0.70060345 54.6733543,8.69921564 L58.9452376,11.4299135 L53.4452376,40.6992156 L7.4452376,21.9596704 C16.7139939,2.5191863 21.531183,-6.90096528 21.8968049,-6.30078436 Z" fill="#FFFFFF" opacity="0.206145531" filter="url(#filter-19)" mask="url(#mask-18)" transform="translate(33.195238, 17.185735) rotate(-25.000000) translate(-33.195238, -17.185735) "></path>
                                    </g>
                                    <path d="M30.5878538,45.6418636 C31.4224055,50.3064178 30.3972516,53.3275399 27.5123923,54.7052299 C25.9430413,55.4546873 24.0733017,55.4604199 22.5804226,54.0400182 C21.7461521,53.2462504 20.6344881,51.1795241 19.2454307,47.8398391 C20.4311992,52.6958144 22.6180452,55.444465 25.8059687,56.0857908 C28.9938921,56.7271165 42.0943632,56.5079945 65.1073819,55.4284248 C68.5872801,55.0590797 70.5666203,53.5972978 71.0454026,51.0430792 C71.5241849,48.4888607 71.5241849,46.6884555 71.0454026,45.6418636 L30.5878538,45.6418636 Z" id="路径-35" fill="url(#linearGradient-20)"></path>
                                    <path d="M4.06747522,0.181754529 C6.85357983,-0.590218325 9.00460486,1.10115888 10.5567899,5.17511041 L10.7051332,5.57679887 C10.7783713,5.78135929 10.8502101,5.99149195 10.9206536,6.20718771 L11.0596817,6.64599398 L11.1949964,7.09961891 C11.2617267,7.33013332 11.3270672,7.56619863 11.391022,7.80780568 L11.5170854,8.29840607 C11.5377885,8.38140339 11.5583378,8.46501579 11.5787337,8.54924292 L11.6992682,9.06197978 C11.7190507,9.14866448 11.73868,9.23596324 11.7581562,9.32387571 L11.8731782,9.85871244 C11.8920428,9.94907842 11.9107547,10.0400574 11.929314,10.1316492 L12.0388399,10.6885491 C12.074739,10.8766315 12.110029,11.0671619 12.144711,11.2601376 L12.2469343,11.8463984 L12.3455174,12.4473151 C12.361645,12.5486883 12.3776212,12.6506715 12.3934461,12.7532643 L12.4865824,13.3761345 L12.5760966,14.0136199 L12.661996,14.6657044 L12.7442879,15.3323717 L19.3636741,47.2142796 C20.798731,52.1106979 22.6120856,54.642719 24.7240182,54.8896023 C28.6559856,55.3492462 31.3313206,52.01674 30.2249559,45.9260018 L30.1862269,45.7196528 L30.9894808,45.5640744 C32.2939761,52.2992074 29.1903123,56.2354618 24.6290203,55.7022503 C22.3028378,55.4303214 20.4384817,53.0793033 18.968722,48.6855224 L18.8070358,48.1889199 C18.7803577,48.1047524 18.7538139,48.0198848 18.7274042,47.9343173 L18.5705512,47.4125164 L11.9375448,15.4641178 L11.858459,14.8231109 L11.7762339,14.1966695 C11.7483031,13.9902817 11.7198504,13.786319 11.690877,13.584779 L11.602396,12.9874248 L11.5107983,12.4045923 C11.4952727,12.3086629 11.4796176,12.213338 11.4638331,12.1186172 L11.3675746,11.5575398 C11.3512732,11.4652344 11.3348427,11.3735326 11.3182833,11.282434 L11.2173812,10.7430789 L11.1133927,10.2181872 C11.0782163,10.0456322 11.0425268,9.87548525 11.0063254,9.70774403 L10.8961869,9.21173495 C10.8589631,9.04880264 10.8212287,8.88827358 10.7829849,8.73014532 L10.6667267,8.26296052 L10.54742,7.81016595 C10.4870059,7.58736379 10.4254513,7.36995249 10.3627607,7.15792383 L10.2358659,6.74104093 C10.1074589,6.33133002 9.9745189,5.94312777 9.83707994,5.57636844 L9.69814261,5.21675432 C8.31720381,1.75078608 6.50234132,0.356110997 4.28594548,0.970229152 C1.30450859,1.7963246 0.226047174,4.23887057 1.10951913,8.41748262 L1.19388223,8.79633072 L1.28874402,9.18447962 C1.33880116,9.38088245 1.39279884,9.58078024 1.45074235,9.78418372 L1.57189205,10.1956681 L1.70357338,10.6165202 L1.84579574,11.0467589 L1.99856855,11.4864034 C2.07759413,11.7085803 2.16057966,11.9342915 2.24753043,12.1635478 L2.33580314,12.3939862 L1.57272098,12.6891592 C1.51230609,12.5329744 1.45362389,12.37816 1.39667574,12.2247186 L1.23103483,11.7685165 L1.07580647,11.3205694 C1.02580002,11.1726315 0.97753036,11.0260721 0.930998865,10.8808942 L0.796620229,10.4495074 C0.775093326,10.3783012 0.754001306,10.307441 0.733344339,10.2369273 L0.614624548,9.81800434 C0.57679241,9.67975077 0.540701853,9.54288555 0.506354248,9.40741145 L0.408541649,9.00516527 C-0.71786066,4.1620136 0.479605336,1.17588022 4.06747522,0.181754529 Z" id="路径-39" fill="url(#linearGradient-21)" fill-rule="nonzero"></path>
                                    <g id="路径-41" fill-rule="nonzero">
                                        <use fill="black" fill-opacity="1" filter="url(#filter-24)" xlink:href="#path-23"></use>
                                        <use fill="url(#linearGradient-22)" xlink:href="#path-23"></use>
                                    </g>
                                    <g id="路径-41备份" fill-rule="nonzero">
                                        <use fill="black" fill-opacity="1" filter="url(#filter-27)" xlink:href="#path-26"></use>
                                        <use fill="url(#linearGradient-25)" xlink:href="#path-26"></use>
                                    </g>
                                    <g id="路径-41备份-2" fill-rule="nonzero">
                                        <use fill="black" fill-opacity="1" filter="url(#filter-29)" xlink:href="#path-28"></use>
                                        <use fill="url(#linearGradient-25)" xlink:href="#path-28"></use>
                                    </g>
                                </g>
                                <g id="编组-48" transform="translate(23.805969, 7.408352)">
                                    <path d="M2.87436206,48.8139023 C15.3022444,49.0254703 25.2891701,39.3510199 32.8351391,19.7905511 L18.5398505,21.9754051 L42.9820242,1.3589388 L47.5985206,28.0852437 L41.3014133,22.8803576 C31.7268425,40.3174143 23.4092559,49.0359427 16.3486533,49.0359427 C12.0635789,49.0359427 8.69562296,49.090465 6.30313793,49.0359427 C5.27003113,49.0123993 4.12710584,48.9383858 2.87436206,48.8139023 Z" id="路径-37" fill="url(#linearGradient-30)"></path>
                                    <path d="M31.7300926,19.6026446 C32.4231459,19.7334471 32.7970955,19.8088927 32.8519415,19.8289813 C32.9109131,19.8505811 33.0065433,19.9131136 33.1388319,20.016579 C29.1104866,31.014775 24.296208,38.4260373 18.5063509,43.1340653 C14.2079237,46.6293355 9.38186626,48.8204613 3.88730255,48.9024025 C2.9429099,48.8094825 2.2689395,48.7344946 1.86539134,48.6774386 C1.46184318,48.6203827 0.998759645,48.4744473 0.476140727,48.2396326 C13.1636912,48.2396326 24.5119856,38.9572417 31.518639,20.1746976 L31.7300926,19.6026446 Z" id="路径-43" fill="url(#linearGradient-31)" fill-rule="nonzero"></path>
                                    <polygon id="路径-44" fill="url(#linearGradient-32)" fill-rule="nonzero" points="41.9370759 0 42.986666 1.4169419 18.5398505 21.9754051 17.4902604 20.5584632"></polygon>
                                </g>
                            </g>
                            <path d="M181.976506,86.3618149 L181.976506,92.2167639 L180.85108,92.2167639 L180.85108,86.3618149 L181.976506,86.3618149 Z M177.474801,86.3618149 L177.474801,91.8353807 L176.349375,91.8353807 L176.349375,86.3618149 L177.474801,86.3618149 Z M186.478211,86.3618149 L186.478211,91.8353807 L185.352785,91.8353807 L185.352785,86.3618149 L186.478211,86.3618149 Z M172.973096,85.8895996 L172.973096,91.4940157 L171.84767,91.4940157 L171.84767,85.8895996 L172.973096,85.8895996 Z M190.979917,85.8895996 L190.979917,91.4940157 L189.85449,91.4940157 L189.85449,85.8895996 L190.979917,85.8895996 Z M163.969685,86.0975031 L163.969685,91.0444089 L162.844259,91.0444089 L162.844259,86.0975031 L163.969685,86.0975031 Z M199.983327,86.0975031 L199.983327,91.0444089 L198.857901,91.0444089 L198.857901,86.0975031 L199.983327,86.0975031 Z M168.471391,85.4140556 L168.471391,90.8053296 L167.345964,90.8053296 L167.345964,85.4140556 L168.471391,85.4140556 Z M195.481622,85.4140556 L195.481622,90.8053296 L194.356196,90.8053296 L194.356196,85.4140556 L195.481622,85.4140556 Z M159.46798,85.2298212 L159.46798,90.2526944 L158.342554,90.2526944 L158.342554,85.2298212 L159.46798,85.2298212 Z M204.485032,85.2298212 L204.485032,90.2526944 L203.359606,90.2526944 L203.359606,85.2298212 L204.485032,85.2298212 Z M154.966275,84.060464 L154.966275,89.0184999 L153.840849,89.0184999 L153.840849,84.060464 L154.966275,84.060464 Z M208.986737,84.060464 L208.986737,89.0184999 L207.861311,89.0184999 L207.861311,84.060464 L208.986737,84.060464 Z M150.46457,83.3564598 L150.46457,87.9831951 L149.339144,87.9831951 L149.339144,83.3564598 L150.46457,83.3564598 Z M213.488443,83.3564598 L213.488443,87.9831951 L212.363016,87.9831951 L212.363016,83.3564598 L213.488443,83.3564598 Z M145.962865,81.7656804 L145.962865,86.3552475 L144.837438,86.3552475 L144.837438,81.7656804 L145.962865,81.7656804 Z M217.990148,81.7656804 L217.990148,86.3552475 L216.864721,86.3552475 L216.864721,81.7656804 L217.990148,81.7656804 Z M141.46116,80.3566593 L141.46116,85.2298212 L140.335733,85.2298212 L140.335733,80.3566593 L141.46116,80.3566593 Z M222.491853,80.3566593 L222.491853,85.2298212 L221.366427,85.2298212 L221.366427,80.3566593 L222.491853,80.3566593 Z M136.959454,78.8547546 L136.959454,83.3564598 L135.834028,83.3564598 L135.834028,78.8547546 L136.959454,78.8547546 Z M226.993558,78.8547546 L226.993558,83.3564598 L225.868132,83.3564598 L225.868132,78.8547546 L226.993558,78.8547546 Z M132.457749,76.2577379 L132.457749,80.7281161 L131.332323,80.7281161 L131.332323,76.2577379 L132.457749,76.2577379 Z M231.495263,76.2577379 L231.495263,80.7281161 L230.369837,80.7281161 L230.369837,76.2577379 L231.495263,76.2577379 Z M127.956044,73.5265937 L127.956044,77.7293283 L126.830618,77.7293283 L126.830618,73.5265937 L127.956044,73.5265937 Z M235.996969,73.5265937 L235.996969,77.7293283 L234.871542,77.7293283 L234.871542,73.5265937 L235.996969,73.5265937 Z M123.454339,68.9760029 L123.454339,73.4072364 L122.328912,73.4072364 L122.328912,68.9760029 L123.454339,68.9760029 Z M240.498674,68.9760029 L240.498674,73.4072364 L239.373247,73.4072364 L239.373247,68.9760029 L240.498674,68.9760029 Z M120.07806,63.5232437 L120.07806,67.3101979 L118.952634,67.3101979 L118.952634,63.5232437 L120.07806,63.5232437 Z M243.874953,63.5232437 L243.874953,67.3101979 L242.749526,67.3101979 L242.749526,63.5232437 L243.874953,63.5232437 Z" id="形状结合" fill="url(#linearGradient-33)" fill-rule="nonzero"></path>
                        </g>
                    </g>
                    <g id="编组-15备份-3" opacity="0.70491915" transform="translate(78.069849, 35.817165) scale(-1, 1) translate(-78.069849, -35.817165) translate(67.569849, 12.317165)">
                        <path d="M20.6722656,0.13958368 C14.2827821,8.53767851 13.3009987,19.6994819 17.7269154,33.6249939 L1.83269905,46.1395837 C-0.359663608,31.7904951 0.554434043,21.053358 4.57499201,13.9281725 C6.02303642,11.3619653 8.24136344,9.92727234 10.456513,8.18313552 C13.0668238,6.12786155 16.4720747,3.4466776 20.6722656,0.13958368 Z M11.7746826,25.182835 L5.77468259,29.1846158 L5.77468259,31.182835 L11.7746826,27.1810541 L11.7746826,25.182835 Z M11.7746826,21.182835 L5.77468259,25.1846158 L5.77468259,27.182835 L11.7746826,23.1810541 L11.7746826,21.182835 Z M11.7746826,16.182835 L5.77468259,20.1846158 L5.77468259,22.182835 L11.7746826,18.1810541 L11.7746826,16.182835 Z" id="形状结合" fill="url(#linearGradient-34)" transform="translate(10.672266, 23.139584) scale(-1, 1) translate(-10.672266, -23.139584) "></path>
                    </g>
                </g>
            </g>
            <ellipse id="椭圆形备份-52" fill="url(#linearGradient-35)" opacity="0.571652435" filter="url(#filter-36)" mask="url(#mask-3)" cx="101.5" cy="16.5" rx="79.5" ry="79"></ellipse>
        </g>
    </g>
</svg>