@import url(../variable.less);
.report-warn{
    &.ant-alert-warning{
        background: rgba(255,152,69,0.10);
        border: 1px solid rgba(255,152,69,0.4);
        border-radius: 2px;
    }
}
.report-mb10{
    margin-bottom: 10px;
}
.report-head-info-wrap{
    position: relative;
    box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.1);
    border-radius: @border-radius;
    overflow: hidden;
    &.blue{
        background-image: linear-gradient(0deg, #F9FCFF 28%, #EBF4FF 54%, #CCE2FF 100%);
        .top-bg{
            background-image: url(./images/blue-top-bg.svg);
        }
        .report-head-info{
            background-image: url(./images/blue-report-head.svg);

        }
    }
    &.purple{
        background-image: linear-gradient(0deg, #FCF9FF 28%, #F6F5FF 54%, #D8DBFF 100%);
        .report-head-info{
            background-image: url(./images/purple-report-head.svg);

        }
        .report-head-info{
            background-image: url(./images/purple-report-head.svg);

        }
    }

    .top-bg{
        width: 118px;
        height: 85px;
        background-size: cover;
        position: absolute;
        top: 0;
        left:0;
    }

    .report-head-info{
        padding:30px 20px;
        box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.1);
        border-radius: 2px;
        background-size: cover;
        background-repeat: no-repeat;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: right bottom;
        line-height: 1.5;
        position: relative;
        .report-head-title{
            h3{
                margin: 0 8px 0 0;
                font-family: HiraginoSansGB-W6;
                font-size: 24px;
                color: #17233D;
                font-weight: 600px;
            }
            .version-icon{
                margin-left: 8px;
            }
        }
        .report-head-sub-info{
            font-family: HiraginoSansGB-W3;
            font-size: 12px;
            color: rgba(69,79,100,0.6);
            line-height: 1;
            margin-top: 2px;
            span{
                display: inline-block;
                margin-right:10px;
                max-width: 33.33%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
        .report-final-deal{
            height: 38px;
            line-height: 38px;
            margin: 16px 0;
            display: inline-block;
            padding: 0 10px;
            border-radius: @border-radius;
            color:#fff;
            vertical-align: middle;
            font-weight: 500;
            >span{
                font-size: 16px;
                font-weight: bold;
            }
            +.report-head-person-info{
                margin: 0;
            }
        }
        .report-head-person-info{
            margin-top: 24px;
            margin-bottom: 18px;
            width: calc(100% - 100px);
            label{
                white-space: nowrap;
                color: #8B919E;
            }
        }
    }
}
