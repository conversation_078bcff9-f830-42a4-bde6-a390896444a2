import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Row, Col, Dropdown, Menu, Tag, Icon, Ellipsis, Alert, message } from 'tntd';
import { getUrlKey } from '@/utils/utils';
import service from '../service';
import './index.less';
import { isJSON } from '@tntd/utils';

export default (props) => {
    const { theme, callTime, status, title, id } = props || {};
    return (
        <>
            <div className={`report-head-info-wrap ${theme}`}>
                <div className="top-bg" />
                <div className="report-head-info">
                    <Row type="flex" justify="space-between">
                        <Row type="flex" className="report-head-title" align="middle">
                            <h3>{title}</h3>
                        </Row>
                    </Row>
                    <div className="report-head-sub-info">
                        <span>
                            <Ellipsis title={I18N.template(I18N.reportheader.index.bianHao<PERSON>, { val1: id })} />
                        </span>
                        <span>
                            <Ellipsis title={I18N.template(I18N.reportheader.index.shiJianCAL, { val1: callTime })} />
                        </span>
                    </div>
                    <div
                        className="report-final-deal"
                        style={{
                            background: `${Number(status) === 1 ? 'rgb(5, 190, 136)' : '#d96156'}`
                        }}>
                        {I18N.reportheader.index.diaoYongJieGuo}<span>{Number(status) === 1 ? I18N.inner.batchresult.chengGong : I18N.allflowtip.index.shiBai}</span>
                    </div>
                </div>
            </div>
        </>
    );
};
