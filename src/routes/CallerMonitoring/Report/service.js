import request from '@/utils/request';
import { getUrl } from '@/services/common';
import { deleteEmptyObjItem } from '@tntd/utils';

// 查询产品调用明细列表
const getInvokeList = async (param) => {
    return request(getUrl('/dataManage/serviceInterface/getInvokeDetailList', deleteEmptyObjItem(param)), {
        method: 'GET'
    });
};

// 查询产品调用明细详情
const getInvokeDetail = async (param) => {
    return request(getUrl('/dataManage/dataProduct/getInvokeDetail', deleteEmptyObjItem(param)), {
        method: 'GET'
    });
};

// 查询调用方数据
const getCallerList = async (param) => {
    return request(
        getUrl('/bridgeApi/serviceCaller/list', param),
        {
            method: 'GET'
        },
        true
    );
};

// 查询服务接口
const getServiceInterface = async (param) => {
    return request(
        getUrl('/bridgeApi/service/interface/list', param),
        {
            method: 'GET'
        },
        true
    );
};

export default {
    getInvokeList,
    getInvokeDetail,
    getCallerList,
    getServiceInterface
};
