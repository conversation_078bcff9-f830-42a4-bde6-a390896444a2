import I18N from '@/utils/I18N';
import { useState, useEffect } from 'react';
import { Button, TntdSecondPage, Ellip<PERSON>, Handle } from 'tntd';
import { getUrlKey } from '@tntd/utils';
import service from '../ServiceInterfaceCallDetail/service';
import { cloneDeep } from 'lodash';
import { connect } from 'dva';
import ReportHeader from './ReportHeader';
import { nodeTypeMap } from '~modules/Report/constant';
// import ReportContent from './ReportContent';
// import ReportHeader from '~modules/Report/ReportHeader';
import ReportContent from '~modules/Report/ReportContent';
const Report = (props) => {
    const { globalStore } = props;
    const { allMap } = globalStore;
    const [detail, setDetail] = useState();
    const { downMode } = window || {};
    const [runDataLoading, setRunDataLoading] = useState(!downMode);
    const [runDataInfo, setRunDataInfo] = useState();
    const [flowLogLoading, setFlowLogLoading] = useState(!downMode);
    const [flowLogData, setFlowLogData] = useState();
    const id = getUrlKey('tokenId');
    const status = getUrlKey('status');
    const callTime = getUrlKey('callTime');
    useEffect(() => {
        service.getReportData({ id }).then((res) => {
            let data = cloneDeep(res?.data);
            data.policyVersionDTO = res?.data.workflowVersionDTO;
            setDetail(data || {});
        });
    }, []);

    // 获取tab信息
    const getRunData = (params) => {
        if (!downMode) {
            setRunDataLoading(true);
            service
                .getRunData(params)
                .then((res) => {
                    setRunDataInfo(res?.data);
                })
                .finally(() => {
                    setRunDataLoading(false);
                });
        }
    };

    // 获取轨迹图信息
    const getFlowLog = (tokenId) => {
        if (!downMode) {
            setFlowLogLoading(true);
            return service
                .getAllCompontlog({ id: tokenId })
                .then((res) => {
                    window.allCompontlog = res?.data;
                    setFlowLogData(res?.data);
                    return res?.data;
                })
                .finally(() => {
                    setFlowLogLoading(false);
                });
        }
        setFlowLogLoading(false);
    };
    return (
        <div style={{ position: 'relative' }}>
            <ReportHeader theme="blue" status={status} callTime={callTime} title={I18N.report.index.diaoYongBaoGao} id={id} />
            <ReportContent
                allMap={allMap}
                // hideReqDesc={hideReqDesc}
                // dealTypeList={dealTypeList}
                // ruleAndIndexFieldMap={ruleAndIndexFieldMap}
                data={detail}
                defaultToken={id}
                nodeDefault={['BaseInfo', 'IndexServiceNode']}
                nodeTypeMap={nodeTypeMap}
                runDataInfoLoading={runDataLoading}
                runDataInfo={runDataInfo}
                canJump={true}
                onTabChange={(key) => {
                    if (String(key) !== '0' && String(key) !== '10') {
                        setRunDataInfo([]);
                        if (downMode) {
                            setRunDataInfo(window.reportData?.nodeRunData?.[key]);
                        } else {
                            getRunData({
                                nodeType: key,
                                tokenId: id
                            });
                        }
                    }
                    if (String(key) === '10') {
                        setRunDataInfo([]);
                        if (downMode) {
                            setRunDataInfo(window.reportData?.nodeRunData?.[8]);
                        } else {
                            getRunData({
                                nodeType: 8,
                                tokenId: id
                            });
                        }
                    }
                }}
                flowLogLoading={flowLogLoading}
                flowLogData={flowLogData}
                getFlowLog={!downMode && getFlowLog}
            />
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(Report);
