import request from '@/utils/request';
import { getUrl } from '@/services/common';
import { deleteEmptyObjItem } from '@tntd/utils';

// 查询服务接口调用明细列表
const getReportList = async (param) => {
    return request(
        getUrl('/bridgeApi/call/record/report/page', param),
        {
            method: 'GET'
        },
        true
    );
};
// 查询调用方数据
const getCallerList = async (param) => {
    return request(
        getUrl('/bridgeApi/serviceCaller/list', param),
        {
            method: 'GET'
        },
        true
    );
};

// 查询服务接口
const getServiceInterface = async (param) => {
    return request(
        getUrl('/bridgeApi/service/interface/list', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取报告数据
const getReportData = async (param) => {
    return request(
        getUrl('/bridgeApi/call/record/report/test/baseInfo/single', param),
        {
            method: 'GET'
        },
        true
    );
};

//获取所有接口名称
const interfaceList = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/list', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取接口信息
const getInterfaceDetail = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/detail', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取应用列表
const getAllField = async (params) => {
    return request(
        getUrl('/bridgeApi/systemField/allField', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};

// 获取轨迹图信息
const getAllCompontlog = async (param) => {
    return request(
        getUrl('/bridgeApi/call/record/report/getAllCompontlog', param),
        {
            method: 'GET'
        },
        true
    );
};

//服务调用明细-节点信息
const getRunData = async (param) => {
    return request(
        getUrl('/bridgeApi/call/record/report/runData', param),
        {
            method: 'GET'
        },
        true
    );
};

// 获取接口字典
const getInterfaceDict = async (params) => {
    return request(
        getUrl('/bridgeApi/service/interface/dict', deleteEmptyObjItem(params)),
        {
            method: 'GET'
        },
        true
    );
};
export default {
    getReportList,
    getCallerList,
    getServiceInterface,
    getReportData,
    interfaceList,
    getInterfaceDetail,
    getAllField,
    getAllCompontlog,
    getRunData,
    getInterfaceDict
};
