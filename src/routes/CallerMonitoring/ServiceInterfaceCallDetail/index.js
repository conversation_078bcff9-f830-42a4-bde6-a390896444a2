import I18N from '@/utils/I18N';
import { Route, Switch } from 'dva/router';
import BreadCrumb from '@tddc/bread-crumb';
import { PageContainer } from 'tntd';

import List from './List';
import Report from '../Report';
const Entrance = () => {
    return (
        <Switch>
            <Route exact name={I18N.serviceinterfacecalldetail.index.baoGao} component={Report} path="/handle/callerMonitoring/serviceInterfaceCallDetail/report" />
            <Route name={I18N.serviceinterfacecalldetail.index.fuWuDiaoYongMing} component={List} path="/" />
        </Switch>
    );
};

export default PageContainer(BreadCrumb(Entrance));
