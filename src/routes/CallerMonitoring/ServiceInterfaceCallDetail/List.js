import I18N from '@/utils/I18N';
import { useState, useRef, useEffect } from 'react';
import { connect } from 'dva';
import moment from 'moment';
import InputParams from '../InputParams';
import { DateRanges, DATA_TYPE_MAP } from '@/constants';
import service from './service';
import { Tooltip, message, Icon, Ellipsis, TableContainer, QueryListScene, Select } from 'tntd';
import QueryListWrapper from '@/components/QueryListWrapper';
const Option = Select.Option;
const { QueryForm: QueryListSceneForm, Field, QueryList, createActions } = QueryListScene;
const actions = createActions();
const query = ({ current = 1, pageSize = 10, ...rest }) => {
    const params = {
        curPage: current,
        pageSize,
        ...rest,
        startTime: rest?.date ? rest?.date[0].startOf('day').valueOf() : null,
        endTime: rest?.date ? rest?.date[1].endOf('day').valueOf() : null,
        inputParamField: rest?.inputParamField,
        inputParamFieldValue: rest?.inputParamFieldValue
    };
    delete params?.date;
    return service.getReportList(params).then((res) => {
        if (res?.success) {
            return {
                curPage: res?.data.curPage,
                pageSize: res?.data.pageSize,
                total: res?.data.total,
                data: res?.data?.contents
            };
        }
        return message.error(res?.message);
    });
};
const serviceInterfaceCallDetail = (props) => {
    const { history, globalStore } = props;
    const { orgMap, appMap } = globalStore;
    const [interfaceList, setInterfaceList] = useState([]);

    const [inputParams, setInputParams] = useState([]);

    const [serviceCode, setServiceCode] = useState('');

    //下拉框选项
    const [statusList, setStatusList] = useState([]);
    const [invokeSourceList, setInvokeSourceList] = useState([]);
    useEffect(() => {
        service.interfaceList({ curPage: 1, pageSize: 10000 }).then((res) => {
            if (res.success) {
                let list = res?.data?.contents.map((v) => {
                    return {
                        uuid: v.uuid,
                        label: v.displayName,
                        value: v.name
                    };
                });

                setInterfaceList(list);
            } else {
                message.error(res?.message);
            }
        });
        service.getInterfaceDict().then((res) => {
            setStatusList(
                res?.data?.riskInvokeStatus.map((v) => {
                    return {
                        label: v.displayName,
                        value: v.name
                    };
                })
            );
            setInvokeSourceList(
                res?.data?.bizModelEnum.map((v) => {
                    return {
                        label: v.displayName,
                        value: v.name
                    };
                })
            );
        });
    }, []);
    useEffect(() => {
        const uuid = interfaceList.find((v) => v.value === serviceCode)?.uuid;
        service.getInterfaceDetail({ uuid }).then((res) => {
            if (res.success) {
                let returnOptions = JSON.parse(res?.data?.inputConfig || []).map((item) => {
                    // 已经选择的需要禁用
                    return {
                        label: item?.displayName,
                        value: item?.field
                    };
                });

                setInputParams(returnOptions);
            }
        });
    }, [serviceCode]);
    const getReportData = ({ tokenId, callTime, status }) => {
        history.push(`/handle/callerMonitoring/serviceInterfaceCallDetail/report?tokenId=${tokenId}&callTime=${callTime}&status=${status}`);
    };
    const columns = [
        {
            title: I18N.businesschannel.diaoYongWeiYiBiao, // 调用唯一标识码
            dataIndex: 'tokenId',
            key: 'tokenId',
            width: 200,
            render: (text) => {
                return <Ellipsis widthLimit={'200px'} title={text || '- -'} />;
            }
        },
        {
            title: I18N.serviceinterfacecalldetail.list.fuWuJieKouMing,
            dataIndex: 'serviceCode',
            width: 180,
            render: (text) => {
                let data = interfaceList.find((v) => v.value === text)?.label;
                return <Ellipsis widthLimit={'180px'} title={data || '- -'} />;
            }
        },
        {
            title: I18N.serviceinterfacecalldetail.list.suoShuJiGou,
            width: 120,
            dataIndex: 'orgCode',
            render: (text, row) => {
                return <Ellipsis widthLimit={'120px'} title={orgMap[text]?.name || '- -'} />;
            }
        },
        {
            title: I18N.addmodify.index.quDao,
            width: 120,
            dataIndex: 'appCode',
            render: (text, row) => {
                return <Ellipsis widthLimit={'120px'} title={appMap[text] || '- -'} />;
            }
        },
        {
            title: I18N.serviceinterfacecalldetail.list.shuJuLaiYuanLei,
            width: 120,
            dataIndex: 'bizModel',
            render: (text, row) => {
                let name = invokeSourceList.find((v) => v.value === text);
                return <Ellipsis widthLimit={'120px'} title={name?.label || '- -'} />;
            }
        },
        {
            title: I18N.threecalldetail.fanHuiJieGuo, // 返回结果
            dataIndex: 'status',
            key: 'status',
            width: 120,
            render: (text, record) => {
                let status = statusList.find((v) => v.value === text);
                return <Ellipsis widthLimit={'120px'} title={status?.label || '- -'} />;
            }
        },
        {
            title: I18N.threecalldetail.qingQiuShiJian, // 请求时间
            dataIndex: 'callTime',
            key: 'callTime',
            width: 185,
            render: (text) => {
                let dom = (
                    <div>
                        <div>{text ? text : '--'}</div>
                    </div>
                );
                return dom;
            }
        },
        {
            title: I18N.threecalldetail.haoShiMS, // 耗时(ms)
            dataIndex: 'cost',
            key: 'cost',
            width: 100
        },
        {
            title: I18N.threecalldetail.caoZuo,
            dataIndex: 'action',
            fixed: 'right',
            width: 120,
            render: (text, record) => {
                let dom = (
                    <>
                        {/* <span className="u-operate" onClick={() => this.look(record)}>
                            查看
                        </span> */}
                        <span className="u-operate" onClick={() => getReportData(record)}>
                            {I18N.serviceinterfacecalldetail.list.baoGao}
                        </span>
                        {/* {record.returnResult === '失败' && (
                            <span className="u-operate" onClick={() => this.setState({ monitoringDetail: record })}>
                                失败详情
                            </span>
                        )} */}
                    </>
                );
                return dom;
            }
        }
    ];
    const onFormChange = (values, changeInfo) => {
        let inputParamField = Object.keys(values?.inputParamField || {})?.[0];
        let inputParamFieldValue = values?.inputParamField?.[inputParamField];
        if (['date', 'serviceCode', 'status', 'invokeSource'].includes(changeInfo.name)) {
            actions?.search({
                ...values,
                inputParamField,
                inputParamFieldValue,
                current: 1
            });
        }
    };
    return (
        <div>
            <QueryListScene query={query} actions={actions}>
                <QueryListSceneForm initialValues={{ date: [moment().subtract(6, 'days'), moment()] }} onChange={onFormChange}>
                    <Field
                        name="date"
                        type="dateRange"
                        propsTitle={I18N.threecalldetail.qingQiuShiJian}
                        props={{
                            ranges: DateRanges,
                            allowClear: false,
                            showTime: true,
                            style: { width: '360px' },
                            format: 'YYYY-MM-DD'
                        }}
                    />
                    <Field
                        type="select"
                        name="serviceCode"
                        propsTitle={I18N.addmodifymodal.index.fuWuJieKou}
                        props={{
                            placeholder: I18N.addmodifymodal.index.fuWuJieKou,
                            options: interfaceList,
                            onChange: (v) => {
                                setServiceCode(v);
                            }
                        }}
                    />
                    <Field
                        type="select"
                        name="status"
                        propsTitle={I18N.businesschannel.fanHuiJieGuo}
                        props={{
                            placeholder: I18N.businesschannel.fanHuiJieGuo,
                            options: statusList
                        }}
                    />
                    <Field
                        type="input"
                        name="bizId"
                        propsTitle={I18N.searchdrawer.index.yeWuWeiYiLiu}
                        props={{
                            placeholder: I18N.searchdrawer.index.shuRuYeWuWei
                        }}
                    />
                    <Field
                        type="input"
                        name="tokenId"
                        propsTitle={I18N.businesschannel.diaoYongWeiYiBiao}
                        props={{
                            placeholder: I18N.serviceinterfacecalldetail.list.shuRuDiaoYongWei
                        }}
                    />
                    {/* <Field
                        type="select"
                        name="inputParamField"
                        title={I18N.featureservicenode.inmaptable.ruCanZiDuan}
                        component={InputParams}
                        props={{ inputParams }}
                    /> */}
                    <Field
                        type="selectInput"
                        name="inputParamField"
                        title={I18N.featureservicenode.inmaptable.ruCanZiDuan}
                        props={{ options: inputParams }}
                    />
                    <Field
                        type="select"
                        name="invokeSource"
                        propsTitle={I18N.businesschannel.shuJuLeiXingLai}
                        props={{
                            placeholder: I18N.addmodify.qingXuanZe,
                            options: invokeSourceList
                        }}
                    />
                </QueryListSceneForm>
                <QueryList
                    unTooltip={true}
                    resizable={true}
                    rowKey="tokenId"
                    paginationSticky
                    columns={columns}
                    scroll={{ x: 1100, y: 540 }}
                />
            </QueryListScene>
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global,
    threeCallDetailStore: state.threeCallDetail
}))(TableContainer(QueryListWrapper(serviceInterfaceCallDetail, actions)));
