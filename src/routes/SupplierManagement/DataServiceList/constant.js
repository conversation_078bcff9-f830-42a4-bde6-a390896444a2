const dataTypeColorMap = [
    { name: '身份验证类', dataType: 1, color: '#07C790' },
    { name: '行为类', dataType: 2, color: '#E844B7' },
    { name: '黑名单类', dataType: 3, color: '#7A5AF8' },
    { name: '反欺诈类', dataType: 4, color: '#F7B035' },
    { name: '信用评分类', dataType: 5, color: '#126BFB' },
    { name: '人行征信类', dataType: 6, color: '#F47345' },
    { name: '其他类', dataType: 7, color: '#8B919E' }
];

export const getDataTypeColor = (dataType) => {
    const item = dataTypeColorMap.find((i) => i?.dataType === dataType);
    return item ? item?.color : '#8B919E'; // 默认灰色
};
