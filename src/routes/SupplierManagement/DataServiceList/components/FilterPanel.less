.filter-container {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-right: 8px;

    .filter-trigger {
        // width: 500px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 8px;
        
        .filter-button {
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            padding: 0 12px;
            height: 32px;
            
            &.active {
                background-color: #E6F4FF;
                color: #126BFB;
                border-color: #126BFB;
            }
        }
        
        .selected-tags {
            border: 1px solid #E1E6EE;
            border-radius: 8px;
            padding: 4px 8px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            align-items: center;
            
            .tag-item {
                display: flex;
                align-items: center;
                padding: 0 8px;
                height: 24px;
                background-color: #F8F9FB;
                border-radius: 4px;
                font-size: 12px;
                color: #17233D;
                
                .tag-close {
                    margin-left: 4px;
                    font-size: 10px;
                    cursor: pointer;
                    
                    &:hover {
                        color: #126BFB;
                    }
                }
            }
            
            .clear-all {
                color: #126BFB;
                cursor: pointer;
                font-size: 12px;
                margin-left: 4px;
                
                &:hover {
                    text-decoration: underline;
                }
            }
        }
    }
    
    .filter-panel {
        position: absolute;
        top: 40px;
        left: 0;
        z-index: 100;
        width: 330px;
        background-color: #fff;
        border-radius: 16px;
        box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.1), 0px 0px 5px -5px rgba(0, 0, 0, 0.05);
        padding: 16px;
        display: flex;
        flex-direction: column;
        gap: 16px;
        
        .filter-section {
            display: flex;
            flex-direction: column;
            gap: 8px;
            
            .filter-label {
                font-size: 14px;
                color: #17233D;
                line-height: 22px;
            }
            
            .filter-dropdown {
                height: 32px;
                background-color: #fff;
                border: 1px solid #E1E6EE;
                border-radius: 8px;
                padding: 5px 12px;
                display: flex;
                align-items: center;
                cursor: pointer;
                
                .dropdown-placeholder {
                    color: #8B919E;
                    font-size: 14px;
                }
            }
            
            .filter-options {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                
                .filter-option {
                    height: 32px;
                    padding: 5px 12px;
                    background-color: #F8F9FB;
                    border-radius: 8px;
                    font-size: 14px;
                    color: #17233D;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    transition: all 0.2s;
                    
                    &:hover {
                        opacity: 0.8;
                    }
                    
                    &:active {
                        transform: scale(0.95);
                    }
                    
                    &.selected {
                        background-color: #E6F4FF;
                        color: #126BFB;
                    }
                }
            }
        }
    }
} 