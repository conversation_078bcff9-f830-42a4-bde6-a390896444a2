.g-dataservice-detail {
    .page-global-header {
        .u-back {
            cursor: pointer;
            .tnt-current-v3 & {
                background-color: transparent;
                color: #8b919e;
                .anticon-left {
                    color: #8b919e;
                }
            }
            &:hover {
                color: #2196f3;
            }
        }
        .u-title {
            margin-left: 12px;
            font-size: 14px;
        }
    }
    .page-global-body {
        .service-header {
            display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: space-between;
            padding: 16px;
            gap: 16px;
            background-color: #fff;
            width: 100%;
            border-radius: 16px;
            .service-header-iconInfo{
                display: flex;
                align-items: center;
                .service-icon {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    width: 40px;
                    height: 40px;
                    background-color: #07c790;
                    border-radius: 100px;
                    margin-right: 16px;
                    span {
                        font-size: 20px;
                        line-height: 22px;
                        color: #ffffff;
                    }
                }
    
                .service-info {
                    display: flex;
                    flex-direction: column;
                    gap: 4px;
                    .service-title-row {
                        display: flex;
                        flex-direction: row;
                        align-items: center;
                        width: 100%;
                        height: 24px;
                        .service-title-container {
                            display: flex;
                            flex-direction: row;
                            align-items: center;
                            gap: 4px;
                            .service-title {
                                margin: 0;
                                font-family: "PingFang SC";
                                font-size: 16px;
                                line-height: 24px;
                                color: #17233d;
                                font-weight: 500;
                            }
    
                            .service-copy-icon {
                                color: #8b919e;
                                cursor: pointer;
    
                                &:hover {
                                    color: #2196f3;
                                }
                            }
                        }
                    }
                }
            }
            .service-tags {
                display: flex;
                flex-direction: row;
                align-items: center;
            }
        }
        .service-content {
            margin-top: 12px;
            width: 100%;
            height: 100%;
            background-color: #ffffff;
            border-radius: 16px;
            padding: 16px;
            .service-content-basicInfo {
                margin-bottom: 16px;
            }
            .service-content-contractBilling{
                margin-bottom: 16px;
            }
            .service-content-flowControl {
                margin-top: 16px;
                &-content {
                    width: 100%;
                    height: 40px;
                    display: flex;
                    border: 1px solid #8cc4ff;
                    border-radius: 16px;
                    background-color: #e6f4ff;
                    padding: 9px 20px;
                    margin: 8px 0;
                    &::before {
                        margin-top: 3px;
                        display: inline-block;
                        margin-right: 4px;
                        content: "";
                        background: url("@/sources/images/common/icon_info.svg")
                            no-repeat center center;
                        width: 16px;
                        height: 16px;
                    }
                }
            }
            .service-content-detail {
                margin-top: 8px;
                border-radius: 8px;
                padding: 12px;
                background-color: #f8f9fb;
                .ant-descriptions-item-content{
                    height: 32px;
                    margin: 4px 0;
                }
                &-badge{
                    vertical-align: top;
                }
                &-output {
                    margin: 4px 0 16px;
                    span {
                        font-size: 14px;
                        color: #8b919e;
                    }
                    div {
                        border-radius: 8px;
                        padding: 12px;
                        background-color: #fff;
                        margin-top: 8px;
                    }
                }
            }
        }
    }
}
