import React, { createContext } from 'react';

export const AnalysisContext = createContext();

const decoder = new TextDecoder('utf-8');

export function handleStreamResponse({ reader, onData, onSuccess, onFail }) {
    let buffer = '';
    let bufferObj;

    const reading = () => {
        let hasError = false;
        reader?.read().then(({ done, value }) => {
            if (done) {
                onSuccess && onSuccess();
                return null;
            }
            buffer += decoder.decode(value, { stream: true });
            const lines = buffer.split('\n');
            let isStop = false;
            try {
                let newMd = '';
                let taskId = '';
                let conversationId = '';
                let isError = false;
                let messageId = '';
                let metricUuid = '';
                let isEnd = false;
                let status = '';
                lines.forEach((message) => {
                    if (message.startsWith('data:')) {
                        try {
                            bufferObj = JSON.parse(message.substring(5));
                        } catch (e) {
                            isError = true;
                            return;
                        }
                        if (bufferObj.status === 400 || !bufferObj.event) {
                            hasError = true;
                            return;
                        }
                        if (bufferObj.event === 'message') {
                            newMd += bufferObj.answer;
                        }
                        if (bufferObj.event === 'workflow_finished') {
                            newMd = bufferObj?.data?.outputs?.answer;
                            status = bufferObj?.data?.status;
                            isEnd = true;
                        }

                        taskId = bufferObj.task_id;
                        conversationId = bufferObj.conversation_id;
                        messageId = bufferObj.message_id;
                    }
                });
                if (onData) {
                    isStop = onData({ answer: newMd, taskId, conversationId, messageId, metricUuid, isEnd, status });
                }
                buffer = isError ? lines[lines.length - 1] : '';
            } catch (e) {
                hasError = true;
                onFail && onFail(e);
                return;
            }
            if (!hasError) {
                !isStop && reading();
            } else {
                onFail && onFail(new Error('Stream response error'));
            }
        });
    };

    return reading();
}
