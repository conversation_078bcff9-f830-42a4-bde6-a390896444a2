/*
 * @Author: liu<PERSON>
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: mock 配置页
 */

import I18N from '@/utils/I18N';
import './index.less';
import aiGenerated from '@/sources/images/common/generated.svg';
import React, { PureComponent, Suspense, Fragment } from 'react';
import { connect } from 'dva';
import { uniqueId, uniqBy, filter } from 'lodash';
import service from '@/services/supplierManagement/aiservice';
import Answer from './Answer';
import { AnalysisContext, handleStreamResponse } from '../utils';
import {
    Button,
    Table,
    Pagination,
    Select,
    message,
    Input,
    Switch,
    Breadcrumb,
    Divider,
    Popconfirm,
    Popover,
    Tooltip,
    Icon,
    PageContainer,
    Empty,
    Spin,
    notification,
    Ellipsis
} from 'tntd';

const TestModal = React.lazy(() => import('../../TestModal'));
import { dataServiceListAPI } from '@/services';
import { checkFunctionHasPermission } from '@/utils/permission';
import { getUrlKey } from '@/utils/utils';
import AddModifyModal from './Inner/AddModifyModal';
import ImportModal from './Inner/ImportModal';
import ConfigModal from './Inner/ConfigModal';
import AiDataMockModal from './Inner/AiDataMockModal';
import AiSystemMockDrawer from './Inner/AiSystemMockDrawer';

const Option = Select.Option;
const { TextArea } = Input;
const InputGroup = Input.Group;

class MockConfig extends PureComponent {
    constructor(props) {
        super(props);
        this.state = {
            loading: false,
            tableList: [],
            total: 0,
            searchParams: {
                curPage: 1,
                pageSize: 10
            },
            mockFlag: '0',
            mockType: '1',
            visible: false,
            importVisible: false,
            inputConfig: [],
            mockField: [],
            mockData: null,
            searchType: null,
            searchValue: null,
            configVisible: false,
            addModifyData: null,
            testVisible: false,
            testId: null,
            testTitle: null,
            testName: null,
            testMockFlag: null,
            aiMockVisible: false,
            aiMockSystemVisible: false,
            aiMockData: null,
            isStop: false,
            messages: [],
            conversationId: '',
            message_id: '',
            questionLoading: false,
            itOutputEnd: true,
            editableContent: '',
            aiMockLoading: false, // AI生成loading状态
            aiMockUuid: '',
            generateNumber: 0,
            // 系统模拟专用状态
            systemMockContent: '',
            systemMockLoading: false,
            systemMockMessages: [],
            systemMockConversationId: ''
        };
        this.controllerRef = null;
        this.systemControllerRef = null;
        this.timerRef = null;
        this.charsQueueRef = [];
    }

    // 新增：提取第一个代码块内容的工具函数
    extractFirstCodeBlock(markdown) {
        const lines = (markdown || '').split('\n');
        let inBlock = false;
        let blockContent = [];
        for (let line of lines) {
            if (!inBlock && line.trim().startsWith('```')) {
                inBlock = true;
                continue;
            }
            if (inBlock && line.trim().startsWith('```')) {
                break;
            }
            if (inBlock) {
                blockContent.push(line);
            }
        }
        return blockContent.length > 0 ? blockContent.join('\n').trim() : '';
    }

    componentDidUpdate(prevProps, prevState) {
        // // 监听 props.response 变化，更新 editableContent
        if (this.props.response !== prevProps.response && this.props.response && this.state.mockType === '1') {
            this.setState({ editableContent: this.props.response });
        }

        // 监听 editableContent 变化，自动同步到父组件（包括空字符串）
        if (
            this.props.getAiMockData &&
            this.state.editableContent !== prevState.editableContent &&
            this.state.mockType === '1' // mockType为1时才同步
        ) {
            // 只把数据调用模拟ai获取数据传给父组件，包括空字符串
            this.props.getAiMockData(this.state.editableContent);
        }
    }

    async componentDidMount() {
        await this.getDetail();
        await this.getData();

        // 初始化时确保editableContent有正确的值
        if (this.state.mockType === '1') {
            this.setState({
                editableContent: this.props.response || ''
            });
        }

        this.timer = setInterval(() => {
            const { globalStore } = this.props;
            const { menuTreeReady } = globalStore;
            if (menuTreeReady) {
                clearInterval(this.timer);
                if (checkFunctionHasPermission('TZ0202', 'query')) {
                    // this.search();
                }
            }
        }, 100);
    }

    getDetail = async () => {
        const uuid = this.props.modalType === 1 ? this.props.uuid : getUrlKey('uuid');
        await dataServiceListAPI.getDetail({ uuid }).then((res) => {
            if (res && res.success) {
                const data = res.data;
                let inputConfig = data.inputConfig ? JSON.parse(data.inputConfig) : [];
                let copyInputConfig = [];
                inputConfig.forEach((item) => {
                    if (item.type === 'variable') {
                        copyInputConfig.push(item);
                    }
                });
                this.setState({
                    mockFlag: data.mockFlag ? `${data.mockFlag}` : '0',
                    mockType: data.mockType ? `${data.mockType}` : '1',
                    inputConfig: copyInputConfig,
                    mockField: data.mockField ? JSON.parse(data.mockField) : [],
                    searchType: data.inputConfig ? copyInputConfig[0]?.field : null
                });
            } else {
                message.error(res.message);
            }
        });
    };

    updateMockConfig = (mockFlag, mockType, mockFields, mockData, type) => {
        const uuid = this.props.modalType === 1 ? this.props.uuid : getUrlKey('uuid');
        const params = {
            uuid,
            mockFlag,
            mockType: mockType ? mockType : null,
            mockFields: mockFields ? mockFields : null,
            mockData: mockData ? mockData : null
        };
        dataServiceListAPI.updateMockConfig(params).then((res) => {
            if (res && res.success) {
                if (type) {
                    message.success(I18N.mockconfig.index.baoCunChengGong); // 保存成功
                } else {
                    this.getDetail();
                }
            } else {
                message.error(res.message);
            }
        });
    };

    handleCancel = () => {
        this.setState({
            visible: false,
            importVisible: false,
            configVisible: false,
            testVisible: false,
            testId: null,
            testTitle: null,
            testName: null,
            testMockFlag: null
        });
    };

    //数据模拟调用
    handleAiMock = () => {
        this.setState({ aiMockVisible: true });
    };
    //系统模拟调用
    handleAiSystemMock = () => {
        if (this.state.aiMockLoading) {
            message.warn('请等待任务生成完成');
            return;
        }
        this.setState({ aiMockSystemVisible: true });
    };
    handleAiMockOk = (params) => {
        // 每次AI生成前清空历史消息
        this.setState({ messages: [] }, () => {
            this.handleSendMessage(params);
            this.setState({ aiMockVisible: false });
        });
    };
    scrollToBottom = () => {
        const drawerWrapperBody = document.querySelector('.mock-data-answer-container');
        if (drawerWrapperBody) {
            drawerWrapperBody.scrollTo({
                top: drawerWrapperBody.scrollHeight,
                behavior: 'smooth'
            });
        }
    };
    handleSendMessage = (message, propsScene = 'test', extraParams = {}) => {
        // 清空历史消息
        this.setState({ messages: [] }, () => {
            const newMessage = {
                id: uniqueId('q_'),
                type: 'question',
                content: message,
                time: Date.now(),
                scene: propsScene
            };
            this.setState(
                (prevState) => ({ messages: [...prevState.messages, newMessage] }),
                () => {
                    this.doSendStreaming(message, null, null, propsScene, extraParams);
                    setTimeout(() => {
                        this.scrollToBottom();
                    }, 1000);
                }
            );
        });
    };
    // doSendStreaming 迁移
    doSendStreaming = (inputValue, pId, cId, curScene, extraParams) => {
        this.controllerRef = new AbortController();
        const signal = this.controllerRef.signal;
        this.setState({ isStop: false, loading: true });
        // 生成10000-99999之间的随机整数
        const randomId = Math.floor(Math.random() * 90000) + 10000;
        const obj = {
            scene: 'test',
            id: randomId,
            type: 'answer',
            data: { id: randomId, md: '', startTime: Date.now(), title: inputValue, isLoading: true }
        };
        this.setState((prevState) => ({ messages: [...prevState.messages, obj] }));
        return service
            .getChatData(
                {
                    query: inputValue,
                    inputs: {
                        ...extraParams
                    },
                    message_id: ('test' && this.state.message_id) || '',
                    response_mode: 'streaming',
                    conversation_id: this.state.conversationId || '',
                    scene: 'test',
                    ...extraParams
                },
                { signal }
            )
            .then((res) => {
                // 获取可读流
                const reader = res?.body?.getReader();

                const id = uniqueId('a_');
                const obj = {
                    scene: 'test',
                    id,
                    type: 'answer',
                    data: { id, md: '', startTime: Date.now(), title: inputValue, reader }
                };
                if (!pId) {
                    this.setState((prevState) => {
                        const filteredList = prevState.messages.filter((item) => item.id !== randomId);
                        return { messages: [...filteredList, obj] };
                    });
                }
                const ending = (isError) => {
                    if (!pId) {
                        obj.data.endTime = Date.now();
                        obj.data.isError = isError;
                        obj.data.suggestedQuestions = [];
                        if (isError && !obj.data.md) {
                            obj.data.md = I18N.aidrawer.index.fenXiShiBai;
                        }
                        this.setState((prevState) => ({ messages: [...prevState.messages] }));
                        // 只有流式真正结束时才恢复按钮
                        this.setState({ loading: false });
                    }
                };

                handleStreamResponse({
                    reader,
                    _onData: ({ answer, taskId, conversationId: newConversationId, messageId, metricUuid, isEnd, status }) => {
                        if (newConversationId !== this.state.conversationId && newConversationId) {
                            this.setState({ conversationId: newConversationId });
                        }
                        if (!pId) {
                            // 判断是否有正在进行的打字机效果
                            if (this.charsQueueRef.length > 0) {
                                if (answer?.length > 0) {
                                    return;
                                }
                                // 已有打字机效果在进行，将新的字符添加到队列中
                                this.charsQueueRef = [...this.charsQueueRef, ...answer.split('')];
                            } else if ((answer.length !== obj.data.md.length && answer.length > 200) || this.charsQueueRef.length > 0) {
                                // 对于长回答或队列中已有字符，启动打字机效果
                                let currentAnswer = obj.data.md || '';
                                // 将新字符添加到队列中
                                this.charsQueueRef = [...this.charsQueueRef, ...answer.split('')];

                                if (this.state.itOutputEnd) {
                                    this.setState({ itOutputEnd: false });
                                }

                                const loadAnswer = () => {
                                    if (this.charsQueueRef.length > 0) {
                                        // 每次取出队列中的一个字符
                                        const char = this.charsQueueRef.shift();
                                        currentAnswer += char;
                                        obj.data.md = currentAnswer;
                                        // 同步到 editableContent
                                        this.setState({ editableContent: currentAnswer });
                                        this.setState((prevState) => ({ messages: [...prevState.messages] }));

                                        // 继续处理队列中的下一个字符
                                        this.timerRef = setTimeout(loadAnswer, 5);
                                    } else {
                                        // 队列为空，打字机效果结束
                                        this.setState({ itOutputEnd: true });
                                        this.timerRef = null;
                                    }
                                };
                                loadAnswer();
                            } else {
                                // 对于短回答，直接添加
                                if (isEnd) {
                                    obj.data.md = answer;
                                    // 新增：同步到 editableContent
                                    this.setState({ editableContent: answer });
                                } else {
                                    obj.data.md += answer;
                                    // 新增：同步到 editableContent
                                    this.setState({ editableContent: obj.data.md });
                                }
                                this.setState((prevState) => ({ messages: [...prevState.messages] }));
                            }
                            obj.data.taskId = taskId;
                            obj.data.messageId = messageId;
                            obj.data.conversationId = newConversationId;
                            if (status) {
                                obj.data.status = status;
                            }
                            this.setState((prevState) => ({ messages: [...prevState.messages] }));
                            if (obj.data.endTime) {
                                return true;
                            }
                        }
                    },
                    get onData() {
                        return this._onData;
                    },
                    set onData(value) {
                        this._onData = value;
                    },
                    onSuccess: ending,
                    onFail: ending
                });
            })
            .finally(() => {
                this.setState((prevState) => {
                    // 检查id为999的消息后面是否有其他消息
                    const index999 = prevState.messages.findIndex((item) => item.id === randomId);
                    if (index999 !== -1 && index999 < prevState.messages.length - 1) {
                        // 如果id为999的消息后面有其他消息，则清理掉id为999的消息
                        return { messages: prevState.messages.filter((item) => item.id !== randomId) };
                    }
                    return { messages: [...prevState.messages] };
                });
                // this.setState({ loading: false }); // 移除这里的loading=false
            });
    };
    stopMessage = () => {
        let messageList = this.state.messages;
        // 只把最后一条消息标记为停止
        if (messageList.length > 0) {
            const lastIndex = messageList.length - 1;
            messageList = messageList.map((message, index) => {
                if (index === lastIndex && message.data) {
                    return {
                        ...message,
                        data: {
                            ...message.data,
                            isStop: true
                        }
                    };
                }
                return message;
            });
        }
        this.setState({ messages: [...messageList] });
        return messageList;
    };
    stopAiGeneration = async () => {
        // 中断当前的流式请求
        try {
            // 1. 先中断当前的流式请求
            this.setState({ isStop: true });
            this.stopMessage();

            // 清除打字机效果定时器
            this.charsQueueRef = [];
            if (this.timerRef) {
                clearTimeout(this.timerRef);
                this.timerRef = null;
                this.setState({ itOutputEnd: true }); // 标识输出已完成
            }

            await this.controllerRef?.abort();
        } catch (error) {
            // 忽略 AbortError，这是预期的错误
            if (error.name === 'AbortError' || error.message.includes('aborted')) {
                return '';
            }
        }
        // 立刻恢复按钮可用
        this.setState({ loading: false });
    };
    handleAiMockCancel = () => {
        this.setState({ aiMockVisible: false });
    };
    handleAiSystemMockCancel = () => {
        this.setState({
            aiMockSystemVisible: false,
            systemMockContent: '',
            systemMockLoading: false,
            systemMockMessages: []
        });
    };
    onStop = () => {
        this.stopAiGeneration();
    };

    // 系统模拟专用的消息处理方法
    handleSystemMockSendMessage = (message, propsScene = 'system', extraParams = {}) => {
        // 清空历史消息
        this.setState({ systemMockMessages: [] }, () => {
            const newMessage = {
                id: uniqueId('q_'),
                type: 'question',
                content: message,
                time: Date.now(),
                scene: propsScene
            };
            this.setState(
                (prevState) => ({ systemMockMessages: [...prevState.systemMockMessages, newMessage] }),
                () => {
                    this.doSystemMockSendStreaming(message, null, null, propsScene, extraParams);
                }
            );
        });
    };

    // 系统模拟专用的流式处理方法
    doSystemMockSendStreaming = (inputValue, pId, cId, curScene, extraParams) => {
        this.systemControllerRef = new AbortController();
        const signal = this.systemControllerRef.signal;
        this.setState({ systemMockLoading: true });

        // 生成随机ID
        const randomId = Math.floor(Math.random() * 90000) + 10000;
        const obj = {
            scene: 'system',
            id: randomId,
            type: 'answer',
            data: { id: randomId, md: '', startTime: Date.now(), title: inputValue, isLoading: true }
        };
        this.setState((prevState) => ({ systemMockMessages: [...prevState.systemMockMessages, obj] }));

        return service
            .getChatData(
                {
                    query: inputValue,
                    inputs: {
                        ...extraParams
                    },
                    message_id: '',
                    response_mode: 'streaming',
                    conversation_id: this.state.systemMockConversationId || '',
                    scene: 'system',
                    ...extraParams
                },
                { signal }
            )
            .then((res) => {
                const reader = res?.body?.getReader();
                const id = uniqueId('a_');
                const answerObj = {
                    scene: 'system',
                    id,
                    type: 'answer',
                    data: { id, md: '', startTime: Date.now(), title: inputValue, reader }
                };

                this.setState((prevState) => {
                    const filteredList = prevState.systemMockMessages.filter((item) => item.id !== randomId);
                    return { systemMockMessages: [...filteredList, answerObj] };
                });

                const ending = (isError) => {
                    answerObj.data.endTime = Date.now();
                    answerObj.data.isError = isError;
                    if (isError && !answerObj.data.md) {
                        answerObj.data.md = '分析失败';
                    }
                    this.setState({ systemMockLoading: false });
                };

                handleStreamResponse({
                    reader,
                    _onData: ({ answer, taskId, conversationId: newConversationId, messageId, metricUuid, isEnd, status }) => {
                        if (newConversationId !== this.state.systemMockConversationId && newConversationId) {
                            this.setState({ systemMockConversationId: newConversationId });
                        }

                        // 直接更新内容，不使用打字机效果
                        if (isEnd) {
                            answerObj.data.md = answer;
                            this.setState({ systemMockContent: answer });
                        } else {
                            answerObj.data.md += answer;
                            this.setState({ systemMockContent: answerObj.data.md });
                        }
                        this.setState((prevState) => ({ systemMockMessages: [...prevState.systemMockMessages] }));
                    },
                    get onData() {
                        return this._onData;
                    },
                    set onData(value) {
                        this._onData = value;
                    },
                    onSuccess: ending,
                    onFail: ending
                });
            })
            .finally(() => {
                this.setState((prevState) => {
                    const index = prevState.systemMockMessages.findIndex((item) => item.id === randomId);
                    if (index !== -1 && index < prevState.systemMockMessages.length - 1) {
                        return { systemMockMessages: prevState.systemMockMessages.filter((item) => item.id !== randomId) };
                    }
                    return { systemMockMessages: [...prevState.systemMockMessages] };
                });
            });
    };

    // 系统模拟专用的停止方法
    onSystemMockStop = async () => {
        try {
            this.setState({ systemMockLoading: false });
            await this.systemControllerRef?.abort();
        } catch (error) {
            if (error.name === 'AbortError' || error.message.includes('aborted')) {
                return '';
            }
        }
    };

    // 获取列表数据
    getData = (curPage, pageSize) => {
        const uuid = this.props.modalType === 1 ? this.props.uuid : getUrlKey('uuid');
        let { searchParams, searchType, searchValue, mockType } = this.state;
        if (curPage) searchParams.curPage = curPage;
        if (pageSize) searchParams.pageSize = pageSize;
        searchParams.uuid = uuid;
        searchParams.mockType = mockType;
        if (searchValue) {
            let obj = {};
            obj[searchType] = searchValue;
            searchParams.query = JSON.stringify(obj);
        }

        this.setState({ loading: true });
        dataServiceListAPI
            .getMockData({ ...searchParams })
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    if (mockType === '1') {
                        const mockDataValue = res.data.contents[0] ? res.data.contents[0].dataValue : null;
                        this.setState({
                            mockData: mockDataValue,
                            // 确保mockType为'1'时editableContent能正确同步数据
                            editableContent: this.props.response || mockDataValue || ''
                        });
                    } else {
                        const tableList = res?.data?.contents;
                        this.setState({
                            total: res.data.total,
                            tableList,
                            searchParams: {
                                curPage: res.data.curPage,
                                pageSize: res.data.pageSize
                            }
                        });
                        // 调用父组件传入的回调函数，传递tableList数据
                        if (this.props.getMockTableList) {
                            this.props.getMockTableList(tableList);
                        }
                    }
                } else {
                    message.error(res.message);
                }
            })
            .catch(() => {
                this.setState({ loading: false });
            });
    };

    // 查询
    search = (curPage, pageSize) => {
        this.getData(curPage, pageSize);
    };

    // 分页
    paginationOnChange(curPage, pageSize) {
        this.search(curPage, pageSize);
    }

    // 改变参数
    async changeField(e, field) {
        const { mockFlag } = this.state;

        let obj = {};
        if (field === 'mockFlag' && !e) {
            obj.mockType = '1';
            obj[field] = '0';
            this.updateMockConfig('0', '1');
        } else if (field === 'mockFlag' && e) {
            obj[field] = '1';
            this.updateMockConfig('1', '1');
        } else if (field === 'mockType') {
            obj[field] = e;
            this.updateMockConfig(mockFlag, e);
            // 当切换到mockType为'1'时，确保editableContent有数据
            if (e === '1') {
                obj.editableContent = this.props.response || this.state.mockData || '';
            }
        } else if (field === 'searchType') {
            obj[field] = e;
            obj.searchValue = null;
        } else {
            obj[field] = e;
        }
        await this.setState({ ...obj });
        if (field === 'mockType') {
            this.search();
        }
    }

    // 新增
    handleAdd = () => {
        const { inputConfig } = this.state;
        let mockDataInput = [];
        inputConfig.forEach((sItem) => {
            mockDataInput.push({
                name: sItem.field,
                displayName: sItem.displayName,
                value: null
            });
        });
        let addModifyData = {
            mockDataInput,
            mockDataOutput: null,
            type: 1,
            uuid: this.props.modalType === 1 ? this.props.uuid : getUrlKey('uuid')
        };
        this.setState({
            visible: true,
            addModifyData
        });
    };
    onSystemMockOK = (params) => {
        const { amount } = params;
        this.setState({ generateNumber: amount });
        this._aiMockStopped = false;
        service.addAiMockData(params).then((res) => {
            if (res && res?.success) {
                this.handleAiSystemMockCancel();
                this.setState({ aiMockLoading: true }); // 展示loading卡片
                //获取接口状态，5s请求一次
                this.pollAiMockStatus(params, 0);
            } else {
                message.error(res?.message);
            }
        });
    };

    pollAiMockStatus = (params, tryCount = 0) => {
        // 如果被停止，直接终止轮询
        if (this._aiMockStopped) {
            return;
        }
        service.getAddAiMockDataStatus().then((res) => {
            if (res && res?.success && res?.data && (res?.data?.status === 'SUCCESS' || res?.data?.status === 'FAILED')) {
                // 停止loading，刷新数据
                this.setState({ aiMockLoading: false, aiMockUuid: res?.data?.uuid });
                this.getData(); //刷新列表卡片数据
                if (res?.data?.status === 'SUCCESS') {
                    this.setState({ generateNumber: res?.data?.amount });
                    notification.success({
                        message: '生成成功',
                        description: `已成功生成${this.state.generateNumber}条数据`
                    });
                } else {
                    notification.error({
                        message: '生成失败',
                        description: '请修改样例报文或数据量后重新尝试'
                    });
                }
            } else {
                // 未完成，3秒后继续轮询
                if (this._aiMockStopped) return;
                setTimeout(() => {
                    this.pollAiMockStatus(params, tryCount + 1);
                }, 3000);
            }
        });
    };
    handleStopAiMockLoading = () => {
        this._aiMockStopped = true;
        const aiMockUuid = this.state.aiMockUuid;
        service.stopAddAiMockData({ uuid: aiMockUuid }).then((res) => {
            if (res && res?.success) {
                this.setState({ aiMockLoading: false });
                this.getData(); // 刷新数据
            } else {
                message.error('停止失败');
            }
        });
    };

    // 导入
    handleImport = () => {
        this.setState({ importVisible: true });
    };

    // 保存
    save = () => {
        const { mockData } = this.state;
        this.updateMockConfig(null, null, null, mockData, 'save');
    };

    // 测试
    handleTest = () => {
        const uuid = this.props.modalType === 1 ? this.props.uuid : getUrlKey('uuid');
        dataServiceListAPI.getDetail({ uuid }).then((res) => {
            if (res && res.success) {
                const data = res.data;
                this.setState({
                    testVisible: true,
                    testId: data.uuid,
                    testTitle: data.displayName,
                    testName: data.name,
                    testMockFlag: data.mockFlag
                });
            } else {
                message.error(res.message);
            }
        });
    };

    // 参数配置
    handleConfig = () => {
        this.setState({
            configVisible: true
        });
    };

    confirmDelete(uuid) {
        dataServiceListAPI.deleteMockData({ uuid }).then((res) => {
            if (res && res.success) {
                let { searchParams, tableList } = this.state;
                let { pageSize, curPage } = searchParams;
                if (tableList.length === 1 && curPage > 1) curPage = curPage - 1;
                this.paginationOnChange(curPage, pageSize);
            } else {
                message.error(res.message);
            }
        });
    }

    getAiMockData = () => {
        const { editableContent } = this.state;
        this.props.getAiMockData(editableContent);
    };

    handleModify(record) {
        const { inputConfig } = this.state;
        let mockDataInput = [];
        inputConfig.forEach((sItem) => {
            mockDataInput.push({
                name: sItem.field,
                displayName: sItem.displayName,
                value: null
            });
        });
        if (record.queryParam) {
            const obj = JSON.parse(record.queryParam);
            for (let key in obj) {
                mockDataInput.forEach((item) => {
                    if (key === item.name) {
                        item.value = obj[key];
                    }
                });
            }
        }

        let addModifyData = {
            mockDataInput,
            mockDataOutput: record.dataValue,
            type: 2,
            mockId: record.uuid
        };
        this.setState({
            visible: true,
            addModifyData
        });
    }

    getColumns = () => {
        const columns = [
            {
                title: I18N.mockconfig.index.ruCanPiPeiZi, // 入参匹配字段
                dataIndex: 'queryParam',
                render: (text) => {
                    let dom;
                    let arr = [];
                    if (text) {
                        let obj;
                        obj = JSON.parse(text);
                        for (let key in obj) {
                            arr.push({
                                name: key,
                                value: obj[key]
                            });
                        }
                    }
                    return (
                        <Fragment>
                            {arr.map((item, index) => {
                                return (
                                    <span key={index}>
                                        {item.name}:{item.value};{' '}
                                    </span>
                                );
                            })}
                        </Fragment>
                    );
                }
            },
            {
                title: I18N.mockconfig.index.chuCanBaoWen, // 出参报文
                dataIndex: 'dataValue',
                render: (text) => {
                    const content = <pre>{text ? JSON.stringify(JSON.parse(text), null, 2) : []}</pre>;
                    return (
                        <Popover
                            content={content}
                            title={I18N.mockconfig.index.chuCanBaoWen} // 出参报文
                            placement="left">
                            <span className="u-ellipsis">{text}</span>
                        </Popover>
                    );
                }
            },
            {
                title: I18N.mockconfig.index.caoZuo, // 操作
                dataIndex: 'operate',
                width: 140,
                render: (text, record) => {
                    return (
                        <Fragment>
                            <a onClick={() => this.handleModify(record)}>{I18N.mockconfig.index.xiuGai}</a>
                            <Divider type="vertical" />
                            <Popconfirm title={I18N.mockconfig.index.queRenShanChuCi} onConfirm={() => this.confirmDelete(record.uuid)}>
                                <a>{I18N.mockconfig.index.shanChu}</a>
                            </Popconfirm>
                        </Fragment>
                    );
                }
            }
        ];
        return columns;
    };

    back = () => {
        this.props.history.push('/handle/supplierManagement/dataServiceList');
    };

    // 渲染 Mock 数据卡片
    renderMockDataCards = () => {
        const { tableList, aiMockLoading } = this.state;

        if ((!tableList || tableList.length === 0) && !aiMockLoading) {
            return <Empty type="no-result" />;
        }

        return (
            <div className="mock-data-container">
                {aiMockLoading && (
                    <div className="mock-data-card mock-data-loading-card">
                        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                            <Spin indicator={<Icon type="loading" style={{ fontSize: 16 }} spin />} style={{ marginRight: 12 }} />
                            <span>正在为您生成{this.state.generateNumber}条数据...</span>
                        </div>
                        <Icon style={{ cursor: 'pointer', fontSize: 20 }} type="quit" onClick={this.handleStopAiMockLoading} />
                    </div>
                )}
                {/* 下面是原有的卡片渲染 */}
                {tableList.map((item, index) => {
                    let queryObj = {};
                    if (item.queryParam) {
                        queryObj = JSON.parse(item.queryParam);
                    }

                    // 格式化 JSON 数据
                    let formattedData = '{}';
                    try {
                        if (item.dataValue) {
                            formattedData = JSON.stringify(JSON.parse(item.dataValue), null, 4);
                        }
                    } catch (error) {
                        formattedData = item.dataValue || '{}';
                    }

                    return (
                        <div className="mock-data-card" key={index}>
                            <div className="card-header">
                                {Object.keys(queryObj).length > 0 &&
                                    Object.keys(queryObj).map((key, i) => (
                                        <div className="header-row" key={i}>
                                            <Ellipsis
                                                widthLimit={190}
                                                title={
                                                    <>
                                                        <span className="field-label">{key} </span>
                                                        <span className="field-value">{queryObj[key]}</span>
                                                    </>
                                                }
                                            />
                                        </div>
                                    ))}
                            </div>
                            <div className="card-content">
                                <pre>{formattedData}</pre>
                            </div>
                            <div className="card-action-buttons">
                                <div className="action-button" onClick={() => this.handleModify(item)}>
                                    <Icon type="form" />
                                </div>
                                <Popconfirm title={I18N.mockconfig.index.queRenShanChuCi} onConfirm={() => this.confirmDelete(item.uuid)}>
                                    <div className="action-button">
                                        <Icon type="delete" />
                                    </div>
                                </Popconfirm>
                            </div>
                        </div>
                    );
                })}
            </div>
        );
    };

    render() {
        const columns = this.getColumns();
        const {
            loading,
            tableList,
            total,
            searchParams,
            mockFlag,
            mockType,
            visible,
            importVisible,
            mockData,
            inputConfig,
            searchType,
            searchValue,
            configVisible,
            mockField,
            addModifyData
        } = this.state;
        const { isMockTitle, uuid, modalType, getAiMockData, documentTypeList = [], response } = this.props;

        const tipContent = (
            <div>
                <div>{I18N.mockconfig.index.mOCKNei2}</div>
                <div>{I18N.mockconfig.index.mOCKWai2}</div>
            </div>
        );

        return (
            <div className="g-mock-config">
                {/* Mock配置控件 */}
                <div className="mock-config-controls">
                    <div className="switch-container">
                        <span className="switch-label">Mock 开启状态：</span>
                        <Switch
                            checkedChildren={I18N.mockconfig.index.shi} // 是
                            unCheckedChildren={I18N.mockconfig.index.fou} // 否
                            checked={mockFlag === '1'}
                            onChange={(e) => this.changeField(e, 'mockFlag')}
                        />
                    </div>
                    <Select
                        className="select-container"
                        dropdownMatchSelectWidth={false}
                        value={mockType ? mockType : undefined}
                        onChange={(e) => this.changeField(e, 'mockType')}>
                        <Option value="1">{'接口模拟调用'}</Option>
                        <Option value="2">{'系统调用模拟'}</Option>
                    </Select>
                    {/* <Tooltip title={tipContent}>
                        <Icon type="question-circle" className="u-tip" />
                    </Tooltip> */}
                </div>

                {mockType === '2' && (
                    <div className="search-controls">
                        <div className="search-form">
                            <Select
                                className="field-select"
                                value={searchType}
                                dropdownMatchSelectWidth={false}
                                onChange={(e) => this.changeField(e, 'searchType')}>
                                {inputConfig &&
                                    inputConfig.map((item, index) => (
                                        <Option value={item.field} key={index}>
                                            {item.field}
                                        </Option>
                                    ))}
                            </Select>
                            <Input
                                className="field-input"
                                placeholder={I18N.mockconfig.index.qingShuRu} // 请输入
                                value={searchValue}
                                onChange={(e) => this.changeField(e.target.value, 'searchValue')}
                                onPressEnter={() => this.search()}
                            />
                        </div>

                        <div className="action-buttons">
                            <Button className="action-button" type="default" icon="import" onClick={this.handleImport} />
                            <Button className="action-button" type="default" onClick={this.handleTest}>
                                测试
                            </Button>
                            <Button className="action-button" type="default" onClick={this.handleAdd}>
                                手动新增
                            </Button>
                            <Button className="ai-mock-action-button" onClick={this.handleAiSystemMock}>
                                <img src={aiGenerated} alt="AI" className="ai-mock-icon" />
                                生成
                            </Button>
                        </div>
                    </div>
                )}

                {mockType === '1' && (
                    <div className="mock-data-answer-container">
                        <div className="action-buttons">
                            {/* <Button type="primary" onClick={this.save} style={{ borderRadius: '8px' }}>
                            {I18N.mockconfig.index.baoCun}
                        </Button> */}
                            <Button className="action-button" disabled={this.state.loading} type="default" onClick={this.handleTest}>
                                测试
                            </Button>
                            {this.state.loading ? (
                                <Button className="ai-mock-action-button stop-button" onClick={this.onStop}>
                                    <Icon type="quit" />
                                    停止
                                </Button>
                            ) : (
                                <Button className="ai-mock-action-button" onClick={this.handleAiMock}>
                                    <img src={aiGenerated} alt="AI" className="ai-mock-icon" />
                                    生成
                                </Button>
                            )}
                        </div>
                        {this.state.loading ? (
                            <div className="mock-data-answer-content">
                                <AnalysisContext.Provider
                                    value={{
                                        handleSendMessage: this.handleSendMessage,
                                        setMessageId: (id) => this.setState({ message_id: id }),
                                        isRunning: this.state.loading,
                                        conversationId: this.state.conversationId
                                    }}>
                                    {this.state.messages.map((msg, idx) => (
                                        <Answer key={msg.id || idx} message={msg} />
                                    ))}
                                </AnalysisContext.Provider>
                            </div>
                        ) : (
                            <Input.TextArea
                                rows={16}
                                disabled={this.state.loading}
                                value={this.state.editableContent || ''}
                                onChange={(e) => this.setState({ editableContent: e.target.value })}
                                style={{ borderRadius: '8px', margin: '16px 0' }}
                                placeholder="请输入"
                            />
                        )}
                    </div>
                )}

                {mockType === '2' && (
                    <div>
                        {this.renderMockDataCards()}
                        <div
                            className="page-global-body-pagination"
                            style={{
                                display: 'flex',
                                justifyContent: 'space-between',
                                alignItems: 'center',
                                marginTop: '16px',
                                textAlign: 'right'
                            }}>
                            <span className="ml20">{I18N.template(I18N.mockconfig.index.gongTOTA, { val1: total })}</span>
                            <Pagination
                                showSizeChanger
                                showQuickJumper
                                current={searchParams.curPage}
                                pageSize={searchParams.pageSize}
                                total={total}
                                onChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                                onShowSizeChange={(curPage, pageSize) => this.paginationOnChange(curPage, pageSize)}
                            />
                        </div>
                    </div>
                )}

                <AddModifyModal
                    addModifyData={addModifyData}
                    visible={visible}
                    onCancel={this.handleCancel}
                    onRefresh={() => this.search()}
                />
                <ImportModal visible={importVisible} onCancel={this.handleCancel} onRefresh={() => this.search()} />
                <ConfigModal
                    inputConfig={inputConfig}
                    mockField={mockField}
                    visible={configVisible}
                    onCancel={this.handleCancel}
                    onRefresh={() => this.getDetail()}
                />
                <Suspense fallback={null}>
                    <TestModal
                        mockFlag={this.state.testMockFlag}
                        title={this.state.testTitle}
                        isMockTitle={isMockTitle}
                        uuid={this.state.testId}
                        name={this.state.testName}
                        visible={this.state.testVisible}
                        onCancel={this.handleCancel}
                    />
                </Suspense>
                {this.state.aiMockVisible && (
                    <AiDataMockModal visible={this.state.aiMockVisible} onCancel={this.handleAiMockCancel} onOk={this.handleAiMockOk} />
                )}
                {this.state.aiMockSystemVisible && (
                    <AiSystemMockDrawer
                        uuid={this.props.modalType === 1 ? this.props.uuid : getUrlKey('uuid')}
                        inputConfig={inputConfig}
                        visible={this.state.aiMockSystemVisible}
                        onCancel={this.handleAiSystemMockCancel}
                        handleSendMessage={this.handleSystemMockSendMessage}
                        editableContent={this.state.systemMockContent}
                        onContentChange={(val) => this.setState({ systemMockContent: val })}
                        isRunning={this.state.systemMockLoading}
                        onStop={this.onSystemMockStop}
                        onSystemMockOK={this.onSystemMockOK}
                        documentTypeList={documentTypeList}
                        systemMockMessages={this.state.systemMockMessages}
                        systemMockConversationId={this.state.systemMockConversationId}
                    />
                )}
            </div>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global,
    dataServiceListStore: state.dataServiceList
}))(PageContainer(MockConfig));
