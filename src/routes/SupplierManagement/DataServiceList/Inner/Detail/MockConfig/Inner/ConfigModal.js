/*
 * @Author: liubo
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 参数配置
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import { Modal, message, Table } from 'tntd';
import { dataServiceListAPI } from '@/services';
import { getUrlKey } from '@/utils/utils';

class ConfigModal extends PureComponent {
	state = {
		loading: false,
		data: [],
		selectedRowKeys: []
	}

	componentDidUpdate(preProps) {
		const preVisible = preProps.visible;
		const nextVisible = this.props.visible;
		if (preVisible !== nextVisible && nextVisible) {
			const { inputConfig, mockField } = this.props;
			this.setState({
				data: inputConfig,
				selectedRowKeys: mockField ? mockField : []
			});
		}
	}

	afterClose = () => {
		this.setState({
			data: [],
			selectedRowKeys: []
		});
	}

	handleOk = () => {
		const { selectedRowKeys } = this.state;
		const uuid = getUrlKey('uuid');
		const params = {
			uuid,
			mockFields: JSON.stringify(selectedRowKeys)
		};
		this.setState({ loading: true });
		dataServiceListAPI.updateMockConfig(params).then(res => {
			this.setState({ loading: false });
			if (res && res.success) {
				message.success(I18N.inner.configmodal.caoZuoChengGong); // 操作成功
				this.props.onCancel();
				this.props.onRefresh();
			} else {
				message.error(res.message);
			}
		}).catch(err => {
			this.setState({ loading: false });
		});
	}

	getColumns = () => {
		const columns = [
			{
				title: I18N.inner.configmodal.ruCan, // 入参
				dataIndex: 'serviceParam'
			},
			{
				title: I18N.inner.configmodal.miaoShu, // 描述
				dataIndex: 'displayName'
			}
		];
		return columns;
	}

	render() {
		const columns = this.getColumns();
		const { data, selectedRowKeys, loading } = this.state;
		const { visible } = this.props;

		const rowSelection = {
			selectedRowKeys,
			onChange: (selectedRowKeys, selectedRows) => {
				this.setState({
					selectedRowKeys
				});
			}
		};

		return (
			<Modal
				title={I18N.inner.configmodal.mOCKCan} // Mock参数配置
				width={600}
				visible={visible}
				maskClosable={false}
				confirmLoading={loading}
				onOk={this.handleOk}
				onCancel={() => this.props.onCancel()}
				afterClose={this.afterClose}
				className="m-mock-config-dialog"
			>
				<Table
					bordered
					size="middle"
					rowSelection={rowSelection}
					columns={columns}
					dataSource={data}
					rowKey="serviceParam"
				/>
			</Modal>
		);
	}
}

export default connect(state => ({
	globalStore: state.global
}))(ConfigModal);
