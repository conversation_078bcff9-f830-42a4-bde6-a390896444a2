.ai-system-mock-drawer {
    .ant-drawer-header {
        height: 48px;
        background: url('@/sources/images/aiMockDrawerTop.png') no-repeat top center, #E9F0FF !important;
        background-size: 100% auto !important;
        border-top-left-radius: 8px !important;
        border-top-right-radius: 8px !important;
        border-bottom: none !important;
        z-index: 200;
    }
    .ant-drawer-body {
        height: 100%;
        background-color: #E9F0FF;
    }
    .ai-system-mock-drawer-footer {
        display: flex;
        justify-content: flex-end;
        .ai-mock-action-button {
            display: flex;
            align-items: center;
            background: linear-gradient(128deg, #C575FA 6.08%, #487CFF 96.35%);
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            padding: 0 12px;
            .ai-mock-icon {
                width: 20px;
                height: 20px;
                margin-right: 4px;
                vertical-align: middle;
            }
        }
    }
    .ai-system-mock-drawer-content {
        padding: 16px;
        background-color: #fff;
        height: 100%;
        border-radius: 8px;
        .ai-system-mock-drawer-content-item {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin: 16px 0;
        }
        .ant-radio-group {
            display: flex;
            width: 100%;
            .ant-radio-button-wrapper {
                flex: 1;
                text-align: center;
            }
        }
        .ai-mock-input-group {
            display: flex;
            align-items: center;
            margin: 16px 0;

            .ai-mock-input {
                flex: 1;
                border: 1px solid #C575FA;
                border-radius: 8px 0 0 8px;
                padding: 0 12px;
                font-size: 14px;
                transition: border-color 0.2s;
                &:focus {
                    border-color: #487CFF;
                    outline: none;
                }
            }
            .ai-mock-action-button {
                padding: 0 6px;
                background: linear-gradient(128deg, #C575FA 6.08%, #487CFF 96.35%);
                color: #fff;
                border: none;
                border-radius: 0 8px 8px 0;
                display: flex;
                align-items: center;
                cursor: pointer;
                transition: opacity 0.2s, transform 0.1s;
                .ai-mock-icon {
                    width: 20px;
                    height: 20px;
                    margin-right: 4px;
                }
                &:hover {
                    opacity: 0.8;
                }
                &:active {
                    transform: scale(0.95);
                }
            }
        }
    }
    .system-mock-answer-content {
        width: 100%;
        overflow-y: auto;
        height: 230px;
        max-height: 230px;
        border: 1px solid #E1E6EE;
        border-radius: 8px;
        background-color: #fff;
    }
}