/*
 * @Author: liubo
 * @CreatDate: 2019-05-17 11:23:59
 * @Describe: 导入弹框
 */

import I18N from '@/utils/I18N';
import { PureComponent } from 'react';
import { connect } from 'dva';
import fetch from 'dva/fetch';
import { Modal, message, Button, Icon, Upload } from 'tntd';
import { dataServiceListAPI } from '@/services';
import { getUrlKey } from '@/utils/utils';
import { getHeader } from '@/utils/common';

class ImportModal extends PureComponent {
    state = {
        loading: false,
        file: null,
        fileName: null
    };

    afterClose = () => {
        this.setState({
            loading: false,
            file: null,
            fileName: null
        });
    };

    handleOk = () => {
        const uuid = getUrlKey('uuid');
        const { file } = this.state;

        if (!file) return message.warning(I18N.inner.importmodal.qingXuanZeWenJian); // 请选择文件
        const formData = new FormData();
        formData.append('uuid', uuid);
        formData.append('file', file);
        this.setState({ loading: true });
        fetch('/bridgeApi/serviceConfig/importMockData', {
            method: 'POST',
            body: formData,
            headers: getHeader()
        })
            .then((res) => {
                return res.json();
            })
            .then((res) => {
                this.setState({ loading: false });
                if (res && res.success) {
                    message.success(I18N.inner.importmodal.caoZuoChengGong); // 操作成功
                    this.props.onCancel();
                    this.props.onRefresh();
                } else {
                    message.error(res.message);
                }
            })
            .catch((err) => {
                this.setState({ loading: false });
            });
    };

    downLoad = () => {
        const uuid = getUrlKey('uuid');
        const params = {
            uuid
        };
        params['fileType'] = 'xlsx';
        params['fileName'] = I18N.inner.importmodal.muBan; // 模板
        dataServiceListAPI.exportMockDataTemplate(params);
    };

    render() {
        const { fileName, loading } = this.state;
        const { visible } = this.props;

        const uploadProps = {
            showUploadList: false,
            beforeUpload: (file) => {
                this.setState({
                    file,
                    fileName: file.name
                });
                return false;
            }
        };

        return (
            <Modal
                title={I18N.inner.importmodal.daoRuMOC} // 导入mock数据
                visible={visible}
                maskClosable={false}
                confirmLoading={loading}
                onOk={this.handleOk}
                onCancel={() => this.props.onCancel()}
                afterClose={this.afterClose}
                className="m-mock-config-dialog">
                <div className="box">
                    <label>
                        {/* 请选择上传文件 */}
                        {I18N.inner.importmodal.qingXuanZeShangChuan}：
                    </label>
                    <Upload
                        {...uploadProps}
                        // accept=".xlsx,.xls,.csv"
                    >
                        <Button>
                            <Icon type="upload" />
                            {/* 上传文件 */}
                            {I18N.inner.importmodal.shangChuanWenJian}
                        </Button>
                    </Upload>
                    <span className="ml10">{fileName}</span>
                </div>
                <div className="box">
                    <label>
                        {/* 点击下载模板 */}
                        {I18N.inner.importmodal.dianJiXiaZaiMo}：
                    </label>
                    <span className="s1" onClick={this.downLoad}>
                        {/* 下载模板 */}
                        {I18N.inner.importmodal.xiaZaiMuBan}
                    </span>
                </div>
            </Modal>
        );
    }
}

export default connect((state) => ({
    globalStore: state.global
}))(ImportModal);
