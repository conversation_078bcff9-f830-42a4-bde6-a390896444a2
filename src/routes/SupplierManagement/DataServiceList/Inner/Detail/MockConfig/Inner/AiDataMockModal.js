import React, { useState } from 'react';
import { Modal, Input, Button, message, TntdForm as Form } from 'tntd';
import aiGenerated from '@/sources/images/common/generated.svg';
import './AiDataMockModal.less';

const { TextArea } = Input;

const AiDataMockModal = ({ visible, onCancel, onOk }) => {
    const [form] = Form.useForm();
    const handleOk = () => {
        form.validateFields().then((values) => {
            if (!values?.content) {
                message.warning('请输入清晰具体的报文要求描述');
                return;
            }
            onOk(values?.content);
        });
    };

    return (
        <Modal
            title="AI生成报文"
            visible={visible}
            onCancel={onCancel}
            footer={null}
            closable={false}
            maskClosable={true}
            className="ai-data-mock-modal"
            width={360}>
            <div className="ai-data-mock-modal-content">
                <Form form={form} layout="vertical">
                    <Form.Item name="content" label="指令">
                        <TextArea rows={5} placeholder="请输入清晰具体的报文要求描述" />
                    </Form.Item>
                </Form>
                <Button type="primary" className="ai-mock-btn" onClick={handleOk} block>
                    <img src={aiGenerated} alt="AI生成" className="ai-mock-icon" />
                    开始生成
                </Button>
            </div>
        </Modal>
    );
};

export default AiDataMockModal;
