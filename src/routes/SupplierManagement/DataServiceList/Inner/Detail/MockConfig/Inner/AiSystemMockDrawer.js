import { Drawer, Title, Tag, Radio, Select, Button, message, TntdForm as Form, InputNumber, Input } from 'tntd';
import './AiSystemMockDrawer.less';
const { TextArea } = Input;
import { useState, useEffect } from 'react';
import aiGenerated from '@/sources/images/common/generated.svg';
import aiStop from '@/sources/images/common/aiStop.svg';
import service from '@/services/captain';
import Answer from '../Answer';
import { AnalysisContext } from '../../utils';
const { Option } = Select;
const AiSystemMockDrawer = ({
    uuid,
    inputConfig = [],
    visible,
    onCancel,
    handleSendMessage,
    editableContent,
    onContentChange,
    isRunning,
    onStop,
    onSystemMockOK,
    documentTypeList = [],
    systemMockMessages = [],
    systemMockConversationId = ''
}) => {
    console.log('documentTypeList',documentTypeList)
    const [radioValue, setRadioValue] = useState('zdybw');
    const [form] = Form.useForm();
    const [inputValue, setInputValue] = useState('');
    const [documentTypeData, setDocumentTypeData] = useState(null);

    // 监听 editableContent 变化，同步到 Form
    useEffect(() => {
        if (editableContent && radioValue === 'zdybw') {
            form.setFieldsValue({
                response: editableContent
            });
        }
    }, [editableContent, form, radioValue]);
    const handleRadioChange = (e) => {
        setRadioValue(e.target.value);
    };
    const onAiGengerate = () => {
        const fields = inputConfig?.map((item) => item?.field);
        const type = radioValue === 'zdybw' ? 0 : 1;
        form.validateFields().then((values) => {
            let sampleResponse = documentTypeData?.sample || '';
            const amount = values?.amount;
            const params = {
                uuid,
                response: type === 0 ? values?.response : sampleResponse,
                amount,
                fieldList: fields?.join(','),
                type
            };
            onSystemMockOK(params);
        });
    };
    const handleInputValue = (e) => {
        setInputValue(e.target.value);
    };
    const onGetAiData = async () => {
        if (!inputValue) {
            message.warning('请输入需要生成的报文描述');
            return;
        }
        // handleSendMessage 是异步流式处理，不会直接返回结果
        // 结果会通过 editableContent 的变化来体现
        handleSendMessage(inputValue);
    };
    const handleDocumentTypeChange = async (uuid) => {
        if (uuid) {
            try {
                const response = await service.getDocumentTypeGet({ uuid });
                setDocumentTypeData(response?.data);
            } catch (error) {
                console.error('获取文档类型数据失败:', error);
            }
        } else {
            setDocumentTypeData('');
        }
    };
    return (
        <Drawer
            className="ai-system-mock-drawer"
            title="AI生成"
            placement="right"
            onClose={onCancel}
            visible={visible}
            showFooter={true}
            footer={
                <div className="ai-system-mock-drawer-footer">
                    <Button onClick={onCancel}>取消</Button>
                    <Button className="ai-mock-action-button" style={{ color: '#fff' }} onClick={onAiGengerate}>
                        <img src={aiGenerated} alt="AI" className="ai-mock-icon" />
                        生成
                    </Button>
                </div>
            }>
            <div className="ai-system-mock-drawer-content">
                <Title title="匹配字段" />
                <div className="ai-system-mock-drawer-content-item">
                    {inputConfig.map((item) => (
                        <Tag key={item.field} border={false}>
                            {item.field}
                        </Tag>
                    ))}
                </div>
                <Title title="生成配置" />
                <Form form={form} layout="vertical">
                    <Form.Item
                        style={{ marginTop: '16px' }}
                        label="数据数量"
                        name="amount"
                        initialValue={3}
                        required
                        rules={[{ required: true, message: '请输入生成数量' }]}>
                        <InputNumber style={{ width: '100%' }} min={1} max={10} />
                    </Form.Item>
                    <Form.Item
                        label={
                            <>
                                <span style={{ color: 'red', marginRight: 4 }}>*</span>样例报文
                            </>
                        }>
                        <div>
                            <Radio.Group onChange={handleRadioChange} defaultValue="zdybw">
                                <Radio.Button value="zdybw">自定义报文</Radio.Button>
                                <Radio.Button value="nzbw">内置报文</Radio.Button>
                            </Radio.Group>
                            {radioValue === 'zdybw' && (
                                <div>
                                    <div className="ai-mock-input-group">
                                        <Input
                                            className="ai-mock-input"
                                            value={inputValue}
                                            onChange={handleInputValue}
                                            placeholder="请输入需要生成的报文描述"
                                        />
                                        {isRunning ? (
                                            <Button className="ai-mock-action-button stop-button" onClick={onStop}>
                                                <img src={aiStop} className="ai-mock-icon" />
                                            </Button>
                                        ) : (
                                            <Button className="ai-mock-action-button" onClick={onGetAiData}>
                                                <img src={aiGenerated} className="ai-mock-icon" />
                                            </Button>
                                        )}
                                    </div>
                                    <Form.Item
                                        name="response"
                                        rules={[
                                            {
                                                required: true,
                                                message: '请输入报文内容'
                                            }
                                        ]}>
                                        {isRunning ? (
                                            <div className="system-mock-answer-content">
                                                <AnalysisContext.Provider
                                                    value={{
                                                        handleSendMessage: handleSendMessage,
                                                        setMessageId: () => {},
                                                        isRunning: isRunning,
                                                        conversationId: systemMockConversationId
                                                    }}>
                                                    {systemMockMessages.map((msg, idx) => (
                                                        <Answer key={msg.id || idx} message={msg} />
                                                    ))}
                                                </AnalysisContext.Provider>
                                            </div>
                                        ) : (
                                            <TextArea
                                                disabled={isRunning}
                                                rows={10}
                                                placeholder="请输入报文或使用ai生成"
                                                value={editableContent}
                                                onChange={(e) => {
                                                    onContentChange(e.target.value);
                                                    form.setFieldsValue({ response: e.target.value });
                                                }}
                                            />
                                        )}
                                    </Form.Item>
                                </div>
                            )}
                            {radioValue === 'nzbw' && (
                                <Form.Item
                                    name="responseUuid"
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择报文'
                                        }
                                    ]}>
                                    <Select style={{ marginTop: 16 }} placeholder="请选择报文" onChange={handleDocumentTypeChange}>
                                        {documentTypeList?.map((v) => (
                                            <Option key={v?.uuid} value={v?.uuid}>
                                                {v?.displayName}
                                            </Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            )}
                        </div>
                    </Form.Item>
                </Form>
            </div>
        </Drawer>
    );
};

export default AiSystemMockDrawer;
