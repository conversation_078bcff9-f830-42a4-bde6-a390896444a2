import I18N from '@/utils/I18N';
import {
    useMemo,
    useState,
    useCallback,
    useRef,
    memo,
    Component,
    forwardRef,
    useImperativeHandle,
    useEffect,
    useLayoutEffect,
    useContext
} from 'react';
import Markdown from 'react-markdown';
import RemarkGfm from 'remark-gfm';
import RehypeRaw from 'rehype-raw';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { atelierHeathLight } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { Spin, Icon, Table, Tooltip } from 'tntd';
import cn from 'classnames';
import { uniqueId } from 'lodash';
import { ring2 } from 'ldrs';
import { style } from './utils';
import './index.less';
import './markdown.less';
import { AnalysisContext } from '../../utils';
import Viewer from 'react-viewer';

ring2.register();

class ErrorBoundary extends Component {
    constructor(props) {
        super(props);
        this.state = { hasError: false };
    }

    componentDidCatch(error, errorInfo) {
        this.setState({ hasError: true });
        console.error(error, errorInfo);
    }

    render() {
        if (this.state.hasError) {
            return (
                <div>
                    Oops! An error occurred. This could be due to an ECharts runtime error or invalid SVG content. <br />
                    (see the browser console for more information)
                </div>
            );
        }
        return this.props.children;
    }
}

const CodeBlock = memo(({ inline, className, children, ...props }) => {
    const match = /language-(\w+)/.exec(className || '');
    const language = match?.[1];
    // const languageShowName = getCorrectCapitalizationLanguageName(language || '');
    const chartData = useMemo(() => {
        if (language === 'echarts') {
            try {
                return JSON.parse(String(children).replace(/\n$/, ''));
            } catch (error) {
                console.log(children);
            }
        }
        return false;
    }, [language, children]);

    const renderCodeContent = useMemo(() => {
        const content = String(children).replace(/\n$/, '');
        return (
            <SyntaxHighlighter
                {...props}
                style={atelierHeathLight}
                customStyle={{
                    paddingLeft: 12,
                    backgroundColor: '#fff'
                }}
                language={match?.[1]}
                showLineNumbers
                PreTag="div">
                {content}
            </SyntaxHighlighter>
        );
    }, [language, match, props, children, chartData]);

    if (inline || !match) {
        return (
            <code {...props} className={className}>
                {children}
            </code>
        );
    }
    return renderCodeContent;
});

function MarkdownComp({ item, scroll, autoScroll }, ref) {
    const context = useContext(AnalysisContext) || {};
    const { handleSendMessage, setMessageId, isRunning, conversationId } = context;
    const [domId] = useState(uniqueId('m_'));
    const [close, setClose] = useState(false);
    const [visible, setVisible] = useState(false);
    const [path, setPath] = useState('');
    // const [hasCollapsedOnce, setHasCollapsedOnce] = useState(false);
    const scrollDom = useRef();
    const shouldAutoScroll = useRef(true);
    const effectExecuted = useRef(false);

    const thinkRef = useRef();
    const { md: _md, content: _content, startTime, endTime, isLoading, isStop } = item || {};
    const md = _md || _content;
    const { think, content } = useMemo(() => {
        let str = (md || '').trim();
        if (str.includes('<think>')) {
            if (str.includes('</think>')) {
                const [str1, str2] = str.split('</think>');
                return {
                    think: str1.replace('<think>', ''),
                    content: str2
                };
            }
            return {
                think: str.replace('<think>', '')
            };
        }
        return { content: str };
    }, [md]);
    const isEnd = useMemo(() => {
        return (startTime && endTime) || isStop;
    }, [startTime, endTime, isStop]);

    useEffect(() => {
        if (isEnd) {
            setClose(true);
        }
    }, [isEnd]);
    useImperativeHandle(ref, () => ({
        download: (fileName) => {
            // 转换
            const rootDom = document.getElementById(domId);
            const doms = rootDom.getElementsByTagName('canvas');
            for (let i = 0; i < doms.length; i++) {
                const dom = doms[i];
                const imageData = dom.toDataURL('image/png');
                const p = document.createElement('p');
                const img = document.createElement('img');
                img.src = imageData;
                p.append(img);
                dom.parentNode.parentNode.parentNode.parentNode.insertBefore(p, dom.parentNode.parentNode.parentNode);
                dom.parentNode.parentNode.parentNode.remove();
            }
            let html = rootDom.innerHTML;
            html = html.replaceAll('<img ', '<img width="600"');
            let header =
                '<html xmlns:o="urn:schemas-microsoft-com:office:office" ' +
                'xmlns:w="urn:schemas-microsoft-com:office:word" ' +
                'xmlns="http://www.w3.org/TR/REC-html40">' +
                '<head><meta charset="utf-8"><title>Export HTML to Word Document with JavaScript</title></head><body>';
            let footer = '</body></html>';
            let sourceHTML = header + style + html + footer;

            let source = 'data:application/vnd.ms-word;charset=utf-8,' + encodeURIComponent(sourceHTML);
            let fileDownload = document.createElement('a');
            document.body.appendChild(fileDownload);
            fileDownload.href = source;
            fileDownload.download = fileName;
            fileDownload.click();
            document.body.removeChild(fileDownload);
        }
    }));

    const handleScroll = useCallback((e) => {
        const { scrollTop, scrollHeight, clientHeight } = e.target;
        if (scrollTop + clientHeight >= scrollHeight - 20) {
            shouldAutoScroll.current = true;
        } else {
            shouldAutoScroll.current = false;
        }
    }, []);

    useLayoutEffect(() => {
        if (autoScroll && scrollDom.current && shouldAutoScroll.current) {
            scrollDom.current.scrollTo({
                top: scrollDom.current.scrollHeight,
                behavior: 'smooth'
            });
        }
    }, [md, autoScroll]);

    useLayoutEffect(() => {
        if (autoScroll && thinkRef.current && shouldAutoScroll.current && think && !close) {
            thinkRef.current.scrollTo({
                top: thinkRef.current.scrollHeight,
                behavior: 'smooth'
            });
        }
    }, [think, close, autoScroll]);

    useEffect(() => {
        if (content && !close && !effectExecuted.current) {
            setClose(true);
            effectExecuted.current = true;
        }
    }, [content, close]);

    return (
        <>
            <div
                className={cn('resultMarkdown', {
                    resultMarkdownLoading: startTime && !endTime,
                    resultMarkdownScroll: scroll
                })}
                onScroll={isEnd || !autoScroll ? undefined : handleScroll}
                ref={scrollDom}>
                <div className="resultMarkdownLoadingInner">
                    {isStop && (
                        <div className="resultMarkdownLoadingInner">
                            <span style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                {isEnd ? (
                                    <span>
                                        <img
                                            src={require('../icons/think.svg')}
                                            alt="loading"
                                            style={{ width: '16px', height: '16px', marginRight: '8px' }}
                                        />
                                        <span style={{ color: '#8B919E', fontWeight: 600 }}>{I18N.markdownview.index.yiTingZhi}</span>
                                    </span>
                                ) : (
                                    <div />
                                )}
                                {!isStop && (
                                    <Icon
                                        style={{
                                            cursor: 'pointer',
                                            position: 'absolute',
                                            right: '0px',
                                            top: '0px'
                                        }}
                                        type={!close ? 'up' : 'down'}
                                        onClick={() => {
                                            setClose(!close);
                                        }}
                                    />
                                )}
                            </span>
                        </div>
                    )}
                    {think && !isStop && (
                        <div className="resultMarkdownLoadingInner">
                            <span style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                {isEnd ? (
                                    <span>
                                        <img
                                            src={require('../icons/think.svg')}
                                            alt="loading"
                                            style={{ width: '16px', height: '16px', marginRight: '8px' }}
                                        />
                                        <span style={{ color: '#8B919E', fontWeight: 600 }}>
                                            {!endTime
                                                ? I18N.markdownview.index.yiTingZhi
                                                : (() => {
                                                      const seconds = parseInt((endTime - startTime) / 1000, 10);
                                                      return seconds === 0 || isNaN(seconds)
                                                          ? I18N.markdownview.index.yiShenDuSiKao2
                                                          : I18N.template(I18N.markdownview.index.yiShenDuSiKao, { val1: seconds });
                                                  })()}
                                        </span>
                                    </span>
                                ) : (
                                    <div />
                                )}

                                <Icon
                                    style={{
                                        cursor: 'pointer',
                                        position: 'absolute',
                                        right: '0px',
                                        top: '0px'
                                    }}
                                    type={!close ? 'up' : 'down'}
                                    onClick={() => {
                                        setClose(!close);
                                    }}
                                />
                            </span>
                        </div>
                    )}
                    {startTime && !endTime && !isStop && !isStop && (
                        <div className="loadingTip" style={{ marginBottom: !think && content ? 16 : 0 }}>
                            <span>
                                <l-ring-2 size="16" stroke="2" stroke-length="0.25" bg-opacity="0.1" speed="0.8" color="#126BFB" />
                            </span>
                            <span
                                style={{
                                    fontFamily: 'PingFang SC',
                                    fontSize: '14px',
                                    fontStyle: 'normal',
                                    fontWeight: 400,
                                    lineHeight: '22px',
                                    top: '0px'
                                }}>
                                {'正在为您生成报文...'}
                            </span>
                        </div>
                    )}
                    <div className="markdownRoot">
                        {think && !close && (
                            <div
                                className={cn('markdownThinkBox', 'markdown-body', { 'is-think': !isEnd })}
                                ref={thinkRef}
                                style={{ maxHeight: !isEnd ? '120px' : 'none' }}>
                                <Markdown>{think}</Markdown>
                            </div>
                        )}
                        {content && (
                            <div
                                className={cn('markdownContentBox', 'markdown-body')}
                                id={domId}
                                style={{ marginTop: !think && !isStop && '0px' }}>
                                <Markdown
                                    className="markdownContent"
                                    rehypePlugins={[RehypeRaw]}
                                    remarkPlugins={[RemarkGfm]}
                                    components={{
                                        code: CodeBlock,
                                        a: ({ node, ...props }) => {
                                            return (
                                                <a
                                                    onClick={() => {
                                                        window.open(props.href, '_blank');
                                                    }}
                                                    {...props}
                                                    href="#"
                                                    rel="noopener noreferrer"
                                                />
                                            );
                                        },
                                        table: ({ node, ...props }) => {
                                            try {
                                                // 解析表格数据
                                                const rows = [];
                                                let headers = [];

                                                // 获取表头
                                                if (node.children && node.children[0]?.tagName === 'thead') {
                                                    const headerRow = node.children[0].children[0];
                                                    if (headerRow && headerRow.children) {
                                                        headers = headerRow.children.map((th) => {
                                                            const text = th.children[0]?.value || '';
                                                            return { title: text, dataIndex: text, key: text };
                                                        });
                                                    }
                                                }

                                                // 获取表格内容
                                                if (node.children && node.children[1]?.tagName === 'tbody') {
                                                    node.children[1].children.forEach((tr, rowIndex) => {
                                                        const rowData = { key: rowIndex };
                                                        tr.children.forEach((td, colIndex) => {
                                                            const columnKey = headers[colIndex]?.dataIndex || `col${colIndex}`;
                                                            rowData[columnKey] = td.children[0]?.value || '';
                                                        });
                                                        rows.push(rowData);
                                                    });
                                                }
                                                // 添加操作列
                                                if (headers.length > 0) {
                                                    headers.push({
                                                        title: I18N.markdownview.index.caoZuo,
                                                        dataIndex: 'operation',
                                                        key: 'operation',
                                                        width: 40,
                                                        render: (text, record) => (
                                                            <div
                                                                style={{
                                                                    fontSize: '16px',
                                                                    display: 'flex',
                                                                    justifyContent: 'center',
                                                                    alignItems: 'center',
                                                                    cursor: 'pointer'
                                                                }}
                                                                onClick={() => {
                                                                    handleSendMessage(
                                                                        record?.[I18N.markdownview.index.ceLueNeiRong],
                                                                        'ruleGenerate'
                                                                    );
                                                                }}>
                                                                <Tooltip title={I18N.markdownview.index.guiZeShengCheng}>
                                                                    <Icon type="rulemanage" />
                                                                </Tooltip>
                                                            </div>
                                                        )
                                                    });

                                                    // 为每行数据添加操作列的值
                                                    rows.forEach((row) => {
                                                        row.operation = I18N.markdownview.index.guiZeShengCheng;
                                                    });
                                                }
                                                return (
                                                    <div className="antd-table-wrapper" style={{ padding: '5px' }}>
                                                        <Table
                                                            dataSource={rows}
                                                            columns={headers}
                                                            pagination={false}
                                                            bordered={true}
                                                            showCellBorders
                                                        />
                                                    </div>
                                                );
                                            } catch (error) {
                                                console.error('Error rendering table:', error);
                                                return <table {...props} />;
                                            }
                                        },
                                        img: ({ node, ...props }) => {
                                            return (
                                                <div
                                                    className="markdown-img-container"
                                                    style={{
                                                        position: 'relative',
                                                        display: 'inline-block'
                                                    }}>
                                                    <img className="markdown-img" src={props.src} />
                                                    {isEnd && (
                                                        <div
                                                            className="markdown-img-overlay"
                                                            style={{
                                                                position: 'absolute',
                                                                top: 0,
                                                                left: 0,
                                                                width: '100%',
                                                                height: '100%',
                                                                backgroundColor: 'rgba(240, 240, 240, 0.7)',
                                                                background:
                                                                    'linear-gradient(0deg, rgba(0, 0, 0, 0.40) 0%, rgba(0, 0, 0, 0.40) 100%), lightgray 50% / cover no-repeat',
                                                                display: 'flex',
                                                                justifyContent: 'center',
                                                                alignItems: 'center',
                                                                opacity: 0,
                                                                transition: 'opacity 0.3s',
                                                                cursor: 'pointer'
                                                            }}
                                                            onClick={() => {
                                                                setTimeout(() => {
                                                                    setVisible(true);
                                                                }, 300);
                                                            }}
                                                            onMouseEnter={(e) => {
                                                                setPath(props?.src);
                                                            }}>
                                                            <Icon type="eye" style={{ color: 'white', fontSize: '24px' }} />
                                                            <span style={{ fontWeight: 'bold', color: 'white', marginLeft: '8px' }}>
                                                                {I18N.markdownview.index.yuLan}
                                                            </span>
                                                        </div>
                                                    )}
                                                </div>
                                            );
                                        }
                                    }}>
                                    {content}
                                </Markdown>
                            </div>
                        )}
                    </div>
                </div>
            </div>

            <Viewer
                // activeIndex={10000}
                zIndex={10000}
                visible={visible}
                onClose={() => {
                    setVisible(false);
                }}
                zoomable
                rotatable
                downloadable={false}
                showTotal={false}
                noNavbar
                attribute={false}
                noImgDetails={false}
                changeable={false}
                disableImgReset
                scalable={false}
                images={[
                    {
                        src: path,
                        alt: ''
                    }
                ]}
            />
        </>
    );
}

export default forwardRef(MarkdownComp);
