.resultMarkdown {
    td {
        min-width: 60px;
    }
    .markdown-img-overlay {
        opacity: 0;
        &:hover {
            opacity: 0.6 !important;
        }
    }
    &Scroll {
        border-radius: 8px;
        overflow: auto;
    }

    &Tools {
        position: absolute;
        right: 0;
        top: 0;
    }

    .loadingTip {
        font-size: 12px;
        font-weight: 600;
        color: #8b919e;
        margin-bottom: 16px;
        span {
            position: relative;
            top: 3px;
            margin-right: 8px;
        }
    }

    :global(.ant-tag) {
        font-weight: 600;
        color: @text-color-secondary;
    }

    .markdownThinkBox {
        color: #8b919e;
        border-left: 1px solid #e1e6ee;
        padding-left: 14px;
        margin: 20px 4px 8px 8px;
        font-size: 14px;
        line-height: 22px;
        overflow-y: auto;
        position: relative; // Needed for pseudo-elements if used, or use mask
    }
    .is-think {
        // Using mask-image for the blur effect
        mask-image: linear-gradient(
            to bottom,
            transparent 0%,
            black 16px,
            // Start fading in
            black 100%
        );
    }

    .markdownContentBox {
        line-height: 24px;
        margin-top: 24px;
        .markdownContent > pre > div {
            padding: 0px !important;
        }
    }
    .resultMarkdownLoadingInner {
        // margin-bottom: 20px;
        position: relative;
    }
    .resultMarkdownLoadingInner > div:last-child {
        margin-bottom: 0 !important;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
    }
}
