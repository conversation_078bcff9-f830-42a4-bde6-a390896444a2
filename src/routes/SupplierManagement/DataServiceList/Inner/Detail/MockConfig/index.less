.g-mock-config {
    width: 100%;
    height: 100%;
    background-color: #fff;
    padding: 16px;
    border-radius: 16px;

    .bread-container {
        line-height: 40px;

        .u-link {
            .tnt-current-v3 & {
                background-color: transparent;
                color: #8b919e;

                .anticon-left {
                    color: #8b919e;
                }
            }

            cursor: pointer;

            &:hover {
                color: #126BFB;
            }
        }
    }

    .u-ellipsis {
        display: inline-block;
        max-width: 300px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
    }

    .u-tip {
        font-size: 16px;
        vertical-align: middle;
        margin-left: 10px;
        margin-top: -2px;
        cursor: pointer;
        color: #454F64;
    }

    // Tabs styling
    .tabs-container {
        height: 40px;
        display: flex;
        gap: 24px;
        border-bottom: 2px solid #E1E6EE;
        margin-bottom: 16px;

        .tab-item {
            height: 40px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;

            .tab-content {
                height: 38px;
                padding: 1px 12px 0 12px;
                display: flex;
                align-items: center;

                .tab-text {
                    font-size: 14px;
                    line-height: 22px;
                    color: #17233D;
                    font-weight: normal;
                }
            }

            .tab-indicator {
                width: 100%;
                height: 1px;
                border-radius: 2px;
            }

            &.active {
                .tab-content {
                    padding: 2px 12px 0 12px;

                    .tab-text {
                        color: #126BFB;
                        font-weight: 600;
                    }
                }

                .tab-indicator {
                    height: 2px;
                    background-color: #126BFB;
                }
            }
        }
    }

    // Mock Config Controls
    .mock-config-controls {
        width: 100%;
        height: 32px;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 16px;
        margin-bottom: 16px;

        .switch-container {
            display: flex;
            align-items: center;
            gap: 8px;

            .switch-label {
                font-size: 14px;
                line-height: 22px;
                font-weight: 600;
                color: #17233D;
            }
        }

        .select-container {
            width: 160px;
            border-radius: 8px;
        }
    }

    // Search controls
    .search-controls {
        width: 100%;
        height: 32px;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        margin-bottom: 16px;

        .search-form {
            display: flex;
            flex-direction: row;
            align-items: center;

            .ant-select-selection {
                border-radius: 8px 0 0 8px;
            }

            .field-select {
                width: 120px;
                border-radius: 8px 0 0 8px;
                background-color: #F8F9FB;
            }

            .field-input {
                width: 280px;
                border-radius: 0 8px 8px 0;
            }
        }


    }

    .mock-data-answer-container {
        display: flex;
        flex-direction: column;
        align-items: flex-end;

        .mock-data-answer-content {
            width: 100%;
            min-height: 360px;
            height: 100%;
            border: 1px solid #E1E6EE;
            border-radius: 8px;
            margin-top: 16px;
        }

    }

    .action-buttons {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 8px;

        .action-button {
            height: 32px;
            border-radius: 8px;
        }

        .ai-mock-action-button {
            display: flex;
            align-items: center;
            background: linear-gradient(128deg, #C575FA 6.08%, #487CFF 96.35%);
            color: #fff;
            font-weight: 400;
            font-size: 14px;
            padding: 0 12px;

            .ai-mock-icon {
                width: 20px;
                height: 20px;
                margin-right: 4px;
                vertical-align: middle;
            }
        }
    }

    // Mock data display
    .mock-data-container {
        display: flex;
        gap: 16px;
        flex-wrap: wrap;

        .mock-data-card {
            width: 32%;
            padding: 16px;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.05);
            display: flex;
            flex-direction: column;
            position: relative;
            transition: all 0.2s;

            &:hover {
                box-shadow: 0px 0px 25px 0px rgba(0, 0, 0, 0.1);

                .card-action-buttons {
                    opacity: 1;
                    visibility: visible;
                }
            }

            .card-header {
                display: flex;
                flex-direction: column;
                gap: 8px;

                .header-row {
                    display: flex;
                    align-items: center;

                    .field-label {
                        margin-right: 8px;
                        color: #8b919e;
                        font-size: 14px;
                        line-height: 22px;
                    }

                    .field-value {
                        color: #17233D;
                        font-size: 14px;
                        line-height: 22px;
                    }
                }
            }

            .card-content {
                width: 100%;
                height: 160px;
                padding: 12px;
                background-color: #F8F9FB;
                border-radius: 8px;
                overflow: auto;
                margin-top: 16px;

                pre {
                    margin: 0;
                    font-size: 14px;
                    line-height: 22px;
                    color: #17233D;
                    white-space: pre-wrap;
                }
            }

            .card-action-buttons {
                position: absolute;
                top: 16px;
                right: 16px;
                display: flex;
                gap: 8px;
                opacity: 0;
                visibility: hidden;
                transition: all 0.2s;

                .action-button {
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;

                    i {
                        font-size: 16px;
                        color: #17233D;

                        &:hover {
                            color: #126BFB;
                        }
                    }
                }
            }
        }

        .mock-data-loading-card {
            display: flex;
            height: 260px;
            align-items: center;
            justify-content: center;
            border-radius: 8px;
            background: #fff;
            box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.05);
            padding: 16px;

            span {
                color: #8B919E;
                font-size: 12px;
                line-height: 20px;
            }
        }
    }
}

// Dialog styles
.m-mock-config-dialog {
    .title {
        border-left: #f90 solid 4px;
        padding-left: 10px;
        margin-bottom: 10px;

        b {
            color: red;
            vertical-align: middle;
            margin-right: 2px;
        }
    }

    .m-ul {
        margin: 0;
        padding: 0px 15px 15px 15px;

        li {
            list-style: none;
            margin-bottom: 10px;

            .u-label {
                display: block;
            }

            .u-width {
                width: 100%;
            }
        }
    }

    .u-textarea {
        padding: 0 15px;
    }

    .box {
        padding: 10px;

        label {
            display: inline-block;
            width: 150px;
            text-align: right;
        }

        .s1 {
            cursor: pointer;
            color: #126BFB;
        }
    }
}