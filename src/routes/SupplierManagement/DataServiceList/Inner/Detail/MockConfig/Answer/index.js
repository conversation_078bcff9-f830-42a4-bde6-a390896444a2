import React, { useContext, useMemo, useState, useRef, useEffect, useLayoutEffect } from 'react';
import { Icon, Tooltip, Timeline, Button, Tag, Modal, Select, Checkbox, message as Message } from 'tntd';
import MarkdownView from '../MarkdownView';
import { ping } from 'ldrs';
import { AnalysisContext } from '../../utils';

import './index.less';

ping.register();

const Answer = ({ message }) => {
    const { handleSendMessage, setMessageId, isRunning, conversationId } = useContext(AnalysisContext);
    const { startTime, endTime, id, title, md, isError, isStop, scene, suggestedQuestions } = message?.data || {};
    const isEnd = (startTime && endTime) || isStop;
    const [collapsed, setCollapsed] = useState(false);
    const [showCollapseButton, setShowCollapseButton] = useState(false);
    const contentRef = useRef(null);
    const answerBoxRef = useRef(null);

    const toggleCollapse = () => {
        setCollapsed(!collapsed);

        // setTimeout(() => {
        //     handleScroll();
        // }, 300);
    };

    //对话完了自动到底部
    useLayoutEffect(() => {
        const drawerWrapperBody = document.querySelector('.mock-data-answer-container');
        if (drawerWrapperBody && isEnd && conversationId) {
            drawerWrapperBody.scrollTo({
                top: drawerWrapperBody.scrollHeight,
                behavior: 'smooth'
            });
        }
    }, [isEnd]);
    useEffect(() => {
        if (!answerBoxRef.current) return;

        const checkHeight = () => {
            const height = answerBoxRef.current.clientHeight;
            setShowCollapseButton(height > 200);
        };

        // 初始检查高度
        checkHeight();
    }, [message?.data]);

    // 使用React事件处理鼠标进入和离开
    const handleMouseEnter = () => {
        if (answerBoxRef.current) {
            const height = answerBoxRef.current.clientHeight;
            setShowCollapseButton(height > 200);
        }
    };

    const handleMouseLeave = () => {
        setShowCollapseButton(false);
    };

    return (
        <div
            className="answerContainer"
            style={{ paddingRight: '37px' }}
            ref={contentRef}
            onMouseEnter={handleMouseEnter}
            onMouseLeave={handleMouseLeave}>
            <div className="answerBox" ref={answerBoxRef}>
                <div
                    style={{ display: showCollapseButton ? 'block' : 'none' }}
                    // style={{ display: 'block' }}
                    className="collapseButton"
                    onClick={(e) => {
                        toggleCollapse();
                    }}>
                    <Tooltip title={collapsed ? '展开' : '收起'}>
                        <Icon type={collapsed ? 'down' : 'up'} />
                    </Tooltip>
                </div>
                <div className={`answerContent answer ${collapsed ? 'collapsed' : ''}`}>
                    <MarkdownView item={message?.data} scroll autoScroll />
                </div>
            </div>
        </div>
    );
};

export default Answer;
