.rule-popover {
    .ant-lightbox-popover-arrow {
        display: none;
    }
    .ant-lightbox-popover-title {
        padding: 18px 16px 2px 16px;
        border-bottom: none;
        font-weight: 600;
    }
    .ant-lightbox-popover-inner-content {
        padding: 12px 16px 12px 16px !important;
    }
    .ant-lightbox-form-item {
        margin-bottom: 12px !important;
    }
    .popover-footer {
        display: flex;
        justify-content: flex-end;
        padding: 0px;
        button {
            height: 28px;
            margin-left: 8px;
            box-shadow: 0px 2px 6px 0px #5f7bff4f;
            border: none;
        }
        button:last-child {
            background: linear-gradient(
                127.87deg,
                #c575fa 6.08%,
                #487cff 96.35%
            );
        }
    }
}
.metric-popover {
    .ant-lightbox-popover-inner-content {
        padding: 18px 16px 12px 16px !important;
    }
    .ant-lightbox-form-item {
        margin-bottom: 12px !important;
    }
    .ant-lightbox-popover-arrow {
        display: none;
    }
    .ant-lightbox-popover-title {
        padding: 18px 16px 2px 16px;
        border-bottom: none;
        font-weight: 600;
    }
    .popover-footer {
        display: flex;
        justify-content: flex-end;
        padding: 0px;
        button {
            height: 28px;
            margin-left: 8px;
            box-shadow: 0px 2px 6px 0px #5f7bff4f;
            border: none;
        }
        button:last-child {
            background: linear-gradient(
                127.87deg,
                #c575fa 6.08%,
                #487cff 96.35%
            );
        }
    }
}
.answerContainer {
    width: 100%;
    .suggested-questions {
        margin-top: 24px;
        .suggested-questions-list {
            display: flex;
            flex-direction: column;
            .suggested-question-item {
                display: block;
                width: fit-content;
                max-width: 100%;
                padding: 5px 16px;
                border-radius: @border-radius-base;
                background: var(---Button-Grey-Default, #f8f9fb);
                margin-right: 0;
                margin-bottom: 12px;
                white-space: normal;
                cursor: pointer;

                &:hover {
                    background: #eef1f5;
                }

                &:last-child {
                    margin-bottom: 0px !important;
                }
            }
        }
    }
    display: flex;

    .avatar {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 16px;

        .avatarBox {
            width: 36px;
            height: 28px;
            border-radius: 4px;
            // background-image: url('../../images/big.png');
            background-size: cover;
            background-position: center;
            animation: avatarBounce 1s ease-in-out infinite alternate;
        }

        .avatarStatus {
            width: 14px;
            height: 6px;
            background-color: #86a2ff;
            border-radius: 50%;
            margin-top: 4px;
            filter: blur(5px);
            transform: scaleY(0.4);
        }
    }

    .answerBox {
        border-radius: @border-radius-base;
        // margin-left: 8px;
        flex: 1;
        min-width: 0;
        position: relative;
        background-color: #ffffff;
        padding: 12px 16px;
        // border-radius: 2px 8px 8px 8px;

        .collapseButton {
            &:hover {
                display: block !important;
            }
            .tntd-anticon {
                float: right;
            }
            width: 35px;
            height: 35px;
            position: absolute;
            right: -24px;
            top: -3px;
            cursor: pointer;
            z-index: 30;

            .ant-lightbox-icon {
                color: #8b919e;
                font-size: 16px;

                &:hover {
                    color: #3e4759;
                }
            }
        }

        &Tools {
            position: absolute;
            right: 10px;
            top: 10px;
        }

        &Title {
            display: flex;
            align-items: center;
            position: relative;
            top: -4px;

            h3 {
                flex: 1;
                font-size: 14px;
                font-weight: 400;
                line-height: 32px;
                margin-bottom: 0;
            }
        }

        :global {
            .ant-timeline {
                .ant-timeline-item {
                    .ant-timeline-item-tail {
                        border-left: 1px dashed #126bfb;
                    }
                }

                .ant-timeline-item-last {
                    padding-bottom: 0;
                }
            }
        }

        .answerContent {
            color: #17233d;
            line-height: 1.6;
            transition: max-height 0.3s ease-in-out; // 双向过渡动画，展开和收起都有动画效果
            overflow: hidden; // 确保动画时内容不溢出

            &.collapsed {
                max-height: 140px;
                position: relative;
                transition: max-height 0.2s ease-out;
                mask-image: linear-gradient(
                    to top,
                    transparent 0%,
                    black 24px,
                    black 100%
                );

                &::after {
                    content: "";
                    position: absolute;
                    bottom: 0;
                    left: 0;
                    width: 100%;
                    height: 40px;
                    transition: opacity 0.2s ease;
                }
            }

            &:not(.collapsed) {
                // 需要一个足够大的值来容纳展开后的内容，或者使用 auto (但 auto 不支持 transition)
                // 可以设置一个比较大的固定值，或者在 JS 中动态计算高度
                max-height: 100% !important; // 设定一个足够大的展开高度，确保内容能完全显示

                transition: max-height 0.2s ease-out; // 双向过渡动画，展开和收起都有动画效果
                // 如果内容高度不确定且可能很大，考虑用 JS 控制高度或使用其他动画方式
            }
        }

        .messageActions {
            text-align: right;
            padding-top: 16px;

            .actionIcon {
                color: #8b919e;
                font-size: 20px;
                cursor: pointer;
                margin-left: 12px;
                &:hover {
                    color: #3e4759;
                }
            }
        }
    }
}

@keyframes avatarBounce {
    0% {
        transform: translateY(0);
    }

    100% {
        transform: translateY(-4px);
    }
}
