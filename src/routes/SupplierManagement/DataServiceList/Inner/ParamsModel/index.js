import I18N from '@/utils/I18N';
import { useState, useMemo, useEffect, useRef } from 'react';
import { Modal, Form, message, Input, Select, Button, Icon, Tooltip, Upload } from 'tntd';
import AceEditor from 'react-ace';

// import "brace/mode/powershell";
// import "brace/theme/monokai";

import 'ace-builds/src-noconflict/mode-javascript';
import 'ace-builds/src-noconflict/theme-monokai';
import 'ace-builds/src-noconflict/ext-language_tools';

import { parameterAPI as service } from '@/services';

import './index.less';

const { Option } = Select;

const ParamModel = (props) => {
    const { form, currentRecord = {}, visible, isEdit, onClose, onOk } = props;
    const [max, setMax] = useState(false);
    const [dataJson, setDataJson] = useState('');
    const [maxLines, setMaxLines] = useState(15);
    const [loading, setLoading] = useState(false);
    const { getFieldDecorator, setFieldsValue, getFieldsValue, getFieldValue } = form;

    const contentType = getFieldValue('contentType');
    const [acceptType, setAcceptType] = useState('.json');
    const aceEditor = useRef();
    const editor = aceEditor?.current?.editor;

    let titleText = '';
    useEffect(() => {
        if (!visible) {
            setMax(false);
            // setDataJson('');
            setMaxLines(15);
        }
        return () => {
            setDataJson('');
            setAcceptType('.json');
            setFieldsValue({
                contentType: 'APPLICATION_JSON'
            });
        };
    }, [visible]);
    useEffect(() => {
        if (visible && aceEditor?.current?.editor) {
            // 监听 change 事件
            editor?.getSession().on('change', function () {
                var content = editor.getValue();
                var contentSize = new Blob([content]).size;
                //2 * 1024 * 1024
                if (contentSize > 2 * 1024 * 1024) {
                    // 超过大小限制的处理逻辑
                    message.warning(I18N.paramsmodel.index.neiRongDaXiaoBu);

                    // 阻止提交或保存操作
                    // ...
                    // handleSubmit(editor)
                }
            });
        }
    }, [visible, aceEditor?.current]);

    switch (contentType) {
        case 'APPLICATION_XML':
            titleText = I18N.components.paramsmodel.baoWenShiLiYong2;
            break;
        case 'APPLICATION_JSON':
            titleText = I18N.components.paramsmodel.baoWenShiLiYong;
            break;
        default:
            break;
    }

    let accept = '';
    switch (contentType) {
        case 'APPLICATION_JSON':
            accept = '.json';
            break;
        case 'APPLICATION_XML':
            accept = '.xml';
            break;
    }

    const onSubmit = () => {
        setLoading(true);

        form.validateFields((errors, data) => {
            if (!errors) {
                service.documentTypeCreate({ ...data, sample: data?.sample, dataSourceUuid: currentRecord?.uuid }).then((res) => {
                    if (res?.success) {
                        onOk();
                        message.success(I18N.paramsmodel.index.xinZengChengGong);
                        setLoading(false);
                    }

                    if (!res?.success) {
                        message.error(res?.message || res?.msg);
                        setLoading(false);
                    }
                });
            } else {
                setLoading(false);
            }
        });
    };
    // 提交或保存操作时检查内容大小
    const handleSubmit = () => {
        if (editor) {
            var content = editor?.getValue();
            var contentSize = new Blob([content])?.size;
            console.log('==contentSize', contentSize);
            // 2 * 1024 * 1024
            if (contentSize > 2 * 1024 * 1024) {
                // 超过大小限制的处理逻辑
                message.error(I18N.paramsmodel.index.neiRongDaXiaoBu);
                return;
            }
        }

        onSubmit();
        // 执行提交或保存操作
        // ...
    };

    const screen = (max) => {
        let maxLines = 15;
        if (max) {
            maxLines = 28;
        }
        setMax(max);
        setMaxLines(maxLines);
    };

    const handleJson = (dataJson) => {
        if (!isEdit) {
            return false;
        }
        setDataJson(dataJson);
        setFieldsValue({
            sample: dataJson
        });
    };
    useEffect(() => {
        const contentType = getFieldValue('contentType');
        setAcceptType(contentType === 'APPLICATION_JSON' ? '.json' : '.xml');
    }, [getFieldValue('contentType')]);

    const beforeUpload = (file) => {
        let reader = new FileReader();
        reader.readAsText(file, 'UTF-8');

        // 获取文件的大小
        const fileSize = file.size;
        // 将文件大小转换为 MB
        const fileSizeInMB = fileSize / 1024 / 1024;

        if (fileSizeInMB > 2) {
            // 文件大小超过 2M，执行相应的逻辑
            message.error(I18N.paramsmodel.index.wenJianDaXiaoBu);
            return false;
        }
        reader.onload = (e) => {
            let fileString = e.target.result;
            handleJson(fileString);
        };
        return false;
    };

    const formItemLayout = {
        labelCol: { span: 5 },
        wrapperCol: { span: 17 }
    };

    const uploadProps = {
        name: 'file',
        accept: acceptType
    };

    const randomNumber = useMemo(() => {
        // 生成 0 到 9 的随机数
        let num = Math.floor(Math.random() * 10000);
        // 确保数字有 4 位
        while (num.toString().length < 4) {
            num = Math.floor(Math.random() * 10000);
        }
        return num;
    }, []);

    return (
        <Modal
            className={max ? 'modal-form m-fullscreen' : 'modal-form'}
            title={I18N.paramsmodel.index.xinZengBaoWen}
            visible={visible}
            width={850}
            onCancel={onClose}
            footer={null}
            maskClosable={false}
            destroyOnClose>
            <div className="u-screen">
                {!max ? (
                    <Icon type="fullscreen" onClick={() => screen(true)} />
                ) : (
                    <Icon type="fullscreen-exit" onClick={() => screen(false)} />
                )}
            </div>

            <Form>
                <Form.Item className="form-item" {...formItemLayout} label={I18N.components.classifyconfig.baoWenMingCheng}>
                    {getFieldDecorator('displayName', {
                        initialValue: currentRecord?.displayName + I18N.components.testmodel.baoWen,
                        getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                        rules: [
                            { required: true, message: I18N.components.paramsmodel.qingTianXieBaoWen2 },
                            { max: 200, message: I18N.components.paramsmodel.baoWenMingChengChang }
                        ]
                    })(<Input readOnly={true} disabled={true} placeholder={I18N.components.catalogmodal.buNengChaoGuoGe} />)}
                </Form.Item>
                <Form.Item className="form-item" {...formItemLayout} label={I18N.components.classifyconfig.baoWenBiaoZhi}>
                    {getFieldDecorator('name', {
                        initialValue: currentRecord?.name + '_baowen_' + randomNumber,
                        getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                        rules: [
                            { required: true, message: I18N.components.paramsmodel.qingTianXieBaoWen },
                            {
                                pattern: '^[0-9a-zA-Z_]{1,}$',
                                message: I18N.components.indexpackagemodal.ziMuShuZiXia
                            },
                            { max: 200, message: I18N.components.treenodeitem.changDuBuNengChao }
                        ]
                    })(<Input disabled={true} placeholder={I18N.components.indexpackagemodal.ziMuShuZiXia} />)}
                </Form.Item>
                <Form.Item className="form-item" {...formItemLayout} label={I18N.components.paramsmodel.baoWenLeiXing}>
                    {getFieldDecorator('contentType', {
                        initialValue: 'APPLICATION_JSON',
                        rules: [{ required: true, message: I18N.components.paramform.qingXuanZeBaowen }]
                    })(
                        <Select
                            placeholder={I18N.components.paramform.qingXuanZeBaowen}
                            allowClear
                            onChange={(val) => {
                                setDataJson('');
                            }}>
                            <Option value="APPLICATION_JSON">JSON</Option>
                            <Option value="APPLICATION_XML">XML</Option>
                        </Select>
                    )}
                </Form.Item>

                <Form.Item
                    className="form-item"
                    {...formItemLayout}
                    label={
                        <div className="tooltip">
                            {I18N.components.importmodal.baoWenDaoRu}
                            <Tooltip placement="top" title={I18N.components.paramsmodel.qingShangChuanWenJian}>
                                <Icon type="question-circle" />
                            </Tooltip>
                        </div>
                    }>
                    <Upload {...uploadProps} beforeUpload={beforeUpload} showUploadList={false}>
                        <Button disabled={!isEdit}>
                            <Icon type="upload" /> {I18N.components.list.daoRu}
                        </Button>
                    </Upload>
                    <span style={{ color: 'red', marginLeft: '15px' }}>{I18N.components.paramsmodel.baoWenChongXinDao}</span>
                </Form.Item>

                <Form.Item
                    className="form-item"
                    {...formItemLayout}
                    label={
                        <div className="tooltip">
                            {I18N.components.paramsmodel.baoWenShiLi}
                            <Tooltip placement="top" title={titleText}>
                                <Icon type="question-circle" />
                            </Tooltip>
                        </div>
                    }>
                    {getFieldDecorator('sample', {
                        initialValue: dataJson,
                        rules: [{ required: true, message: I18N.paramsmodel.index.qingTianXieBaoWen }]
                    })(<Input hidden placeholder={I18N.components.paramsmodel.baoWenShiLi} disabled={!isEdit} />)}
                    <AceEditor
                        mode="javascript"
                        theme="monokai"
                        value={dataJson}
                        ref={aceEditor}
                        onChange={handleJson}
                        width="100%"
                        placeholder={I18N.components.paramsmodel.qingShuRuNeiRong}
                        minLines={maxLines}
                        maxLines={maxLines}
                        readOnly={!isEdit}
                        // disabled={!isEdit}
                        className="ace-editor"
                        highlightActiveLine
                        showPrintMargin={false}
                        showGutter
                        editorProps={{ $blockScrolling: true }}
                    />
                </Form.Item>
            </Form>
            <div className="footer">
                <span className="footer-btn">
                    <Button onClick={onClose}>{I18N.addmodify.index.quXiao}</Button>
                    <Button onClick={handleSubmit} loading={loading} type="primary">
                        {I18N.childflownode.index.queDing}</Button>
                </span>
            </div>
        </Modal>
    );
};

export default Form.create()(ParamModel);
