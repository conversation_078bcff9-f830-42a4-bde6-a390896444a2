import I18N from '@/utils/I18N';
import { useEffect, useState, Fragment, useMemo } from 'react';
import '../index.less';
import {
    message,
    Input,
    Select,
    Tooltip,
    Icon,
    Ellipsis,
    Title,
    Row,
    Empty,
    Col,
    TntdForm as Form,
    Checkbox,
    Button,
    PageContainer,
    InputNumber,
    Radio
} from 'tntd';

import {
    allConfigName,
    basicRequiredName,
    contractRequiredName,
    allbasicName,
    allcontractName,
    allcacheName,
    allFlowlimitName
} from './constant';
import classNames from 'classnames';

import { dataServiceListAPI, systemFieldsAPI, supplierListAPI, captainAPI, contractListAPI } from '@/services';
import ContractModal from '@/routes/SupplierManagement/ContractList/Inner/AddModifyModal';

import FlowLimitConfigTable from './Components/FlowLimitConfigTable';
import SyncProxyInput from './Components/SyncProxyInput';
import { CacheSettingFormItem } from './Components/CacheSettingForm';
import MockConfig from '../../Detail/MockConfig';
import ServiceInput from '../ServiceInput';
import ServiceOutput from '../ServiceOutput';

const TextArea = Input.TextArea;
const Option = Select.Option;
import './index.less';
const ConfigList = (props) => {
    const {
        addEditModalData,
        globalStore,
        modalType,
        disabled,
        systemList,
        documentTypeList,
        providerList,
        isSync,
        featureSetList,
        etlList,
        history
    } = props;
    // 菜单项数据
    const { allMap } = globalStore;
    const { isIntegrationTG, captainEnable } = allMap;
    // 是否已保存过（成功保存一次后设置为true）
    // const [hasSaved, setHasSaved] = useState(true || !!addEditModalData?.uuid);// (可暂时设置为true，方便测试，后续去掉true)
    const [hasSaved, setHasSaved] = useState(!!addEditModalData?.uuid);
    const [AiMockData, setAiMockData] = useState(null);
    const [mockTableList, setMockTableList] = useState([]);

    const menuItems = [
        { key: 'basic', name: '基础配置', completed: false, required: true, selected: true },
        { key: 'contract', name: '合同计费与出入参', completed: false, required: true },
        { key: 'cache', name: '缓存设置', completed: true, required: false },
        { key: 'flow', name: '流量控制', completed: true, required: false },
        { key: 'mock', name: '模拟调用', required: false, hasTooltip: !hasSaved, tooltip: '保存后可进行模拟调用配置，保存后此图标消失' }
    ];
    const [form] = Form.useForm();
    const { getFieldsValue } = form;

    // 当前选中的菜单项
    const [selectedKey, setSelectedKey] = useState('basic');
    // 是否显示高级配置
    const [showAdvanced, setShowAdvanced] = useState(false);
    // 合同列表状态
    const [localContractList, setContractList] = useState([]);
    // 合同详情状态
    const [contractModalData, setContractModalData] = useState({
        visible: false,
        modalType: 3,
        uuid: null,
        data: {}
    });

    // 切换选中的菜单项
    const handleMenuItemClick = (key) => {
        // 在切换前校验当前页面
        validateCurrentPage(selectedKey);

        // 如果切换到模拟调用页面，需要校验所有前面的页面
        if (key === 'mock') {
            ['basic', 'contract'].forEach((pageKey) => {
                if (!validatedPages.has(pageKey)) {
                    validateCurrentPage(pageKey);
                }
            });
        }
        setSelectedKey(key);
    };
    const getAiMockData = (data) => {
        setAiMockData(data);
    };

    const getMockTableList = (tableList) => {
        setMockTableList(tableList);
    };

    // 切换高级配置显示状态
    const toggleAdvanced = () => {
        setShowAdvanced(!showAdvanced);
    };
    const [isPreCheckbox, setIsPreCheckbox] = useState(false); //前置处理器
    const [isPostEtlkRadio, setIsPostEtlkRadio] = useState('json'); //后置处理器
    const [originalPostEtlHandlerName, setOriginalPostEtlHandlerName] = useState(''); //保存原始的后置ETL处理器名称

    // 直接用form来管理表单项
    useEffect(() => {
        if (addEditModalData) {
            form.setFieldsValue(addEditModalData);

            // 判断前置ETL处理器是否有值，有值则勾选
            if (addEditModalData?.preEtlHandlerName) {
                setIsPreCheckbox(true);
            } else {
                setIsPreCheckbox(false);
            }

            // 判断后置ETL处理器类型并设置单选按钮状态
            if (addEditModalData?.postEtlHandlerName) {
                if (addEditModalData.postEtlHandlerName === 'json') {
                    setIsPostEtlkRadio('json');
                    setOriginalPostEtlHandlerName('');
                } else {
                    setIsPostEtlkRadio('postEtl');
                    setOriginalPostEtlHandlerName(addEditModalData.postEtlHandlerName);
                }
            } else {
                // 默认为json
                setIsPostEtlkRadio('json');
                setOriginalPostEtlHandlerName('');
                form.setFieldValue('postEtlHandlerName', 'json');
            }
        }
    }, [addEditModalData, form]);

    // methodType 直接用form.getFieldValue
    const [methodType, setMethodType] = useState(form.getFieldValue('methodType'));
    // const methodType = form.getFieldValue('methodType');

    const getIndexPackageName = (indexPackageName) => {
        const indexName = indexPackageName
            ?.map((item) => featureSetList.find((i) => i.name === item)?.displayName)
            ?.filter(Boolean)
            .join(',');
        return indexName;
    };

    // 只校验basic和contract
    const validateFieldsKeys = allConfigName;

    // 是否已点击过保存
    const [hasTriedSave, setHasTriedSave] = useState(false);

    // 根据页面类型获取需要校验的必填字段
    const getValidationFieldsByPage = (pageKey) => {
        switch (pageKey) {
            case 'basic':
                return basicRequiredName;
            case 'contract':
                return contractRequiredName;
            default:
                return [];
        }
    };

    // 校验当前页面
    const validateCurrentPage = (pageKey) => {
        let fieldsToValidate = getValidationFieldsByPage(pageKey);

        // 特殊处理缓存页面的校验
        if (pageKey === 'cache') {
            const currentInvokePolicy = form.getFieldValue('invokePolicy');
            if (currentInvokePolicy !== 1) {
                // 如果不是"直接接口"，则需要校验缓存设置
                fieldsToValidate = [...fieldsToValidate, 'cacheTime'];
            }
        }

        // 标记该页面已被校验
        setValidatedPages((prev) => new Set([...prev, pageKey]));

        if (fieldsToValidate.length === 0) {
            // 没有必填字段的页面默认为通过
            setMenuItemsStatus((prev) => ({
                ...prev,
                [pageKey]: true
            }));
            return;
        }

        // 静默校验，不显示错误信息
        form.validateFields(fieldsToValidate)
            .then(() => {
                // 校验通过，设置成功状态
                setMenuItemsStatus((prev) => ({
                    ...prev,
                    [pageKey]: true
                }));
            })
            .catch(({ errorFields }) => {
                // 只有必填字段校验失败才设置错误状态
                const hasRequiredFieldError = errorFields.some(({ errors }) =>
                    errors.some((error) => error.includes('请输入') || error.includes('请选择') || error.includes('请设置'))
                );

                setMenuItemsStatus((prev) => ({
                    ...prev,
                    [pageKey]: !hasRequiredFieldError
                }));
            });
    };

    // 处理保存
    const handleSave = () => {
        setHasTriedSave(true);
        // 标记所有页面为已校验
        setValidatedPages(new Set(['basic', 'contract', 'cache', 'flow']));

        form.validateFields(validateFieldsKeys)
            .then((values) => {
                // Form校验通过，设置所有状态为通过
                setMenuItemsStatus({
                    basic: true,
                    contract: true,
                    cache: true,
                    flow: true
                });
                // 保存逻辑
                onSave(values);
            })
            .catch(({ errorFields }) => {
                // 归类错误到页面
                const errorMap = {
                    basic: true,
                    contract: true,
                    cache: true,
                    flow: true
                };
                errorFields.forEach(({ name }) => {
                    const field = name[0];
                    if (allbasicName.includes(field)) {
                        errorMap.basic = false;
                    }
                    if (allcontractName.includes(field)) {
                        errorMap.contract = false;
                    }
                    if (allcacheName.includes(field)) {
                        errorMap.cache = false;
                    }
                    if (allFlowlimitName.includes(field)) {
                        errorMap.flow = false;
                    }
                });
                setMenuItemsStatus(errorMap);
            });
    };
    const [uuid, setUuid] = useState('');
    useEffect(() => {
        setUuid(addEditModalData?.uuid || '');
    }, [addEditModalData]);

    // 服务入参校验函数
    const validateInputConfig = (inputConfig) => {
        return new Promise((resolve, reject) => {
            if (!inputConfig || inputConfig.length === 0) {
                reject(new Error(I18N.formlist.step2.fuWuRuCanBu));
                return;
            }

            // 检查是否填写完整
            let inputConfigFlag = true;
            let repeatName = null;
            let repeatValue = null;
            let inputConfigQueryFlag = false;
            let isSocket = methodType === 'socket';

            inputConfig.forEach((item) => {
                if (!item.serviceParam || !(item.sendSpace || isSocket)) {
                    inputConfigFlag = false;
                }

                // 检查重名参数标识
                inputConfig.forEach((subItem) => {
                    if (subItem.serviceParam === item.serviceParam && subItem.uuid !== item.uuid) {
                        repeatName = subItem.serviceParam;
                    }
                    if (subItem.type === 'constant' && subItem.value === item.value && subItem.uuid !== item.uuid) {
                        repeatValue = subItem.value;
                    }
                });

                // 检查是否有查询字段
                if (item.keyInclude) {
                    inputConfigQueryFlag = true;
                }
            });

            if (!inputConfigFlag) {
                reject(new Error(I18N.formlist.step2.fuWuRuCanYou));
                return;
            }

            if (repeatName) {
                reject(new Error(I18N.template(I18N.formlist.step2.fuWuRuCanBu2, { val1: repeatName })));
                return;
            }

            if (repeatValue) {
                reject(new Error(I18N.template(I18N.formlist.step2.fuWuRuCanBu3, { val1: repeatValue })));
                return;
            }

            if (!inputConfigQueryFlag) {
                reject(new Error(I18N.formlist.step2.fuWuRuCanZhong));
                return;
            }

            resolve();
        });
    };

    // 服务出参校验函数
    const validateOutputConfig = (outputConfig) => {
        return new Promise((resolve, reject) => {
            if (!outputConfig || outputConfig.length === 0) {
                reject(new Error(I18N.formlist.step2.fuWuChuCanBu));
                return;
            }

            let flag2 = false;
            let repeatName2 = null;
            let checkFlag = false;
            let { chargeMethod } = getFieldsValue();

            outputConfig.forEach((item) => {
                // 检查是否填写完整
                if (item.type === 'variable') {
                    if (!item.serviceParam || !item.field) {
                        flag2 = true;
                    }
                } else {
                    if (!item.field || !item.value) {
                        flag2 = true;
                    }
                }
                if (item.checked && item.includeCheck.checkType === 2 && !item.includeCheck.checkValue) {
                    flag2 = true;
                }

                // 检查重名参数标识
                const obj = outputConfig.find((subItem) => subItem.serviceParam === item.serviceParam);
                if (obj && item.uuid !== obj.uuid) {
                    repeatName2 = obj.serviceParam;
                }
                if (item.checked) {
                    checkFlag = true;
                }
            });

            if (flag2) {
                reject(new Error(I18N.formlist.step2.fuWuChuCanYou));
                return;
            }

            if (repeatName2) {
                reject(new Error(I18N.template(I18N.formlist.step2.fuWuChuCanBu2, { val1: repeatName2 })));
                return;
            }

            // 查得计费至少配一个字段为查得
            if (!checkFlag && chargeMethod === 2) {
                reject(new Error(I18N.formlist.step2.dangQianJiFeiFang));
                return;
            }

            resolve();
        });
    };

    const onSave = (params) => {
        const { account } = globalStore.currentUser;
        let { limitConfig, inputConfig, outputConfig, cacheTime, indexPackageName = [] } = params;
        params.cacheTime = JSON.stringify(cacheTime);
        if (params.proxy === '0') params.proxyInfo = null;
        params.pagination = params.pagination ? 1 : 0;
        params = {
            ...params?.proxys,
            ...params,
            ...cacheTime
        };
        delete params.proxys;
        delete params.cacheTime;
        params.response = AiMockData; //aiMock报文信息
        params.indexPackageName = indexPackageName.join(',');
        params.inputConfig = JSON.stringify(inputConfig);
        params.outputConfig = JSON.stringify(outputConfig);
        params.uuid = uuid;
        params.limitConfig = JSON.stringify(limitConfig);

        params.dataSourceType = 'SYNC';
        if (modalType === 1 && !hasSaved) {
            params.creator = account;
            dataServiceListAPI.addData(params).then((res) => {
                if (res && res.success) {
                    setUuid(res?.data?.uuid);
                    message.success(I18N.formlist.step3.caoZuoChengGong);
                    setHasSaved(true); // 标记为已保存
                } else {
                    message.error(res.message);
                }
            });
        } else {
            params.operator = account;
            dataServiceListAPI.updateData(params).then((res) => {
                if (res && res.success) {
                    message.success(I18N.formlist.step3.caoZuoChengGong);
                    setHasSaved(true); // 标记为已保存
                } else {
                    message.error(res.message);
                }
            });
        }
    };

    // 处理取消
    const handleCancel = () => {
        history.push('/handle/supplierManagement/dataServiceList');
    };

    // 处理保存并关闭
    const handleSaveAndClose = () => {
        setHasTriedSave(true);
        // 标记所有页面为已校验
        setValidatedPages(new Set(['basic', 'contract', 'cache', 'flow']));

        form.validateFields(validateFieldsKeys)
            .then((values) => {
                // Form校验通过，设置所有状态为通过并保存关闭
                setMenuItemsStatus({
                    basic: true,
                    contract: true,
                    cache: true,
                    flow: true
                });
                // 保存成功后关闭
                Promise.resolve(onSave(values))
                    .then(() => {
                        handleCancel(); // 关闭页面
                    })
                    .catch(() => {
                        // 保存失败不关闭
                    });
            })
            .catch(({ errorFields }) => {
                // 归类错误到页面
                const errorMap = {
                    basic: true,
                    contract: true,
                    cache: true,
                    flow: true
                };
                errorFields.forEach(({ name }) => {
                    const field = name[0];
                    if (allbasicName.includes(field)) {
                        errorMap.basic = false;
                    }
                    if (allcontractName.includes(field)) {
                        errorMap.contract = false;
                    }
                    if (allcacheName.includes(field)) {
                        errorMap.cache = false;
                    }
                    if (allFlowlimitName.includes(field)) {
                        errorMap.flow = false;
                    }
                });
                setMenuItemsStatus(errorMap);
            });
    };

    // 查看合同详情
    const handleContract = (data) => {
        getInfo(data.uuid, 3);
    };

    let hostReg =
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\:([0-9]|[1-9]\d{1,3}|[1-5]\d{4}|6[0-5]{2}[0-3][0-5])$/;

    // 获取单条合同信息
    const getInfo = (uuid, modalType) => {
        contractListAPI.getInfo({ uuid }).then((res) => {
            if (res.success) {
                if (!res.data) return;
                const {
                    code,
                    version,
                    name,
                    startTime,
                    endTime,
                    chargeType,
                    contractFileNames,
                    interfaceFileNames,
                    chargeConfig,
                    masterDimensions,
                    followDimensions,
                    luaScript
                } = res.data;
                const { dispatch } = props;

                let attachFileList = [];
                let contractList = contractFileNames ? JSON.parse(contractFileNames) : [];
                contractList.forEach((item) => {
                    attachFileList.push({ name: item });
                });

                let docFileList = [];
                let interfaceList = interfaceFileNames ? JSON.parse(interfaceFileNames) : [];
                interfaceList.forEach((item) => {
                    docFileList.push({ name: item });
                });

                let price = null;
                let obj = {
                    fieldCode: null,
                    matchType: null,
                    matchConfig: {
                        regex: null
                    }
                };
                let countConfig = [{ price: null, matchFields: [obj] }];
                let flowRange = [{ begin: null, end: -99, price: null }];
                let fieldPricesFlowRange = [
                    {
                        hierarchyDetail: [{ begin: null, end: -99, price: null }],
                        matchFields: [obj]
                    }
                ];
                let dateRange = [{ begin: startTime, end: endTime, price: null }];
                let config = chargeConfig ? JSON.parse(chargeConfig) : null;
                if (config) {
                    if (chargeType === 'countFieldPrices') {
                        countConfig = config.fieldPrices;
                    }
                    if (chargeType === 'hierarchyDimensionsFlow') {
                        fieldPricesFlowRange = config.fieldPrices;
                    }
                    if (chargeType === 'hierarchyDate') {
                        // 阶梯计费-日期区间
                        dateRange = config.hierarchyDetail;
                    } else if (chargeType === 'hierarchyFlow') {
                        // 阶梯计费-流量
                        flowRange = config.hierarchyDetail;
                    } else {
                        price = config.price;
                    }
                }

                dispatch({
                    type: 'contractList/setAttrValue',
                    payload: {
                        dialogShow: {
                            addEditModal: true
                        },
                        modalType,
                        updateId: uuid,
                        dialogData: {
                            addEditModalData: {
                                code, // 合同编号
                                version, // 合同版本
                                name, // 合同名称
                                startTime, // 合同开始日期
                                endTime, // 合同结束日期
                                chargeType, // 计费方式
                                docFileList, // 接口文档
                                attachFileList, // 合同附件
                                price, // 价格
                                masterDimensions, // 主属性
                                followDimensions, // 从属性
                                luaScript, // lua脚本
                                countConfig, // 按次字段匹配计费
                                flowRange, // 流量区间
                                fieldPricesFlowRange, // 阶梯字段匹配计费-流量
                                dateRange // 日期区间
                            }
                        }
                    }
                });
            } else {
                message.error(res.message);
            }
        });
    };

    const formItemLayout = {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 }
    };
    const sqlTypeList = ['mysql', 'oracle', 'postgreSQL'];

    const handlePreCheckbox = (e) => {
        setIsPreCheckbox(e.target.checked);
    };
    const handlePostEtlRadio = (e) => {
        if (e.target.value === 'json') {
            setIsPostEtlkRadio('json');
            form.setFieldValue('postEtlHandlerName', 'json');
        } else {
            setIsPostEtlkRadio('postEtl');
            // 切换到后置ETL处理器时，恢复原始值或清空
            if (originalPostEtlHandlerName && originalPostEtlHandlerName !== 'json') {
                form.setFieldValue('postEtlHandlerName', originalPostEtlHandlerName);
            } else {
                form.setFieldValue('postEtlHandlerName', undefined);
            }
        }
    };
    const methodList = useMemo(() => {
        let list = [];
        if (methodType && allMap && allMap.contentTypeList && allMap.methodTypeList) {
            const obj = allMap.methodTypeList.find((item) => item.code === methodType);
            if (obj) {
                allMap.contentTypeList.forEach((item) => {
                    if (item.type === obj.type) {
                        list.push(item);
                    }
                });
            }
        }
        return list;
    }, [methodType, allMap]);

    // 保留原有的validate函数作为备用，现在主要依赖Form的校验
    const validate = () => {
        // 现在主要的校验逻辑已经移到Form的validator中
        // 这里可以保留一些额外的业务逻辑校验
        return true;
    };
    const onMethodTypeChange = (value) => {
        setMethodType(value);
        form.setFieldValue('contentType', undefined);
    };
    const [invokePolicy, setInvokePolicy] = useState(form.getFieldValue('invokePolicy') || 0);
    const handleInvokePolicy = (e) => {
        setInvokePolicy(e.target.value);
    };
    // 显示相应的右侧内容
    const renderContent = (key) => {
        switch (key) {
            case 'basic':
                return (
                    <div className="service-form-basic-config">
                        <Title title="基础配置" style={{ marginBottom: 16 }} />
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'displayName'}
                                    label={'接口名称'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入接口名称'
                                        }
                                    ]}>
                                    <Input placeholder="请输入" />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'name'}
                                    label={'接口标识'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入接口标识'
                                        },

                                        {
                                            pattern: /^[a-zA-Z0-9\_]+$/,
                                            message: I18N.formlist.step1.shuJuYuanFuWuB
                                        },
                                        // 长度限制50个字符
                                        {
                                            max: 50,
                                            message: I18N.formlist.step1.changDuBuNengChao
                                        }
                                    ]}>
                                    <Input placeholder="请输入" disabled={disabled || modalType === 2} />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'url'}
                                    label={'url地址'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入url地址'
                                        }
                                    ]}>
                                    <Input placeholder="请输入" />
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'methodType'}
                                    label={'协议'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择协议'
                                        }
                                    ]}>
                                    <Select
                                        showSearch
                                        style={{ width: '100%' }}
                                        dropdownStyle={{ width: 350 }}
                                        disabled={disabled}
                                        onChange={(e) => {
                                            onMethodTypeChange(e);
                                        }}
                                        placeholder={'请选择接口类型协议'}
                                        dropdownMatchSelectWidth={false}
                                        optionFilterProp="children">
                                        {allMap &&
                                            allMap.methodTypeList &&
                                            allMap.methodTypeList
                                                .filter((i) => {
                                                    if (sqlTypeList.includes(i.code) && !isSync) {
                                                        return false;
                                                    }
                                                    return true;
                                                })
                                                .map((item, index) => {
                                                    return (
                                                        <Option value={item.code} key={index}>
                                                            {item.name}
                                                        </Option>
                                                    );
                                                })}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'contentType'}
                                    label={'调用方式'}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择调用方式'
                                        }
                                    ]}>
                                    <Select
                                        showSearch
                                        style={{ width: '100%' }}
                                        dropdownStyle={{ width: 350 }}
                                        disabled={disabled}
                                        placeholder={I18N.formlist.step2.qingXuanZeDiaoYong}
                                        dropdownMatchSelectWidth={false}
                                        optionFilterProp="children">
                                        {methodList.map((item, index) => {
                                            return (
                                                <Option value={item.code} key={index}>
                                                    {item.code}
                                                </Option>
                                            );
                                        })}
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>

                        <div className="advanced-config-btn" onClick={toggleAdvanced}>
                            <span className="link-text">
                                高级配置
                                <Icon type={showAdvanced ? 'up' : 'down'} />
                            </span>
                        </div>

                        {showAdvanced && (
                            <div className="advanced-section">
                                <Row gutter={16}>
                                    <Col span={12}>
                                        <Form.Item name={'documentTypeUuid'} label={'报文'}>
                                            <Select
                                                allowClear
                                                showSearch
                                                disabled={disabled}
                                                dropdownMatchSelectWidth={false}
                                                dropdownStyle={{ width: 350 }}
                                                placeholder={I18N.formlist.step2.qingXuanZeBaoWen}
                                                optionFilterProp="children">
                                                {documentTypeList?.map((v) => (
                                                    <Option key={v.uuid} value={v.uuid}>
                                                        {v.displayName}
                                                    </Option>
                                                ))}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                    <Col span={12}>
                                        <Form.Item name={'indexPackageName'} label={'指标集'}>
                                            <Select
                                                mode="multiple"
                                                showSearch
                                                disabled={disabled}
                                                dropdownMatchSelectWidth={false}
                                                dropdownStyle={{ width: 350 }}
                                                placeholder={I18N.formlist.step2.qingXuanZeZhiBiao}
                                                optionFilterProp="children">
                                                {featureSetList &&
                                                    featureSetList.map((item) => (
                                                        <Option value={item.name} key={item.name}>
                                                            {item.displayName}
                                                        </Option>
                                                    ))}
                                            </Select>
                                        </Form.Item>
                                    </Col>
                                </Row>

                                <Row gutter={16}>
                                    <Col span={12}>
                                        <Form.Item initialValue="3000" name={'timeout'} label={'超时时长'}>
                                            <Input placeholder="请输入" addonAfter="ms" />
                                        </Form.Item>
                                    </Col>
                                    <Col span={12}>
                                        <Form.Item initialValue="3" name={'retry'} label={'重试次数'}>
                                            <Input addonAfter="次" placeholder="请输入" />
                                        </Form.Item>
                                    </Col>
                                </Row>

                                <Row gutter={24}>
                                    <Col span={24}>
                                        <div className="advanced-section-start-pagination" style={{ display: 'flex' }}>
                                            <Form.Item
                                                initialValue={false}
                                                style={{ width: 150 }}
                                                name={'pagination'}
                                                valuePropName="checked">
                                                <Checkbox>开启分页接口</Checkbox>
                                            </Form.Item>
                                            <Form.Item
                                                style={{ width: 600 }}
                                                name={'proxys'}
                                                rules={[
                                                    {
                                                        validator: (rule, value, callback) => {
                                                            let { proxy, proxyInfo } = value || {};
                                                            // 如果没有选择代理，则不需要校验
                                                            if (!proxy || proxy === '0') {
                                                                callback();
                                                                return;
                                                            }
                                                            // 如果选择了使用代理，但没有填写代理信息，则报错
                                                            if (proxy === '1' && !proxyInfo) {
                                                                callback(new Error(I18N.formlist.step2.qingTianXieWanZheng));
                                                                return;
                                                            }
                                                            // 如果选择了使用代理，且填写了代理信息，但格式不正确，则报错
                                                            if (proxy === '1' && proxyInfo && !hostReg.test(proxyInfo)) {
                                                                callback(new Error(I18N.formlist.step2.daiLiXinXiGe));
                                                                return;
                                                            }
                                                            callback();
                                                        }
                                                    }
                                                ]}>
                                                <SyncProxyInput disabled={disabled} isSync={true} current={1} />
                                            </Form.Item>
                                        </div>
                                    </Col>
                                </Row>
                            </div>
                        )}
                    </div>
                );
            case 'contract':
                return (
                    <div className="service-form-contract-config">
                        <Title title="合同计费" style={{ marginBottom: 16 }} />
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'dataType'}
                                    label={'数据类型'}
                                    wrapperCol={{ span: 19 }}
                                    labelCol={{ span: 5 }}
                                    rules={[
                                        {
                                            required: true,
                                            message: '请输入数据类型'
                                        }
                                    ]}>
                                    <Select
                                        allowClear
                                        showSearch
                                        value={getFieldsValue()?.dataType || undefined}
                                        disabled={disabled}
                                        dropdownMatchSelectWidth={false}
                                        dropdownStyle={{ width: 350 }}
                                        placeholder={'请选择数据类型'}
                                        optionFilterProp="children">
                                        {allMap?.serviceTypeList?.map((v) => (
                                            <Option key={v.dataType} value={v.dataType}>
                                                {v.name}
                                            </Option>
                                        ))}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'partnerId'}
                                    wrapperCol={{ span: 19 }}
                                    labelCol={{ span: 5 }}
                                    label={'合作方名称'}
                                    rules={[
                                        {
                                            required: true,
                                            message: I18N.formlist.step1.qingXuanZeHeZuo
                                        }
                                    ]}>
                                    <Select
                                        allowClear
                                        showSearch
                                        style={{ width: '100%' }}
                                        disabled={disabled}
                                        dropdownStyle={{ width: 350 }}
                                        placeholder={I18N.formlist.step1.qingXuanZeHeZuo}
                                        optionFilterProp="children"
                                        value={getFieldsValue()?.partnerId || undefined}
                                        onChange={(value) => {
                                            form.setFieldValue('partnerId', value);
                                            // 更新合同列表
                                            if (value) {
                                                supplierListAPI.getList().then((res) => {
                                                    if (res?.success) {
                                                        const providerList = res?.data?.contents || [];
                                                        const findObj = providerList.find((item) => item.uuid === value);
                                                        if (findObj) {
                                                            contractListAPI.getList({ providerUuid: findObj?.uuid }).then((res) => {
                                                                if (res.success) {
                                                                    setContractList(res?.data?.contents || []);
                                                                }
                                                            });
                                                        }
                                                    }
                                                });
                                            }
                                        }}>
                                        {providerList &&
                                            providerList
                                                .filter(({ status }) => status !== 2)
                                                .map((item, index) => {
                                                    return (
                                                        <Option value={item.uuid} key={index}>
                                                            {item.displayName}
                                                        </Option>
                                                    );
                                                })}
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'contractId'}
                                    label={'合同'}
                                    wrapperCol={{ span: 19 }}
                                    labelCol={{ span: 5 }}
                                    rules={[
                                        {
                                            required: true,
                                            message: I18N.formlist.step1.qingXuanZeHeTong
                                        }
                                    ]}>
                                    <Select
                                        allowClear
                                        showSearch
                                        style={{ width: '100%' }}
                                        disabled={disabled}
                                        placeholder={I18N.formlist.step1.qingXuanZeHeTong}
                                        optionFilterProp="label"
                                        value={getFieldsValue()?.contractId || undefined}
                                        onChange={(value) => form.setFieldValue('contractId', value)}>
                                        {localContractList &&
                                            localContractList.map((item, index) => {
                                                return (
                                                    <Option
                                                        value={item.uuid}
                                                        key={index}
                                                        label={item.name}
                                                        title={item.name}
                                                        chargeType={item.chargeType || ''}>
                                                        <Tooltip title={item.name} placement="top">
                                                            <span
                                                                className={classNames('u-contract-option', {
                                                                    'u-contract-option-disabled': item.status !== '1'
                                                                })}>
                                                                {item.name}
                                                            </span>
                                                        </Tooltip>
                                                        <span
                                                            className="u-contract-option-detail"
                                                            onClick={(e) => {
                                                                e.stopPropagation();
                                                                // 查看合同详情
                                                                handleContract(item);
                                                            }}>
                                                            {I18N.formlist.step1.chaKanXiangQing}
                                                        </span>
                                                    </Option>
                                                );
                                            })}
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'confidence'}
                                    label={
                                        <span style={{ verticalAlign: 'middle' }}>
                                            <span style={{ marginRight: 4 }}>{I18N.formlist.step1.zhiXinDu}</span>
                                            <Tooltip title={I18N.formlist.step1.jieHeJingYanDui}>
                                                <Icon type="question-circle" />
                                            </Tooltip>
                                        </span>
                                    }
                                    wrapperCol={{ span: 19 }}
                                    labelCol={{ span: 5 }}
                                    rules={[
                                        {
                                            required: true,
                                            message: I18N.formlist.step1.qingXuanZeZhiXin
                                        }
                                    ]}>
                                    <Select
                                        showSearch
                                        style={{ width: '100%' }}
                                        placeholder={I18N.formlist.step1.qingXuanZeZhiXin}
                                        disabled={disabled}
                                        optionFilterProp="children"
                                        value={getFieldsValue()?.confidence || undefined}
                                        onChange={(value) => form.setFieldValue('confidence', value)}>
                                        <Option value={1}>{I18N.formlist.step1.gao}</Option>
                                        <Option value={2}>{I18N.formlist.step1.zhong}</Option>
                                        <Option value={3}>{I18N.formlist.step1.di}</Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Row gutter={16}>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'costLevel'}
                                    label={
                                        <span style={{ verticalAlign: 'middle' }}>
                                            <span>{I18N.formlist.step1.chengBenDengJi}</span>{' '}
                                            <Tooltip title={I18N.formlist.step1.genJuShuJuYuan}>
                                                <Icon type="question-circle" />
                                            </Tooltip>
                                        </span>
                                    }
                                    wrapperCol={{ span: 19 }}
                                    labelCol={{ span: 5 }}
                                    rules={[
                                        {
                                            required: true,
                                            message: I18N.formlist.step1.qingXuanZeChengBen
                                        }
                                    ]}>
                                    <Select
                                        showSearch
                                        style={{ width: '100%' }}
                                        disabled={disabled}
                                        placeholder={I18N.formlist.step1.qingXuanZeChengBen}
                                        optionFilterProp="children"
                                        value={getFieldsValue()?.costLevel || undefined}
                                        onChange={(value) => form.setFieldValue('costLevel', value)}>
                                        <Option value={1}>{I18N.formlist.step1.gao}</Option>
                                        <Option value={2}>{I18N.formlist.step1.zhong}</Option>
                                        <Option value={3}>{I18N.formlist.step1.di}</Option>
                                    </Select>
                                </Form.Item>
                            </Col>
                            <Col span={12}>
                                <Form.Item
                                    required
                                    name={'chargeMethod'}
                                    label={'计费类型'}
                                    wrapperCol={{ span: 19 }}
                                    labelCol={{ span: 5 }}
                                    initialValue={1}
                                    rules={[
                                        {
                                            required: true,
                                            message: I18N.formlist.step1.qingXuanZeJiFei
                                        }
                                    ]}>
                                    <Radio.Group disabled={disabled}>
                                        <Radio value={1}>查询计费</Radio>
                                        <Radio value={2}>查得计费</Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </Col>
                        </Row>
                        <Title
                            title={
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                    <div style={{ marginRight: 16 }}>服务入参</div>
                                    <div className="service-form-contract-config-preetlhandler">
                                        <Checkbox checked={isPreCheckbox} onChange={handlePreCheckbox}>
                                            {'前置ETL处理器'}
                                        </Checkbox>
                                    </div>
                                    {isPreCheckbox && (
                                        <Form.Item name="preEtlHandlerName" wrapperCol={{ span: 24 }} style={{ marginTop: 24 }}>
                                            <Select
                                                showSearch
                                                allowClear
                                                style={{ width: 160, fontWeight: 400 }}
                                                disabled={disabled}
                                                dropdownMatchSelectWidth={true}
                                                placeholder={I18N.formlist.step2.qingXuanZeET}
                                                value={getFieldsValue()?.preEtlHandlerName || undefined}
                                                onChange={(value) => form.setFieldValue('preEtlHandlerName', value)}
                                                optionFilterProp="children">
                                                {etlList &&
                                                    etlList.map((item, index) => {
                                                        let dom = null;
                                                        if (item.type === 2) {
                                                            dom = (
                                                                <Option value={item.name} key={index}>
                                                                    <Ellipsis title={item.displayName} />
                                                                </Option>
                                                            );
                                                        }
                                                        return dom;
                                                    })}
                                            </Select>
                                        </Form.Item>
                                    )}
                                </div>
                            }
                            style={{ marginBottom: 16 }}
                        />
                        <div className="service-form-contract-config-input">
                            <Form.Item
                                name="inputConfig"
                                wrapperCol={{ span: 24 }}
                                rules={[
                                    {
                                        validator: (_, value) => {
                                            return validateInputConfig(value);
                                        }
                                    }
                                ]}>
                                <ServiceInput
                                    modalType={modalType}
                                    isSync={true}
                                    disabled={disabled}
                                    systemList={systemList}
                                    methodType={methodType}
                                    isSql={sqlTypeList.includes(methodType)}
                                />
                            </Form.Item>
                        </div>

                        <Title title="服务出参" style={{ marginBottom: 16 }} />
                        <div className="service-form-contract-config-output">
                            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
                                <div style={{ marginRight: 16 }}>后置ETL处理器</div>
                                <div className="service-form-contract-config-postEtlhandler">
                                    <Radio.Group value={isPostEtlkRadio} onChange={handlePostEtlRadio}>
                                        <Radio value={'json'}>JSON-ETL</Radio>
                                        <Radio value={'postEtl'}>后置ETL处理器</Radio>
                                    </Radio.Group>
                                </div>
                                {isPostEtlkRadio === 'postEtl' && (
                                    <Form.Item style={{ marginBottom: 0 }} name="postEtlHandlerName">
                                        <Select
                                            showSearch
                                            disabled={disabled}
                                            style={{ width: 160 }}
                                            dropdownMatchSelectWidth={true}
                                            placeholder={I18N.formlist.step2.qingXuanZeET}
                                            value={getFieldsValue()?.postEtlHandlerName || undefined}
                                            onChange={(value) => {
                                                form.setFieldValue('postEtlHandlerName', value);
                                                // 更新原始值，以便下次切换时能正确恢复
                                                if (value && value !== 'json') {
                                                    setOriginalPostEtlHandlerName(value);
                                                }
                                            }}
                                            optionFilterProp="children">
                                            {etlList &&
                                                etlList
                                                    .filter((i) => {
                                                        if (i.type === 3) {
                                                            // let isPagination = getFieldsValue()?.pagination ? 1 : 0;
                                                            // if (i.tag !== isPagination) return false;
                                                            return true;
                                                        }
                                                        return false;
                                                    })
                                                    .map((item, index) => {
                                                        return (
                                                            <Option value={item.name} key={index}>
                                                                {item.displayName}
                                                            </Option>
                                                        );
                                                    })}
                                        </Select>
                                    </Form.Item>
                                )}
                            </div>
                            {isPostEtlkRadio === 'json' && (
                                <div style={{ display: 'flex', marginBottom: 16 }}>
                                    <div style={{ marginRight: 16 }}>输出处理器模板</div>
                                    <Form.Item name="outputTemplate" wrapperCol={{ span: 24 }} style={{ flex: 1 }}>
                                        <TextArea
                                            style={{ width: '100%' }}
                                            value={getFieldsValue()?.outputTemplate || undefined}
                                            rows={3}
                                            disabled={disabled}
                                            onChange={(e) => form.setFieldValue('outputTemplate', e.target.value)}
                                        />
                                    </Form.Item>
                                </div>
                            )}

                            <Form.Item
                                name="outputConfig"
                                wrapperCol={{ span: 24 }}
                                rules={[
                                    {
                                        validator: (_, value) => {
                                            return validateOutputConfig(value);
                                        }
                                    }
                                ]}>
                                <ServiceOutput
                                    modalType={modalType}
                                    isSync={isSync}
                                    disabled={disabled}
                                    systemList={systemList}
                                    methodType={methodType}
                                    isIntegrationTG={isIntegrationTG}
                                />
                            </Form.Item>
                        </div>
                    </div>
                );
            case 'cache':
                return (
                    <div className="service-form-cache-config">
                        <Row gutter={16}>
                            <Col span={24}>
                                <Form.Item
                                    required
                                    initialValue={0}
                                    name={'invokePolicy'}
                                    wrapperCol={{ span: 21 }}
                                    labelCol={{ span: 3 }}
                                    label={
                                        <div style={{ verticalAlign: 'middle' }}>
                                            <span>{I18N.formlist.step1.chaXunFangShi}</span>{' '}
                                            <Tooltip title={I18N.formlist.step1.keYiPeiZhiDuo}>
                                                <Icon type="question-circle" />
                                            </Tooltip>
                                        </div>
                                    }
                                    rules={[
                                        {
                                            required: true,
                                            message: '请选择查询方式'
                                        }
                                    ]}>
                                    <Radio.Group disabled={disabled} value={invokePolicy} onChange={handleInvokePolicy}>
                                        <Radio value={0}>
                                            {I18N.formlist.step1.benDiHuanCunYou}{' '}
                                            <Tooltip title={I18N.formlist.step1.xianChaXunBenDi}>
                                                <Icon type="question-circle" />
                                            </Tooltip>
                                        </Radio>
                                        <Radio value={1}>
                                            {I18N.formlist.step1.zhiJieJieKou}{' '}
                                            <Tooltip title={I18N.formlist.step1.zhiJieFaQiJie}>
                                                <Icon type="question-circle" />
                                            </Tooltip>
                                        </Radio>
                                        <Radio value={2}>
                                            {I18N.formlist.step1.chuanCanYouXian}{' '}
                                            <Tooltip title={I18N.formlist.step1.tongGuoJieKouTiao}>
                                                <Icon type="question-circle" />
                                            </Tooltip>
                                        </Radio>
                                    </Radio.Group>
                                </Form.Item>
                            </Col>
                        </Row>
                        {invokePolicy !== 1 && (
                            <Row gutter={16}>
                                <Col span={24}>
                                    <Form.Item
                                        required
                                        name={'cacheTime'}
                                        label={'缓存设置'}
                                        wrapperCol={{ span: 21 }}
                                        labelCol={{ span: 3 }}
                                        rules={[
                                            {
                                                required: true,
                                                message: '请设置缓存'
                                            }
                                        ]}>
                                        <CacheSettingFormItem disabled={disabled} />
                                    </Form.Item>
                                </Col>
                            </Row>
                        )}
                    </div>
                );
            case 'flow':
                return (
                    <div className="service-form-flow-control">
                        <div className="service-content-flow-control-title">任一监控维度流量达到上限均会触发流量控制</div>
                        <Form.Item name="limitConfig" wrapperCol={{ span: 24 }}>
                            <FlowLimitConfigTable />
                        </Form.Item>
                    </div>
                );
            case 'mock':
                return (
                    <div>
                        {uuid ? (
                            <MockConfig
                                isMockTitle={true}
                                uuid={uuid}
                                response={addEditModalData?.response || ''}
                                modalType={modalType}
                                getAiMockData={getAiMockData}
                                getMockTableList={getMockTableList}
                                documentTypeList={documentTypeList}
                            />
                        ) : (
                            <Empty type="no-result" />
                        )}
                    </div>
                );
        }
    };

    const [menuItemsStatus, setMenuItemsStatus] = useState({
        basic: true,
        contract: true,
        cache: true,
        flow: true
    });

    // 跟踪哪些页面已经被校验过
    const [validatedPages, setValidatedPages] = useState(new Set());

    // 检查所有校验是否通过并且是否已保存（有UUID）
    const isMockEnabled = useMemo(() => {
        const allValidated = menuItemsStatus.basic && menuItemsStatus.contract && menuItemsStatus.cache && menuItemsStatus.flow && hasSaved;

        return allValidated;
    }, [menuItemsStatus, hasSaved]);

    return (
        <div className="config-list-container">
            <div className="config-list-main">
                <div className="config-list-sidebar">
                    {menuItems.map((item) => {
                        const isMock = item.key === 'mock';
                        const disabledStatus = isMock && !isMockEnabled && !addEditModalData?.uuid;
                        // 只对basic、contract、cache和flow显示校验状态
                        const showStatus = ['basic', 'contract', 'cache', 'flow', 'mock'].includes(item.key);
                        const hasBeenValidated = validatedPages.has(item.key);
                        // 缓存设置和流量控制默认显示成功图标，因为它们有默认值
                        // 编辑模式时，基础配置和合同计费与出入参也默认显示成功图标，因为有必填项数据
                        const isSuccessMock =
                            item.key === 'mock' && (mockTableList?.length > 0 || addEditModalData?.response || AiMockData);
                        const hasDefaultValue =
                            ['cache', 'flow'].includes(item.key) || (addEditModalData?.uuid && ['basic', 'contract'].includes(item.key));
                        const isError = showStatus && hasBeenValidated && menuItemsStatus[item.key] === false;
                        const isSuccess =
                            showStatus && ((hasBeenValidated && menuItemsStatus[item.key] === true) || hasDefaultValue || isSuccessMock);
                        return (
                            <div
                                key={item.key}
                                className={classNames('menu-item', {
                                    selected: selectedKey === item.key,
                                    disabled: addEditModalData?.uuid ? false : disabledStatus
                                })}
                                onClick={() => {
                                    if (disabledStatus) return;
                                    handleMenuItemClick(item.key);
                                }}>
                                {item.required && <span className="required-icon">*</span>}
                                <span className="menu-text">{item.name}</span>
                                {showStatus &&
                                    (isError ? (
                                        <span className="error-icon">
                                            <Icon style={{ color: '#ff4d4f' }} type="exclamation-circle" />
                                        </span>
                                    ) : isSuccess ? (
                                        <span className="success-icon">
                                            <Icon style={{ color: '#52c41a' }} type="check-circle" />
                                        </span>
                                    ) : null)}
                                {item.hasTooltip && !addEditModalData?.uuid && (
                                    <Tooltip title={item.tooltip || ''}>
                                        <Icon type="info-circle" />
                                    </Tooltip>
                                )}
                            </div>
                        );
                    })}
                </div>
                <div className="config-list-content">
                    <Form {...formItemLayout} form={form} colon={false}>
                        <div style={{ display: selectedKey === 'basic' ? 'block' : 'none' }}>{renderContent('basic')}</div>
                        <div style={{ display: selectedKey === 'contract' ? 'block' : 'none' }}>{renderContent('contract')}</div>
                        <div style={{ display: selectedKey === 'cache' ? 'block' : 'none' }}>{renderContent('cache')}</div>
                        <div style={{ display: selectedKey === 'flow' ? 'block' : 'none' }}>{renderContent('flow')}</div>
                        <div style={{ display: selectedKey === 'mock' ? 'block' : 'none' }}>{renderContent('mock')}</div>
                    </Form>
                </div>
            </div>
            <div className="config-list-footer">
                <Button onClick={handleCancel}>取消</Button>
                <Button style={{ margin: '0 16px' }} onClick={handleSave}>
                    保存
                </Button>
                <Button type="primary" onClick={handleSaveAndClose}>
                    保存并关闭
                </Button>
            </div>
            {/* 当使用本地状态控制合同详情模态框时，使用以下方式 */}
            {contractModalData.visible && (
                <div className="contract-modal-wrapper">
                    <ContractModal
                        visible={contractModalData.visible}
                        modalType={contractModalData.modalType}
                        data={contractModalData.data}
                        onClose={() => setContractModalData({ ...contractModalData, visible: false })}
                    />
                </div>
            )}
        </div>
    );
};
export default ConfigList;
