import React, { useState, useEffect } from 'react';
import { Table, Input, DatePicker, Button, Icon, InputNumber } from 'tntd';
import moment from 'moment';
import '../index.less';

const FlowLimitConfigTable = ({ value = [], onChange, modalType }) => {
    // 固定的监控维度选项
    const fixedTypes = [
        { type: 1, name: '总流量' },
        { type: 2, name: '单日' },
        { type: 3, name: '每小时' },
        { type: 4, name: '每分钟' },
        { type: 5, name: '每秒(单机)' }
    ];

    // 只读模式的列配置
    const limitColumns = [
        {
            title: '监控维度',
            dataIndex: 'type',
            width: 200,
            render: (text, record) => {
                if (text === 6 && record?.isTimeRange && record?.begin && record?.end) {
                    // 当type是6时，显示时间段
                    const startTime = moment(record?.begin).format('YYYY-MM-DD HH:mm:ss') || '';
                    const endTime = moment(record?.end).format('YYYY-MM-DD HH:mm:ss') || '';
                    return startTime && endTime ? `${startTime} ~ ${endTime}` : '- -';
                }
                // 查找固定类型的名称
                const fixedType = fixedTypes?.find((item) => item?.type === text);
                if (fixedType) {
                    return fixedType.name;
                }
            }
        },
        {
            title: '流量阈值',
            dataIndex: 'limit',
            width: 200,
            render: (text, record) => {
                return text ? text : '- -';
            }
        }
    ];

    if (modalType === 3) {
        // 只读模式下的数据排序：先显示固定类型，再显示时间段
        const sortedData = [...value].sort((a, b) => {
            // 如果a是时间段，优先级低（排在后面）
            if (a.type === 6 && a.isTimeRange) {
                return 1;
            }
            // 如果b是时间段，优先级低（排在后面）
            if (b.type === 6 && b.isTimeRange) {
                return -1;
            }
            // 固定类型之间按type排序
            return a.type - b.type;
        });

        return <Table bordered columns={limitColumns} dataSource={sortedData} />
    }


    // 默认值只包含type=1的数据
    const defaultValue = [
        {
            type: 1,
            limit: 500
        }
    ];

    // 初始化数据
    const [dataSource, setDataSource] = useState(() => {
        // 如果有传入的value，使用传入的value
        if (value && value.length > 0) {
            return value;
        }
        // 否则使用默认值
        return defaultValue;
    });

    useEffect(() => {
        if ((!value || value.length === 0) && onChange) {
            onChange(defaultValue);
        }
    }, []);

    useEffect(() => {
        if (value && value.length > 0) {
            setDataSource(value);
        }
    }, [value]);

    const handleDataChange = (newData) => {
        setDataSource(newData);
        onChange?.(newData);
    };

    const handleInputChange = (value, record, displayIndex) => {
        const newData = [...dataSource];

        if (record.isTimeRange) {
            // 对于自定义时间段，需要找到在原数据中的真实索引
            const timeRangeItems = dataSource.filter(item => item.isTimeRange);
            const timeRangeIndex = displayIndex - fixedTypes.length;
            const realIndex = dataSource.findIndex(item => item === timeRangeItems[timeRangeIndex]);
            if (realIndex !== -1) {
                newData[realIndex] = { ...record, limit: value };
            }
        } else {
            // 对于固定类型，更新或添加数据
            const existingIndex = dataSource.findIndex(item => item.type === record.type);
            if (existingIndex !== -1) {
                newData[existingIndex] = { ...record, limit: value };
            } else {
                newData.push({ ...record, limit: value });
            }
        }

        handleDataChange(newData);
    };

    const handleTimeChange = (timeRange, record, displayIndex) => {
        const newData = [...dataSource];

        // 找到在原数据中的真实索引
        const timeRangeItems = dataSource.filter(item => item.isTimeRange);
        const timeRangeIndex = displayIndex - fixedTypes.length;
        const realIndex = dataSource.findIndex(item => item === timeRangeItems[timeRangeIndex]);

        if (realIndex !== -1) {
            if (!timeRange || timeRange.length !== 2) {
                newData[realIndex] = {
                    ...record,
                    begin: undefined,
                    end: undefined
                };
            } else {
                newData[realIndex] = {
                    ...record,
                    begin: timeRange[0].valueOf(),
                    end: timeRange[1].valueOf()
                };
            }
            handleDataChange(newData);
        }
    };

    const handleAdd = () => {
        const newData = [
            ...dataSource,
            {
                type: 6,
                limit: undefined,
                isTimeRange: true,
                begin: moment().startOf('day').valueOf(),
                end: moment().endOf('day').valueOf()
            }
        ];
        handleDataChange(newData);
    };

    const handleDelete = (displayIndex) => {
        // 找到要删除的自定义时间段在原数据中的真实索引
        const timeRangeItems = dataSource.filter(item => item.isTimeRange);
        const timeRangeIndex = displayIndex - fixedTypes.length;
        const itemToDelete = timeRangeItems[timeRangeIndex];

        if (itemToDelete) {
            const newData = dataSource.filter(item => item !== itemToDelete);
            handleDataChange(newData);
        }
    };

    // 构建要显示的数据
    const displayData = [
        // 固定类型数据
        ...fixedTypes.map((item) => {
            const existingData = dataSource?.find((d) => d?.type === item?.type);
            return existingData || { type: item?.type };
        }),
        // 自定义时间段数据
        ...dataSource.filter((item) => item.isTimeRange)
    ];

    const columns = [
        {
            title: '监控维度',
            dataIndex: 'type',
            width: '50%',
            render: (type, record, index) => {
                if (record?.isTimeRange) {
                    return (
                        <DatePicker.RangePicker
                            style={{ width: '100%' }}
                            format="YYYY-MM-DD HH:mm:ss"
                            showTime={{ format: 'HH:mm:ss' }}
                            value={record?.begin && record?.end ? [moment(record?.begin), moment(record?.end)] : null}
                            onChange={(dates) => handleTimeChange(dates, record, index)}
                            placeholder={['开始时间', '结束时间']}
                        />
                    );
                }
                const fixedType = fixedTypes?.find((item) => item?.type === type);
                return fixedType ? fixedType?.name : '';
            }
        },
        {
            title: '流量阈值',
            width: '40%',
            dataIndex: 'limit',
            render: (text, record, index) => {
                if (record?.isTimeRange) {
                    return (
                        <InputNumber
                            value={record.limit}
                            onChange={(value) => handleInputChange(value, record, index)}
                            placeholder="不限"
                            style={{ width: '100%' }}
                        />
                    );
                }
                return (
                    <Input placeholder="不限" value={record?.limit} onChange={(e) => handleInputChange(e.target.value, record, index)} />
                );
            }
        },
        {
            title: '操作',
            width: '10%',
            render: (text, record, index) => {
                if (record.isTimeRange) {
                    return <Icon type="delete" style={{ cursor: 'pointer', fontSize: 20 }} onClick={() => handleDelete(index)} />;
                }
                return null;
            }
        }
    ];

    return (
        <div className="flow-limit-config-table">
            <Table bordered={true} striped={true} dataSource={displayData} columns={columns} pagination={false} />
            <Button onClick={handleAdd} style={{ width: '100%', border: '1px dashed #C9D2DD', marginTop: 8 }} icon="plus">
                新增时间段
            </Button>
        </div>
    );
};

export default FlowLimitConfigTable;
