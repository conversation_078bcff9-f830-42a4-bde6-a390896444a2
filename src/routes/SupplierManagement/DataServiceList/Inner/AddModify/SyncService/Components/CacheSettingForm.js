import { useEffect } from 'react';
import { InputNumber, Select, Checkbox } from 'tntd';

// 新增缓存设置自定义组件
export const CacheSettingFormItem = ({ value = {}, onChange, disabled }) => {
    // 受控字段，保证有默认值
    const { cacheUnit = 'SAME_DAY', cacheUnitNumber = 1, cacheInfrastructureType = 'nosql', cacheOpen = 1 } = value;

    // 只要组件渲染，确保父表单拿到所有字段的默认值
    useEffect(() => {
        if (onChange) {
            onChange({
                cacheOpen: 1,
                cacheUnit,
                cacheUnitNumber: cacheUnit === 'SAME_DAY' ? 1 : cacheUnitNumber,
                cacheInfrastructureType
            });
        }
        // 只在首次挂载时执行
        // eslint-disable-next-line
    }, []);

    // 统一触发变更
    const triggerChange = (changedValue) => {
        if (onChange) {
            onChange({
                cacheOpen: 1,
                cacheUnit,
                cacheUnitNumber: cacheUnit === 'SAME_DAY' ? 1 : cacheUnitNumber,
                cacheInfrastructureType,
                ...changedValue
            });
        }
    };

    // 选择单位时自动处理cacheUnitNumber
    const handleUnitChange = (val) => {
        if (val === 'SAME_DAY') {
            triggerChange({ cacheUnit: val, cacheUnitNumber: 1 });
        } else if (val === 'HOURS') {
            triggerChange({ cacheUnit: val, cacheUnitNumber: 1 });
        } else if (val === 'DAYS') {
            triggerChange({ cacheUnit: val, cacheUnitNumber: 7 });
        }
    };

    // 持久化checkbox
    const handlePersistChange = (e) => {
        triggerChange({ cacheInfrastructureType: e.target.checked ? 'elasticsearch' : 'nosql' });
    };

    // 输入框
    const handleNumberChange = (val) => {
        triggerChange({ cacheUnitNumber: val });
    };

    return (
        <div className="service-form-cache-config-item">
            {cacheUnit !== 'SAME_DAY' && (
                <InputNumber
                    style={{ width: '160px' }}
                    min={1}
                    placeholder="请输入"
                    value={cacheUnitNumber}
                    disabled={disabled}
                    onChange={handleNumberChange}
                />
            )}
            <Select
                style={{ width: '160px' }}
                placeholder="请选择"
                value={cacheUnit}
                disabled={disabled}
                onChange={handleUnitChange}
                options={[
                    { label: '当天', value: 'SAME_DAY' },
                    { label: '时', value: 'HOURS' },
                    { label: '天', value: 'DAYS' }
                ]}
            />
            <Checkbox onChange={handlePersistChange} checked={cacheInfrastructureType === 'elasticsearch'} disabled={disabled}>
                持久化
            </Checkbox>
        </div>
    );
};
