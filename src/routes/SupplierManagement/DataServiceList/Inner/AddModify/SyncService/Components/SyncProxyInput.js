import I18N from '@/utils/I18N';
import { Input, Checkbox } from 'tntd';

export default (props) => {
    const { value, onChange, disabled, current } = props;

    let proxy = current === 1 ? value?.proxy : value?.asyncProxy;
    let proxyInfo = current === 1 ? value?.proxyInfo : value?.asyncProxyInfo;

    const onValueChange = (value) => {
        const { proxy, proxyInfo } = value;
        if (current === 1) {
            onChange({ proxy, proxyInfo });
        } else {
            onChange({ asyncProxy: proxy, asyncProxyInfo: proxyInfo });
        }
    };

    const handleCheckboxChange = (e) => {
        const isChecked = e.target.checked;
        onValueChange({ proxy: isChecked ? '1' : '0', proxyInfo: isChecked ? proxyInfo : '' });
    };

    const handleInputChange = (e) => {
        onValueChange({ proxy: '1', proxyInfo: e.target.value });
    };

    return (
        <div style={{ display: 'flex' }}>
            <Checkbox disabled={disabled} checked={proxy === '1'} onChange={handleCheckboxChange}>
                {'使用代理'}
            </Checkbox>

            {proxy === '1' && (
                <Input
                    disabled={disabled}
                    style={{ width: 'calc(100% - 130px)', marginLeft: '10px' }}
                    placeholder={I18N.addmodify.proxyinput.qingShuRuWangLuo}
                    value={proxyInfo}
                    onChange={handleInputChange}
                />
            )}
        </div>
    );
};
