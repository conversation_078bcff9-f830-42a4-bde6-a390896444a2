export const allbasicName = [
    'displayName',
    'name',
    'url',
    'methodType',
    'contentType',
    'documentTypeUuid',
    'indexPackageName',
    'timeout',
    'retry',
    'pagination',
    'proxys'
];
export const allcontractName = [
    'dataType',
    'partnerId',
    'contractId',
    'confidence',
    'costLevel',
    'chargeMethod',
    'inputConfig',
    'outputConfig',
    'preEtlHandlerName',
    'postEtlHandlerName',
    'outputTemplate'
];
export const allcacheName = ['invokePolicy', 'cacheTime'];
export const allFlowlimitName = ['limitConfig'];
export const basicRequiredName = [
    'displayName', // 必填
    'name', // 必填
    'url', // 必填
    'methodType', // 必填
    'contentType' // 必填
];
export const contractRequiredName = [
    'dataType', // 必填
    'partnerId', // 必填
    'contractId', // 必填
    'confidence', // 必填
    'costLevel', // 必填
    'chargeMethod', // 必填
    'inputConfig', // 必填
    'outputConfig' // 必填
];
export const allConfigName = [...allbasicName, ...allcontractName, ...allcacheName, ...allFlowlimitName];