.config-list-container {
    width: 100%;
    height: calc(100vh - 100px); /* 减去顶部导航栏的高度 */
    display: flex;
    flex-direction: column;
    background-color: transparent;
    // 主内容区域
    .config-list-main {
        display: flex;
        flex-direction: row;
        background-color: #ffffff;
        border-radius: 16px;
        box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.05);
        flex: 1;
        overflow: hidden;
        height: calc(100% - 64px); /* 减去底部区域的高度 */
        // 左侧菜单栏
        .config-list-sidebar {
            width: 220px;
            display: flex;
            flex-direction: column;
            padding: 12px;
            gap: 4px;
            box-shadow: inset 0 0 0 1px rgb(225, 230, 238);
            overflow-y: auto;
            .menu-item {
                display: flex;
                flex-direction: row;
                align-items: center;
                padding: 0 12px;
                height: 36px;
                border-radius: 8px;
                cursor: pointer;
                position: relative;
                transition: background-color 0.3s;

                &.selected {
                    background-color: rgb(230, 244, 255);

                    .menu-text {
                        font-family: "PingFang SC";
                        font-weight: 500;
                    }
                }

                &.disabled {
                    cursor: not-allowed;

                    .menu-text {
                        color: rgb(186, 189, 197);
                    }
                }

                &:hover:not(.selected):not(.disabled) {
                    background-color: rgba(230, 244, 255, 0.5);
                }
            }

            .required-icon {
                width: 16px;
                height: 16px;
                color: rgb(239, 68, 68);
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .menu-text {
                font-size: 14px;
                line-height: 22px;
                color: rgb(69, 79, 100);
                flex: 1;
                margin-left: 8px;
            }

            .completed-icon {
                width: 16px;
                height: 16px;
                color: rgb(0, 161, 120);
                display: flex;
                align-items: center;
                justify-content: center;

                &::before {
                    content: "✓";
                    font-size: 14px;
                    line-height: 14px;
                }
            }
        }
        // 右侧内容区域
        .config-list-content {
            flex: 1;
            padding: 16px;
            overflow: auto;
            .service-edit-basic-config {
                .ant-row {
                    margin-bottom: 0;
                }
            }
            .service-form-cache-config {
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                align-content: flex-start;
                .ant-row {
                    width: 100%;
                }
                .service-form-cache-config-item {
                    display: flex;
                    gap: 16px;
                    align-items: center;
                    .tnt-checkbox {
                        width: 300px;
                    }
                }
            }
            .service-form-flow-control {
                .ant-col {
                    width: 100%;
                }
                .service-content-flow-control-title {
                    width: 100%;
                    height: 40px;
                    display: flex;
                    border: 1px solid #8cc4ff;
                    border-radius: 16px;
                    background-color: #e6f4ff;
                    padding: 9px 20px;
                    margin: 8px 0;
                    &::before {
                        margin-top: 3px;
                        display: inline-block;
                        margin-right: 4px;
                        content: "";
                        background: url("@/sources/images/common/icon_info.svg")
                            no-repeat center center;
                        width: 16px;
                        height: 16px;
                    }
                }
            }
            .service-form-contract-config {
                .service-form-contract-config-input,
                .service-form-contract-config-output {
                    .tnt-col {
                        width: 100%;
                        .u-add {
                            margin-top: 8px;
                            border: dashed 1px #e8e8e8;
                            margin-bottom: 8px;
                            border-radius: 8px;
                        }
                    }
                }
                .service-form-contract-config-preetlhandler {
                    font-weight: 400;
                    .tnt-checkbox {
                        display: flex;
                        align-items: center;
                        .ant-checkbox {
                            margin-top: 4px;
                        }
                    }
                }
            }
        }
    }
}

// 高级配置按钮
.advanced-config-btn {
    display: inline-flex;
    cursor: pointer;
    transition: opacity 0.3s;

    &:hover {
        opacity: 0.8;
    }
    .link-text {
        color: #454f64;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 4px;

        .anticon {
            font-size: 12px;
        }
    }
}

// 高级配置区域
.advanced-section {
    padding: 8px 16px;
    background-color: #f8f9fb;
    border-radius: 8px;
    margin-top: 8px;
    animation: fadeIn 0.3s ease-in-out;
    .advanced-section-start-pagination {
        display: flex;
        .ant-form-item {
            margin-bottom: 0;
        }
    }
}

// 底部按钮区域
.config-list-footer {
    margin-top: 8px;
    height: 64px;
    padding: 0 16px;
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    background-color: #ffffff;
    border-radius: 16px 16px 0 0;
}
