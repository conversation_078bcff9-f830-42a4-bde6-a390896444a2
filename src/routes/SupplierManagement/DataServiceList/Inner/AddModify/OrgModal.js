import { useState, useEffect } from 'react';
import { Modal, tntdForm as Form, Icon, Input, Select, TreeSelect } from 'tntd';
import I18N from '@/utils/I18N';

export default (props) => {
    const [form] = Form.useForm();
    const typeSelect = Form.useWatch('type', form);
    const { visible, curOrgValue, onCancel } = props;

    useEffect(() => {
        // 设置表单的初始值
        form.setFieldsValue({
            orgItem: [{ type: '1', orgCode: '', limit: '' }]
        });
    }, [form]);
    return (
        <Modal width={800} title={I18N.addmodify.flowlimit.jigouliuliangshangxian} onCancel={() => onCancel()} visible={visible}>
            <Form form={form}>
                <Form.List name="orgItem">
                    {(fields, { add, remove }) => (
                        <>
                            {fields.map(({ key, name, ...restField }) => (
                                <div key={key} style={{ display: 'flex', marginBottom: 8 }}>
                                    <Form.Item name="type">
                                        <Select>
                                            <Select.Option value="1">{I18N.addmodify.flowlimit.jigoushuru}</Select.Option>
                                            <Select.Option value="2">{I18N.addmodify.flowlimit.jigouxuanze}</Select.Option>
                                        </Select>
                                    </Form.Item>

                                    <Form.Item
                                        {...restField}
                                        name={[name, 'orgCode']}
                                        rules={[{ required: true, message: 'Missing first name' }]}>
                                        {typeSelect === '1' ? (
                                            <Input placeholder={I18N.addmodify.flowlimit.qingshurujigou} />
                                        ) : (
                                            <TreeSelect placeholder={I18N.addmodify.flowlimit.qingxuanzejigou} />
                                        )}
                                    </Form.Item>
                                    <Form.Item
                                        {...restField}
                                        name={[name, 'limit']}
                                        rules={[{ required: true, message: 'Missing last name' }]}>
                                        <Input placeholder="Last Name" />
                                    </Form.Item>
                                    <Icon type="delete" onClick={() => remove(name)} />
                                    <Icon type="plus-circle" onClick={() => add()} />
                                </div>
                            ))}
                        </>
                    )}
                </Form.List>
            </Form>
        </Modal>
    );
};
