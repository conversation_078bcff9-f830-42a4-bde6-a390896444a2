.page-parameter-detail {
    .title {
        position: sticky;
        top: 0;
        height: 40px;
        background: #FFFFFF;
        line-height: 40px;
        padding: 0 20px;
        z-index: 1;
        display: flex;
        align-items: center;
    }

    .detail-config {
        display: flex;
        height: calc(100vh - 280px);

        .config-tree {
			background: #fff;
			box-shadow: 0 0 10px 0 rgba(213,213,213,0.50);
            width: 30%;
			height: 100%;
        }

        .config-field {
			padding: 20px 0;
            flex: 1;
			background: #FCFCFC;
			box-shadow: 0 0 10px 0 rgba(213,213,213,0.50);
			height: 100%;
			overflow-y: scroll;
        }
    }
   
}
.footer{
    width: 100%;
    display: flex;
    justify-content: center;
    margin-top: 12px;
    >span{
        >button{
        margin-right: 10px;
    }}
}
