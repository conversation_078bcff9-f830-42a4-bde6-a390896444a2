import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Empty, Button, message } from 'tntd';
import { connect } from 'dva';
import UpdateTimeModal from '../UpdateTimeModal';
import im from 'immutable';

import './index.less';

import { parameterAPI as service } from '@/services';
import SearchTree from '../SearchTree';
import TreeNodeItem from '../TreeNodeItem';

const DetailConfig = (props) => {
    const { globalStore, uuid, visible, curRecord, onClose, mode } = props;

    const { subDataTypeList } = globalStore;
    const id = curRecord?.uuid;

    const [tree, setTree] = useState([]);
    const [curItem, setCurItem] = useState('');
    const [updateDataParams, setUpdateDataParams] = useState({});
    const [loading, setLoading] = useState(false);
    const [timeModalVisible, setTimeModalVisible] = useState(false);

    const queryTree = (id, isDetele) => {
        service
            .fieldTree({
                documentTypeUuid: id
            })
            .then((tree) => {
                setTree(tree?.data);
                if (isDetele) {
                    setCurItem('');
                }
            });
    };

    useEffect(() => {
        if (id && visible) {
            queryTree(id);
        }

        return () => {
            setTree([]);
            setCurItem('');
            setUpdateDataParams({});
        };
    }, [id, visible]);
    useEffect(() => {
        if (mode === 'HIDE') {
            setCurItem('');
        }
        // return () => {
        //     setTree([]);
        //     setCurItem('');
        //     setUpdateDataParams({});
        // };
    }, [mode]);

    const getValues = (values) => {
        const treeCopy = [...tree];
        const uuid = Object.keys(values)?.[0];

        // 触发修改事件
        // 标记父节点
        const treeData = modifyNodeAndParents(treeCopy, uuid, values?.[uuid] || {});

        setTree(treeData);

        setUpdateDataParams((prevData) => ({
            ...prevData,
            [Object.keys(values)?.[0]]: Object.values(values)?.[0]
        }));
    };
    function modifyNodeAndParents(treeData, uuid, newData) {
        // 辅助函数，用于设置当前节点及所有父节点的 showStar 属性为 true
        function setShowStar(tree, node) {
            node.showStar = true;
            if (node.parentUuid) {
                setShowStar(tree, findNode(tree, node.parentUuid));
            }
        }

        // 辅助函数，用于在树中查找具有指定 uuid 的节点
        function findNode(tree, uuid) {
            for (const node of tree) {
                if (node.uuid === uuid) {
                    node.showStar = true;
                    return node;
                }
                if (node.children) {
                    const foundNode = findNode(node.children, uuid);
                    if (foundNode) {
                        return foundNode;
                    }
                }
            }
            return null;
        }

        // 找到要修改的节点
        const nodeToModify = findNode(treeData, uuid);
        if (nodeToModify) {
            // 克隆要修改的节点
            const modifiedNode = { ...nodeToModify };
            // 应用新数据
            Object.assign(modifiedNode, newData);

            const updateData = { ...modifiedNode, ...newData };
            // 设置 showStar 属性
            updateData.showStar = true;
            // const treeCopy = JSON.parse(JSON.stringify(treeData))
            // 更新原始树数据中的节点
            updateTreeNode(treeData, uuid, updateData);
            // 设置父节点的 showStar 属性
            setShowStar(treeData, updateData);
        }

        return treeData;
    }

    function updateTreeNode(tree, uuid, newData) {
        for (let i = 0; i < tree.length; i++) {
            if (tree[i].uuid === uuid) {
                if (newData.path === 'root') {
                    newData.children = tree[i].children;
                }
                tree[i] = newData;
                return;
            }

            if (tree[i].children) {
                updateTreeNode(tree[i].children, uuid, newData);
            }
        }
    }

    function addNodeToTreeData(nodeId, newNode) {
        // 深拷贝 treeData 以避免数据污染
        let treeDataCopy = JSON.parse(JSON.stringify(tree));

        function findNodeById(node, id) {
            if (node.uuid === id) {
                return node;
            }
            if (node.children && node.children.length > 0) {
                for (let child of node.children) {
                    let foundNode = findNodeById(child, id);
                    if (foundNode) {
                        return foundNode;
                    }
                }
            }
            return null;
        }

        let targetNode = findNodeById(treeDataCopy[0], nodeId);

        if (targetNode) {
            let existingNode = targetNode?.children?.find((child) => child.name === newNode.name);
            if (existingNode) {
                message.error(I18N.dataservicelist.geiZiDuanBiaoShi);
                return;
            }

            newNode.showStar = true; // 在新节点上添加 showStar 属性

            let currentNode = targetNode;
            while (currentNode) {
                currentNode.showStar = true; // 在所有父节点上添加 showStar 属性
                currentNode = findNodeById(treeDataCopy[0], currentNode.parentUuid);
            }

            if (!targetNode.children) {
                targetNode.children = [];
            }

            targetNode.children.push(newNode);
            let newUUid = newNode.uuid;

            let params = {
                uuid: newNode?.uuid,
                documentTypeUuid: newNode?.documentTypeUuid,
                parentUuid: newNode?.parentUuid,
                displayName: newNode?.displayName,
                name: newNode?.name,
                dataType: newNode?.dataType,
                topDataType: newNode?.topDataType,
                sourceName: newNode?.sourceName,
                config: newNode?.config,
                dispalyOrder: newNode?.dispalyOrder,
                tableDataType: newNode?.tableDataType,
                //如果是更改的话 是1。新增的话是2
                status: newNode?.status,
                description: newNode?.description,
                tableDataName: newNode?.tableDataName,
                path: newNode?.path
            };
            setUpdateDataParams((prevData) => {
                prevData[newUUid] = params;
                return { ...prevData };
            });

            const treeData = JSON.parse(JSON.stringify(treeDataCopy));
            setTree(treeData);
            message.success(I18N.dataservicelist.tianJiaChengGong);
        } else {
            message.error(I18N.dataservicelist.muBiaoJieDianBuCunZai);
        }
    }

    function removeNodeAndUpdateParents(uuidToDelete, parentUuid) {
        setUpdateDataParams((prevData) => {
            const newData = { ...prevData }; // 复制 prevData
            delete newData[uuidToDelete]; // 删除指定的 uuid 对应的数据
            return newData;
        });

        // 使用深拷贝复制树数据，以防止直接修改原始数据
        const updatedTreeData = JSON.parse(JSON.stringify(tree));

        // 定义一个辅助函数来递归删除节点和更新父节点
        function removeNodeAndUpdateParentsRecursively(node, parent) {
            if (node.uuid === uuidToDelete) {
                // 从父节点的子节点列表中删除该节点
                parent.children = parent.children.filter((c) => c.uuid !== uuidToDelete);

                // 向上检查父节点的父节点，直到根节点

                while (parent) {
                    // 如果父节点下没有 isNewAdd 为 true 且 showStar 为 true 的子节点，且父节点本身不是新增出来的，则将父节点的 showStar 属性设置为 false
                    const hasShowStarTrue = parent?.children?.some((item) => item.showStar === true);

                    const isCreated = parent?.isNewAdd === true;
                    if (!hasShowStarTrue && !isCreated) {
                        parent.showStar = false;
                    }

                    parent = updatedTreeData.find((n) => n.uuid === parent.parentUuid);
                }
            } else {
                if (node.children) {
                    node.children.forEach((c) => removeNodeAndUpdateParentsRecursively(c, node));
                }
            }
        }

        // 从树的根节点开始递归查找和删除节点
        const root = updatedTreeData.find((node) => node.parentUuid === null);
        removeNodeAndUpdateParentsRecursively(root, null);

        const res = updatedTreeData;
        setTree(res);
    }

    return (
        // <Modal
        //     title={'报文结构'}
        //     visible={visible}
        //     width={1000}
        //     onCancel={onClose}
        //     footer={null}
        //     maskClosable={false}
        //     destroyOnClose
        // >
        <div>
            <div className="page-parameter-detail">
                <div className="page-container detail-config">
                    <div className="config-tree">
                        <SearchTree
                            queryTree={queryTree}
                            mode={mode}
                            uuid={uuid}
                            treeData={tree}
                            addDataToNode={(nodeId, newData) => addNodeToTreeData(nodeId, newData)}
                            documentTypeUuid={id}
                            onSelectItem={(item) => {
                                let tempConfig = item;
                                if (tempConfig) {
                                    tempConfig = im.fromJS(item);
                                    tempConfig = tempConfig.toJS();
                                }
                                setCurItem(tempConfig);
                            }}
                            updateDataParams={updateDataParams}
                            removeNode={(delUuid, parentUuid) => {
                                removeNodeAndUpdateParents(delUuid, parentUuid);
                            }}
                        />
                    </div>
                    <div className="config-field">
                        {curItem ? (
                            <TreeNodeItem
                                mode={mode}
                                item={curItem}
                                queryTree={queryTree}
                                documentTypeUuid={id}
                                subDataTypeList={subDataTypeList}
                                curRecord={curRecord}
                                getValues={getValues}
                            />
                        ) : (
                            <Empty
                                // image={empty}
                                type="no-result"
                                imageStyle={{
                                    // height: 140,
                                    margin: '20px 0'
                                }}
                                style={{ position: 'relative', top: '40px' }}
                                description={<span>{I18N.components.detailconfig.zuoCeXuanZeXiang}</span>}
                            />
                        )}
                    </div>
                </div>
            </div>
            {mode === 'EDIT' && (
                <div className="footer">
                    <span>
                        <Button onClick={onClose}>{I18N.dataservicelist.quXiao}</Button>
                        <Button
                            type="primary"
                            onClick={() => {
                                setTimeModalVisible(true);
                            }}
                            loading={loading}>
                            {I18N.dataservicelist.gengXin}
                        </Button>
                    </span>
                </div>
            )}

            <UpdateTimeModal
                visible={timeModalVisible}
                onSave={() => {
                    queryTree(id);
                    setTimeModalVisible(false);
                    onClose();
                    setUpdateDataParams({});
                }}
                onClose={() => {
                    setTimeModalVisible(false);
                }}
                updateDataParams={updateDataParams}
                curRecord={curRecord}
            />
        </div>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(DetailConfig);
