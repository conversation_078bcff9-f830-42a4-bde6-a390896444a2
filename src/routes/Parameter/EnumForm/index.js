import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Tabs, Input, Icon } from 'tntd';
import im from 'immutable';

const { TabPane } = Tabs;

import './index.less';

const EnumForm = (props) => {
    const { onChange, enumData } = props;

    const [panes, setPanes] = useState([]);
    const [activeKey, setActiveKey] = useState('');

    useEffect(() => {
        if (enumData && enumData.length) {
            setPanes(
                enumData.map((res, index) => {
                    const initKey = `${index}${Number(new Date())}`;
                    if (index === 0) {
                        setActiveKey(initKey);
                    }
                    res.key = initKey;
                    return res;
                })
            );
        } else {
            const initKeys = `${Number(new Date())}`;
            setActiveKey(initKeys);
            setPanes([
                {
                    displayName: 'tab',
                    key: initKeys,
                    values: [{ displayName: '', value: '' }]
                }
            ]);
        }
        return () => { };
    }, [enumData]);

    const onChangeTabs = (activeKey) => {
        setActiveKey(activeKey);
    };

    const tabsAction = {
        add: () => {
            const aKey = `${Number(new Date())}`;
            panes.push({
                displayName: `tab${panes.length}`,
                values: [{ displayName: '', value: '' }],
                key: aKey
            });
            setPanes(panes);
            setActiveKey(aKey);
            onChange(panes);
        },
        remove: (targetKey) => {
            let lastIndex;
            let tempActiveKey;
            panes.forEach((pane, i) => {
                if (pane.key === targetKey) {
                    lastIndex = i - 1;
                }
            });
            const tempPanes = panes.filter((pane) => pane.key !== targetKey);
            if (panes.length && activeKey === targetKey) {
                if (lastIndex >= 0) {
                    tempActiveKey = panes[lastIndex].key;
                } else {
                    tempActiveKey = panes[0].key;
                }
            }
            setPanes(tempPanes);
            if (tempActiveKey) {
                setActiveKey(tempActiveKey);
            }
            onChange(tempPanes);
        }
    };

    const onEdit = (targetKey, action) => {
        tabsAction[action](targetKey);
    };

    const onDoubleClick = (index) => {
        let tempPanes = im.List(panes);
        tempPanes = tempPanes.setIn([index, 'idEditTab'], true);

        setPanes(tempPanes.toJS());
    };

    const onPressEnterTab = (val, index) => {
        let tempPanes = im.List(panes);
        tempPanes = tempPanes.setIn([index, 'displayName'], val.currentTarget.value);
        tempPanes = tempPanes.setIn([index, 'idEditTab'], false);
        tempPanes = tempPanes.toJS();
        setPanes(tempPanes);
        onChange(tempPanes);
    };

    const handlerAddField = (index, pindex) => {
        let tempPanes = im.List(panes);
        const values = tempPanes.getIn([pindex, 'values']);
        if (values.length < 49) {
            values.push({ displayName: '', value: '' });
            tempPanes.setIn([pindex, 'values'], im.List(values));
            tempPanes = tempPanes.toJS();
            setPanes(tempPanes);
            onChange(tempPanes);
        }
    };

    const handleFieldValue = (item, index, pindex) => {
        let tempPanes = im.List(panes);
        const values = tempPanes.getIn([pindex, 'values']);
        values.splice(index, 1, item);
        tempPanes.setIn([pindex, 'values'], im.List(values));
        tempPanes = tempPanes.toJS();
        setPanes(tempPanes);
        onChange(tempPanes);
    };

    const handlerRemoveField = (index, pindex) => {
        let tempPanes = im.List(panes);
        const values = tempPanes.getIn([pindex, 'values']);
        values.splice(index, 1);
        tempPanes.setIn([pindex, 'values'], im.List(values));
        tempPanes = tempPanes.toJS();
        setPanes(tempPanes);
        onChange(tempPanes);
    };

    const inputJsx = (param, index, pindex, values) => {
        return (
            <div key={`param-${index}`} className="param-row">
                <div className="input">
                    <Input
                        defaultValue={param.value}
                        placeholder={I18N.components.enumform.yuanShiZhi}
                        maxLength={50}
                        onChange={(e) => {
                            param.value = e.currentTarget.value;
                            handleFieldValue(param, index, pindex);
                        }}
                    />
                </div>
                <div className="input">
                    <Input
                        defaultValue={param.displayName}
                        placeholder={I18N.components.enumform.xianShiZhi}
                        maxLength={50}
                        onChange={(e) => {
                            param.displayName = e.currentTarget.value;
                            handleFieldValue(param, index, pindex);
                        }}
                    />
                </div>
                <div className="operate-icons">
                    <Icon
                        type="plus-circle"
                        onClick={() => {
                            handlerAddField(index, pindex);
                        }}
                    />
                    {values.length !== 1 && (
                        <Icon
                            type="minus-circle"
                            onClick={() => {
                                handlerRemoveField(index, pindex);
                            }}
                        />
                    )}
                </div>
            </div>
        );
    };

    return (
        <Tabs className="enum-form" onChange={onChangeTabs} activeKey={activeKey} type="editable-card" onEdit={onEdit}>
            {panes.map((pane, index) => (
                <TabPane
                    tab={
                        <span
                            className="onDoubleClick"
                            onDoubleClick={() => {
                                onDoubleClick(index);
                            }}>
                            {pane.idEditTab ? (
                                <Input
                                    defaultValue={pane.displayName}
                                    onBlur={(val) => {
                                        onPressEnterTab(val, index);
                                    }}
                                    onPressEnter={(val) => {
                                        onPressEnterTab(val, index);
                                    }}
                                />
                            ) : (
                                pane.displayName
                            )}
                        </span>
                    }
                    key={`${pane.key}`}
                    closable={index !== 0}>
                    {pane.values.map((param, i) => {
                        return inputJsx(param, i, index, pane.values);
                    })}
                </TabPane>
            ))}
        </Tabs>
    );
};

export default EnumForm;
