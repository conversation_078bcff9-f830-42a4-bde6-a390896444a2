import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Modal, message, Input, Form, Select, Cascader } from 'tntd';
import { connect } from 'dva';
import { parameterAPI as service } from '@/services';
import { generateUUIDContinuity } from '@/utils/utils';
import './index.less';

const Option = Select.Option;

const CatalogModal = (props) => {
    const { visible, actions, documentTypeUuid, item = {}, editType, form, globalStore, uuid, addDataToNode } = props;

    const { getFieldDecorator, validateFields, getFieldValue, setFieldsValue } = form;

    const { dataTypeList, subDataTypeList } = globalStore;
    const [name, setName] = useState('');

    const close = () => {
        actions.close();
    };

    useEffect(() => {
        // if (visible) {}
        return () => {};
    }, [visible]);
    useEffect(() => {
        if (getFieldValue('name')) {
            setFieldsValue({
                sourceName: getFieldValue('name')
            });
        }
    }, [getFieldValue('name')]);

    const onSubmit = () => {
        validateFields(async (errors, values) => {
            if (!errors) {
                try {
                    addDataToNode(item.uuid, {
                        parentUuid: item.uuid,
                        ...values,
                        documentTypeUuid,
                        uuid: generateUUIDContinuity(),
                        showStar: true,
                        path: (item.sourcePath ? item.sourcePath + '.' : '') + values?.sourceName,
                        dataType: values?.dataType?.[1] ? values?.dataType?.[1] : values?.dataType?.[0],
                        dataTypes: values?.dataType,
                        sourcePath: (item.sourcePath ? item.sourcePath + '.' : '') + values?.sourceName,
                        tableDataName: '',
                        idataType: values?.dataType?.[1] ? values?.dataType?.[1] : values?.dataType?.[0],
                        status: 2,
                        isNewAdd: true,
                        topDataType: item.dataType,
                        config: '',
                        dispalyOrder: '',
                        tableDataType: ''
                    });
                    // const res =
                    //     editType === 'add'
                    //         ? await service.fieldCreate({
                    //             parentUuid: item.uuid,
                    //             ...values,
                    //             documentTypeUuid,
                    //         })
                    //         : await service.fieldUpdate({
                    //             ...item,
                    //             ...values,
                    //             documentTypeUuid,
                    //         });
                    // if (res) {
                    // actions.refreshTree();

                    close();
                    // message.success('添加成功！');
                    // }
                } catch (e) {
                    message.error(e.msg, I18N.dataservicelist.xinZengChuCuo);
                }
            }
        });
    };

    const formItemLayout = {
        labelCol: { span: 6 },
        wrapperCol: { span: 16 }
    };

    return (
        <Modal
            className="catalog-modal"
            title={I18N.components.catalogmodal.jieGouPeiZhi}
            visible={visible}
            width={800}
            onCancel={close}
            onOk={onSubmit}
            maskClosable={false}
            destroyOnClose>
            <Form className="catalog-form">
                <Form.Item className="form-item" {...formItemLayout} label={I18N.components.catalogmodal.ziDuanMingCheng}>
                    {getFieldDecorator('displayName', {
                        initialValue: editType === 'edit' ? item.displayName : undefined,
                        getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                        rules: [
                            { required: true, message: I18N.components.catalogmodal.qingTianXieZiDuan2 },
                            { max: 200, message: I18N.components.catalogmodal.changDuBuNengChao },
                            { pattern: I18N.components.catalogmodal.aZAZYi2, message: I18N.components.catalogmodal.buNengBaoHanTe }
                        ]
                    })(<Input placeholder={I18N.components.catalogmodal.buNengChaoGuoGe} />)}
                </Form.Item>
                <Form.Item className="form-item" {...formItemLayout} label={I18N.components.catalogmodal.ziDuanLeiXing}>
                    {getFieldDecorator('dataType', {
                        initialValue: item.dataTypes,
                        rules: [{ required: true, message: I18N.components.catalogmodal.qingXuanZeZiDuan }]
                    })(
                        <Cascader
                            options={subDataTypeList}
                            fieldNames={{ label: 'displayName', value: 'value', children: 'children' }}
                            placeholder={I18N.components.catalogmodal.qingXuanZeZiDuan}
                        />
                    )}
                </Form.Item>
                <Form.Item className="form-item" {...formItemLayout} label={I18N.components.catalogmodal.ziDuanBiaoZhi}>
                    {getFieldDecorator('name', {
                        initialValue: editType === 'edit' ? item.name : undefined,
                        getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                        rules: [
                            { required: true, message: I18N.components.catalogmodal.qingTianXieZiDuan },
                            { max: 200, message: I18N.components.catalogmodal.changDuBuNengChao },

                            ...(editType !== 'edit'
                                ? [{ pattern: '^[0-9a-zA-Z_]{1,}$', message: I18N.components.catalogmodal.ziMuShuZiXia }]
                                : [])
                        ]
                    })(<Input placeholder={I18N.components.catalogmodal.qingShuRuZiDuan} disabled={editType === 'edit'} />)}
                </Form.Item>
                <Form.Item className="form-item" {...formItemLayout} label={I18N.components.catalogmodal.yuanShiLuJing}>
                    {getFieldDecorator('sourceName', {
                        initialValue: editType === 'edit' ? item.sourcePath : undefined,
                        getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                        rules: [{ required: true, message: I18N.components.catalogmodal.qingTianXieYuanShi }]
                    })(
                        <Input
                            disabled={true}
                            addonBefore={editType === 'edit' ? '' : item.sourcePath ? item.sourcePath + '.' : ''}
                            placeholder={I18N.components.catalogmodal.qingShuRuYuanShi}
                        />
                    )}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(Form.create()(CatalogModal));
