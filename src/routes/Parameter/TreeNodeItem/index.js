import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Input, Form, Row, Col, Button, Select, message, Cascader } from 'tntd';

import './index.less';
import isJSON from '@/utils/isJSON';

const { TextArea } = Input;
const Option = Select.Option;

const formItemLayout3 = {
    labelCol: { span: 6 },
    wrapperCol: { span: 16 }
};
const formItemLayout4 = {
    labelCol: { span: 7 },
    wrapperCol: { span: 15 }
};
const formItemLayout2 = {
    labelCol: { span: 3 },
    wrapperCol: { span: 20 }
};
const { OptGroup } = Select;
const TreeNodeItem = (props) => {
    const { item, form, queryTree, documentTypeUuid, subDataTypeList = [], mode, curRecord, getValues } = props;

    const { getFieldDecorator, setFieldsValue, resetFields } = form;

    const [dataType, setDataType] = useState([]);
    const [enumConfig, setEnumConfig] = useState([]);
    const [iDataType, setIDataType] = useState(item?.idataType);
    const [subDataTypeListOptions, setSubDataTypeListOptions] = useState(subDataTypeList || []);
    const options = [
        {
            value: 'data',
            label: I18N.dataservicelist.yuanBaoWenBianLiang,
            children: [
                {
                    value: 'data',
                    label: I18N.dataservicelist.yuanBaoWenBianLiang
                }
            ]
        },
        {
            value: 'custom',
            label: I18N.dataservicelist.ziDingYiBaoWenBianLiang,
            children: [
                {
                    value: 'createDate',
                    label: I18N.dataservicelist.chuangJianShiJian,
                    disabled: false
                },
                {
                    value: 'uuid',
                    label: 'uuid',
                    disabled: false
                },
                {
                    value: 'randomNum',
                    label: I18N.dataservicelist.suiJiShu,
                    disabled: false
                }
            ]
        }
    ];
    const [tableTypeOptions, setTableTypeOptions] = useState(options);
    useEffect(() => {
        if (item && item?.children && item?.children?.length && item?.idataType === 'OBJECT') {
            const options = subDataTypeList?.filter((itemData) => itemData?.value === 'ARRAY' || itemData?.value === 'OBJECT');
            setSubDataTypeListOptions(options);
        } else if (item && item?.children && item?.children?.length && item?.idataType === 'ARRAY') {
            const options = subDataTypeList?.filter((itemData) => itemData?.value === 'OBJECT' || itemData?.value === 'ARRAY');
            setSubDataTypeListOptions(options);
        } else {
            setSubDataTypeListOptions(subDataTypeList || []);
        }
    }, [item]);

    useEffect(() => {
        setDataType(item.dataTypes);
        setIDataType(item?.idataType);
        getTableTypeOption(item?.idataType);
        if (item.dataTypes[1] === 'ENUM') {
            try {
                isJSON(item.config) ? setEnumConfig(JSON.parse(item.config)) : setEnumConfig([]);
            } catch (error) {
                console.log('parse error', error);
            }
        }
        resetFields();

        return () => {};
    }, [item]);

    const dataTypeOnChange = (val) => {
        setDataType(val);
        setFieldsValue({
            config: ''
        });

        setIDataType(val?.[1] ? val?.[1] : val?.[0]);
        let value = val?.[1] ? val?.[1] : val?.[0];
        if (value === 'BOOLEAN') {
            setFieldsValue({
                tableDataType: ['data', 'data']
            });
        }
        getTableTypeOption(val?.[1] ? val?.[1] : val?.[0]);
    };

    const getTableTypeOption = (iDataTypeVal) => {
        if (iDataTypeVal) {
            //根据字段类型，将表字段类型某些选项设置为禁用状态
            let curIDataType = iDataTypeVal;
            let newCustomOptions = [...options]?.[1]?.children;
            if (curIDataType === 'INTEGER' || curIDataType === 'FLOAT') {
                //禁用创建时间选项\禁用uuid
                newCustomOptions = newCustomOptions.map((item) => {
                    if (item.value === 'createDate' || item.value === 'uuid') {
                        return {
                            ...item,
                            disabled: true
                        };
                    }
                    return item;
                });
            }
            if (curIDataType === 'DATE') {
                //禁用uuid/禁用随机数
                newCustomOptions = newCustomOptions.map((item) => {
                    if (item.value === 'randomNum' || item.value === 'uuid') {
                        return {
                            ...item,
                            disabled: true
                        };
                    }
                    return item;
                });
            }
            const newOption = [
                {
                    value: 'data',
                    label: I18N.dataservicelist.yuanBaoWenBianLiang,
                    children: [
                        {
                            value: 'data',
                            label: I18N.dataservicelist.yuanBaoWenBianLiang
                        }
                    ]
                },
                { value: 'custom', label: I18N.dataservicelist.ziDingYiBaoWenBianLiang, children: [...newCustomOptions] }
            ];
            setTableTypeOptions(newOption);
        }
    };
    const tableTypeOnChange = (val) => {
        console.log('val', val);
    };

    const onSubmit = () => {
        // isloading(true);

        form.validateFields(async (errors, data) => {
            if (!errors) {
                const temp = {
                    ...item,
                    ...data,
                    config: typeof data.config !== 'string' ? JSON.stringify(data.config) : data.config,
                    children: undefined
                };

                const tempData = {
                    uuid: temp?.uuid,
                    documentTypeUuid: temp?.documentTypeUuid,
                    parentUuid: temp?.parentUuid,
                    displayName: temp?.displayName,
                    name: temp?.name,
                    dataType: data?.dataType?.[1] ? data?.dataType?.[1] : data?.dataType?.[0],
                    idataType: data?.dataType?.[1] ? data?.dataType?.[1] : data?.dataType?.[0],
                    dataTypes: data?.dataType,
                    topDataType: data?.dataType?.[0],
                    sourceName: temp?.sourceName,
                    config: temp?.config,
                    dispalyOrder: temp?.displayOrder,
                    tableDataType: Array.isArray(temp?.tableDataType) ? temp?.tableDataType?.[1] : temp?.tableDataType,
                    //如果是更改的话 是1。新增的话是2
                    status: temp?.isNewAdd ? 2 : 1,
                    description: temp?.description,
                    tableDataName: temp?.tableDataName,
                    path: temp?.path
                };
                let params = {};
                params[item.uuid] = tempData;

                getValues({
                    ...params
                });
            }
        });
    };

    return (
        <Form className="tree-node-item">
            <Row gutter={16}>
                <Col span={12}>
                    {/* 字段名称 */}
                    <Form.Item className="form-item" {...formItemLayout3} label={I18N.components.treenodeitem.ziDuanMingCheng}>
                        {getFieldDecorator('displayName', {
                            initialValue: item.displayName,
                            getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                            rules: [
                                {
                                    required: true,
                                    message: I18N.components.treenodeitem.qingTianXieZiDuan2
                                },
                                {
                                    max: 200,
                                    message: I18N.components.treenodeitem.changDuBuNengChao
                                },
                                {
                                    pattern: I18N.components.treenodeitem.aZAZYi2,
                                    message: I18N.components.treenodeitem.buNengBaoHanTe
                                }
                            ]
                        })(<Input placeholder={I18N.components.treenodeitem.buNengChaoGuoGe} disabled={mode === 'VIEW'} />)}
                    </Form.Item>
                </Col>

                <Col span={12}>
                    {/* 数据表名 */}
                    <Form.Item
                        className="form-item"
                        {...formItemLayout4}
                        label={
                            (iDataType === 'OBJECT' && item?.children?.length) || (iDataType === 'ARRAY' && item?.children?.length)
                                ? I18N.dataservicelist.shuJuBiaoMing
                                : I18N.dataservicelist.biaoZiDuanMing
                        }>
                        {getFieldDecorator('tableDataName', {
                            initialValue: item?.tableDataName || '',
                            getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                            rules: [
                                {
                                    required: !item?.isNewAdd,
                                    message: I18N.components.treenodeitem.qingTianXieZiDuan
                                },
                                {
                                    max: 200,
                                    message: I18N.components.treenodeitem.changDuBuNengChao
                                }
                            ]
                        })(<Input disabled={true} placeholder={I18N.components.treenodeitem.qingShuRuZiDuan} />)}
                    </Form.Item>
                </Col>
            </Row>
            <Row gutter={16}>
                <Col span={12}>
                    {/* 字段类型 */}
                    <Form.Item className="form-item" {...formItemLayout3} label={I18N.components.treenodeitem.ziDuanLeiXing}>
                        {getFieldDecorator('dataType', {
                            initialValue: item.dataTypes,
                            rules: [
                                {
                                    required: true,
                                    message: I18N.components.treenodeitem.qingXuanZeZiDuan2
                                }
                            ]
                        })(
                            <Cascader
                                onChange={dataTypeOnChange}
                                options={subDataTypeListOptions}
                                disabled={mode === 'VIEW'}
                                fieldNames={{
                                    label: 'displayName',
                                    value: 'value',
                                    children: 'children'
                                }}
                                placeholder={I18N.components.treenodeitem.qingXuanZeZiDuan2}
                            />
                        )}
                    </Form.Item>
                </Col>
                <Col span={12}>
                    {/* 表数据类型 */}
                    {((iDataType !== 'OBJECT' && iDataType !== 'ARRAY') ||
                        (iDataType === 'OBJECT' && !item?.children?.length) ||
                        (iDataType === 'ARRAY' && !item?.children?.length)) && (
                        <Form.Item className="form-item" {...formItemLayout4} label={I18N.treenodeitem.index.biaoShuJuLeiXing}>
                            {getFieldDecorator('tableDataType', {
                                initialValue: [item?.tableDataType === 'data' ? 'data' : 'custom', item?.tableDataType] || ['data', 'data'],
                                rules: [
                                    {
                                        required: true,
                                        message: I18N.dataservicelist.ziDingYiBaoWenBianLiang
                                    }
                                ]
                            })(
                                <Cascader
                                    options={tableTypeOptions}
                                    disabled={
                                        mode === 'VIEW' ||
                                        iDataType === 'BOOLEAN' ||
                                        (iDataType === 'OBJECT' && !item?.children?.length) ||
                                        (iDataType === 'ARRAY' && !item?.children?.length)
                                    }
                                    onChange={tableTypeOnChange}
                                />
                                // <Select
                                //     placeholder="请选择表数据类型"
                                //     allowClear
                                //     showSearch
                                //     disabled={
                                //         mode === 'VIEW' ||
                                //         iDataType === 'BOOLEAN' ||
                                //         (iDataType === 'OBJECT' &&
                                //             !item?.children?.length) ||
                                //         (iDataType === 'ARRAY' &&
                                //             !item?.children?.length)
                                //     }
                                //     // onChange={dataTypeOnChange}
                                //     filterOption={(input, option) =>
                                //         option.props.children
                                //             .toLowerCase()
                                //             .indexOf(input.toLowerCase()) >= 0
                                //     }
                                // >

                                //     <Option value="data">原报文变量</Option>
                                //     <OptGroup label="自定义报文变量">
                                //         <Option
                                //             value="createDate"
                                //             disabled={
                                //                 iDataType === 'INTEGER' ||
                                //                 iDataType === 'FLOAT'
                                //             }
                                //         >
                                //             创建时间
                                //         </Option>
                                //         <Option
                                //             value="uuid"
                                //             disabled={
                                //                 iDataType === 'INTEGER' ||
                                //                 iDataType === 'FLOAT' ||
                                //                 iDataType === 'DATE'
                                //             }
                                //         >
                                //             uuid
                                //         </Option>
                                //         <Option
                                //             value="randomNum"
                                //             disabled={iDataType === 'DATE'}
                                //         >
                                //             随机数
                                //         </Option>
                                //     </OptGroup>
                                // </Select>
                            )}
                        </Form.Item>
                    )}
                </Col>
            </Row>

            <Row gutter={16}>
                <Col span={24}>
                    {/* 报文路径 */}
                    <Form.Item
                        className="form-item"
                        {...formItemLayout2}
                        label={iDataType !== 'OBJECT' ? I18N.components.treenodeitem.baoWenLuJing : I18N.treenodeitem.index.ziDuanLuJing}>
                        {getFieldDecorator('path', {
                            initialValue: item.path,
                            getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                            rules: [
                                {
                                    required: true,
                                    message: I18N.components.treenodeitem.qingTianXieBaoWen
                                }
                            ]
                        })(<Input readOnly={item.uuid} disabled={true} placeholder={I18N.components.treenodeitem.qingShuRuBaoWen} />)}
                    </Form.Item>
                </Col>
            </Row>
            <Row gutter={16}>
                <Col span={24}>
                    {/* 原始路径 */}
                    <Form.Item className="form-item" {...formItemLayout2} label={I18N.components.treenodeitem.yuanShiLuJing}>
                        {getFieldDecorator('sourcePath', {
                            initialValue: item.sourcePath,
                            getValueFromEvent: (event) => event.target.value.replace(/^\s+|\s+$/gm, ''),
                            rules: [
                                {
                                    required: true,
                                    message: I18N.components.treenodeitem.qingTianXieYuanShi
                                }
                            ]
                        })(<Input readOnly={item.uuid} disabled={true} placeholder={I18N.components.treenodeitem.qingShuRuYuanShi} />)}
                    </Form.Item>
                </Col>
            </Row>
            <Row gutter={16}>
                <Col span={24}>
                    {/* 字段描述 */}
                    <Form.Item className="form-item" {...formItemLayout2} label={I18N.components.treenodeitem.ziDuanMiaoShu}>
                        {getFieldDecorator('description', {
                            initialValue: item.description,
                            rules: []
                        })(
                            <TextArea
                                disabled={mode === 'VIEW'}
                                rows={4}
                                placeholder={mode === 'VIEW' ? '' : I18N.components.treenodeitem.qingShuRuZhiBiao}
                            />
                        )}
                    </Form.Item>
                </Col>
            </Row>
            {mode === 'EDIT' && (
                <Row gutter={16}>
                    <Col span={24} className="btns">
                        <Button
                            onClick={() => {
                                resetFields();
                            }}>
                            {I18N.components.treenodeitem.zhongZhi}
                        </Button>
                        <Button type="primary" onClick={onSubmit}>
                            {I18N.components.treenodeitem.baoCun}
                        </Button>
                    </Col>
                </Row>
            )}
        </Form>
    );
};

export default Form.create()(TreeNodeItem);
