import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Modal, message, Form, Select, Radio, DatePicker } from 'tntd';
import { connect } from 'dva';
import { parameterAPI as service } from '@/services';
import moment from 'moment';
import './index.less';

const Option = Select.Option;

const CatalogModal = (props) => {
    const { visible, form, globalStore, uuid, onSave, onClose, updateDataParams, curRecord } = props;
    const [value, setValue] = useState(1);
    const [loading, setLoading] = useState(false);
    const [timeValue, setTimeValue] = useState('');

    const onChange = (e) => {
        setValue(e.target.value);
    };

    useEffect(() => {
        if (visible) return;
    }, [visible]);

    const onSubmit = async () => {
        const values = Object.values(updateDataParams);
        const listPrams = values?.map((item) => {
            return {
                uuid: item?.uuid,
                documentTypeUuid: item?.documentTypeUuid,
                parentUuid: item?.parentUuid,
                displayName: item?.displayName,
                name: item?.name,
                dataType: item?.dataType,
                topDataType: item?.topDataType,
                sourceName: item?.sourceName,
                config: item?.config,
                dispalyOrder: item?.displayOrder,
                tableDataType: item?.tableDataType,
                //如果是更改的话 是1。新增的话是2
                status: item?.status,
                description: item?.description,
                tableDataName: item?.tableDataName,
                path: item?.path,
                // ...item,
                effect: value === 1,
                effectTime: value === 2 ? timeValue : ''
            };
        });
        setLoading(true);
        await service
            .fieldUpdate({ list: listPrams, documentName: curRecord?.name })
            .then((res) => {
                if (!res) return;
                if (res.success) {
                    message.success(I18N.components.treenodeitem.gengXinChengGong);
                    onSave();
                }
                if (!res?.success) {
                    message.error(res.message || res.msg);
                }
            })
            .finally(() => {
                setLoading(false);
            });
    };

    function timeOnChange(value, dateString) {
        console.log('Selected Time: ', value);
        console.log('Formatted Selected Time: ', dateString);
    }

    function onOk(value) {
        const utcTime = moment(value).utc().format('YYYY-MM-DDTHH:mm:ssZ');
        setTimeValue(utcTime);
    }

    return (
        <Modal
            className="updateTime-modal"
            title={I18N.dataservicelist.gengXinNeiRong}
            visible={visible}
            width={800}
            onCancel={onClose}
            onOk={onSubmit}
            maskClosable={false}
            confirmLoading={loading}
            destroyOnClose>
            <Radio.Group onChange={onChange} value={value} defaultValue={1}>
                <Radio value={1}>{I18N.dataservicelist.liJiShengXiao}</Radio>
                <Radio value={2}>
                    {I18N.dataservicelist.zhiDingShengXiaoShiJian} &nbsp;{' '}
                    <DatePicker
                        disabled={value === 1}
                        showTime
                        placeholder={I18N.dataservicelist.xuanZeShiJian}
                        onChange={timeOnChange}
                        onOk={onOk}
                    />
                </Radio>
            </Radio.Group>
        </Modal>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(Form.create()(CatalogModal));
