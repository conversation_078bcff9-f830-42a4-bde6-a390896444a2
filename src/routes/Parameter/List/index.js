import I18N from '@/utils/I18N';
import { useEffect, useMemo, useState } from 'react';
import { connect } from 'dva';
import { Button, message, Switch, Tooltip, Handle, QueryListScene, Modal, PageContainer } from 'tntd';
import moment from 'moment';
import { sortableColumnTitleRenderer } from '@/utils/sortableColumnTitleRenderer';
import './index.less';
import { parameterAPI as service } from '@/services';
import { parse } from 'query-string';
import ParamModel from '../ParamsModal';
import { withRouter } from 'dva/router';
import DetailConfig from '../DetailConfig';
import { makeRandomCode } from '@/utils/utils';

const { QueryList, QueryForm, Field, createActions } = QueryListScene;
const actions = createActions();
const List = withRouter((props) => {
    const { dispatch, store, history, globalStore } = props;
    const { currentApp } = globalStore;
    const [flag, setFlag] = useState(true);
    const [mode, setMode] = useState('HIDE');
    const parsedSearch = parse(props.location.search);
    const uuid = parsedSearch?.uuid;
    const [curRecord, setCurRecord] = useState({});
    const [paramsModalVisible, setParamsModalVisible] = useState(false);
    const [recordItem, setRecordItem] = useState({});
    const [loading, setLoading] = useState(false);
    const [expandedRowKeys, setExpandedRowKeys] = useState([]);

    const { paramsModal } = store;

    const randomCode = useMemo(() => {
        return makeRandomCode();
    }, [mode]);
    useEffect(() => {
        if (!flag) {
            actions.search({ current: 1 });
        }
        setFlag(false);
    }, [currentApp]);

    const changeField = (index, value, record) => {
        setLoading(true);
        service
            .updateStatus({
                documentTypeUuid: record.uuid,
                status: value === 1 ? 'ENABLED' : 'DISABLED'
            })
            .then((res) => {
                if (res?.success) {
                    message.success(I18N.dataservicelist.zhuangTaiBianGeng);
                    setLoading(false);
                    actions.search();
                }
                if (!res?.success) {
                    message.error(res?.msg);
                    setLoading(false);
                }
            });
    };

    const toCheckedOpen = async (index, checked, record) => {
        setLoading(true);
        let sum = 0;
        await service
            .checkTimeMethod({
                documentTypeUuid: record?.uuid
            })
            .then((res) => {
                sum = res?.data;
            });
        if (!sum) {
            changeField(index, checked ? 1 : 0, record);
        } else {
            Modal.confirm({
                title: I18N.dataservicelist.zhuangTaiTiXing,
                okText: I18N.dataservicelist.queDing,
                cancelText: I18N.dataservicelist.quXiao,
                content: I18N.dataservicelist.weiDaoZhiDingShiJian,
                onOk() {
                    if (record?.status === 'ENABLED') {
                        message.warning(I18N.dataservicelist.zhuangTaiWeiKaiQi);
                    } else {
                        service
                            .runDelayTaskNow({
                                documentTypeUuid: record.uuid
                            })
                            .then((res) => {
                                if (res?.success) {
                                    message.success(I18N.dataservicelist.qiYongChengGong);
                                    actions.search();
                                    setLoading(false);
                                }
                                if (!res?.success) {
                                    message.error(res.message);
                                    setLoading(false);
                                }
                            });
                    }
                },

                onCancel() {
                    setLoading(false);
                    actions.search();
                }
            });
        }
    };

    const showConfirm = async (record) => {
        let sum = 0;
        await service
            .checkTimeMethod({
                documentTypeUuid: record?.uuid
            })
            .then((res) => {
                sum = res?.data;
            });
        const titleText = sum ? I18N.dataservicelist.baoWenShanChuHou : I18N.dataservicelist.queRenShanChu;
        Modal.confirm({
            title: titleText,
            okText: sum ? I18N.dataservicelist.jiXuShanChu : I18N.dataservicelist.queDing,
            cancelText: I18N.dataservicelist.quXiao,
            //   content: 'Some descriptions',
            onOk() {
                if (record?.status === 'ENABLED') {
                    message.warning(I18N.dataservicelist.zhuangTaiWeiKaiQi);
                } else {
                    service
                        .documentTypeDelete({
                            documentTypeUuid: record.uuid
                        })
                        .then((res) => {
                            if (res?.success) {
                                message.success(I18N.dataservicelist.shanChuChengGong);
                                // actions.search();
                                history.push('/handle/supplierManagement/dataServiceList');
                            }
                            if (!res?.success) {
                                message.error(res.message);
                            }
                        });
                }
            },

            onCancel() {
                console.log('Cancel');
            }
        });
    };

    const columns = [
        {
            title: I18N.components.list.baoWenMingCheng,
            dataIndex: 'displayName',
            width: 200,
            render: (item) => {
                return (
                    <Tooltip placement="topLeft" title={item}>
                        <div className="displayName">{item}</div>
                    </Tooltip>
                );
            }
        },
        {
            title: I18N.components.list.baoWenBiaoZhi,
            dataIndex: 'name',
            width: 180,
            render: (item) => {
                return (
                    <Tooltip placement="topLeft" title={item}>
                        <div className="displayName">{item}</div>
                    </Tooltip>
                );
            }
        },
        {
            title: I18N.components.paramsmodel.baoWenLeiXing,
            dataIndex: 'contentType',
            width: 110,
            render: (item, record) => {
                return <span>{record?.contentType === 'APPLICATION_JSON' ? 'JSON' : 'XML'}</span>;
            }
        },
        {
            title: sortableColumnTitleRenderer(I18N.dataservicelist.zhuangTai), // 状态
            dataIndex: 'status',
            key: 'status',
            width: 110,
            render: (text, record, index) => {
                let dom = (
                    <Switch
                        className="u-checked"
                        checkedChildren={I18N.addmodify.serviceapi.kai} // 已上线
                        unCheckedChildren={I18N.addmodify.serviceapi.guan} // 已下线
                        checked={record?.status === 'ENABLED'}
                        onChange={(checked) => {
                            if (!checked) {
                                changeField(index, checked ? 1 : 0, record);
                            } else {
                                toCheckedOpen(index, checked ? 1 : 0, record);
                            }
                        }}
                    />
                );
                return dom;
            }
        },
        {
            title: I18N.dataservicelist.gengXinShiJian,
            dataIndex: 'gmtModify',
            width: 190,
            render: (item) => {
                return moment(item).format('YYYY-MM-DD HH:mm:ss');
            }
        },
        {
            title: I18N.components.commontable.xiuGaiRen,
            dataIndex: 'modifyBy',
            width: 100
        },
        {
            title: I18N.components.commontable.caoZuo,
            dataIndex: 'operation',
            width: 180,
            // fixed: 'right',
            render: (val, record) => {
                return (
                    <Handle nums={4}>
                        <Button
                            type="link"
                            onClick={() => {
                                setMode('VIEW');
                                setCurRecord(record);
                                setExpandedRowKeys([record?.uuid]);
                            }}>
                            {I18N.components.commontable.chaKan}
                        </Button>
                        <Button
                            type="link"
                            onClick={() => {
                                setMode('EDIT');
                                setCurRecord(record);

                                setExpandedRowKeys([record?.uuid]);
                            }}>
                            {I18N.components.commontable.xiuGai}
                        </Button>

                        <Button
                            type="link"
                            onClick={() => {
                                showConfirm(record);
                            }}>
                            {I18N.components.list.shanChu}
                        </Button>
                        {/* {window.auth('CP0101', 'delete') ? (

                        ) : null} */}
                        <Button
                            type="link"
                            disabled={record?.status === 'ENABLED'}
                            onClick={() => {
                                // if (window.auth('CP0101', 'config')) {
                                setParamsModalVisible(true);
                                setRecordItem(record);
                                // }
                            }}>
                            {I18N.dataservicelist.chongJian}
                        </Button>
                    </Handle>
                );
            }
        }
    ];

    const query = () => {
        return service.documentTypePage({ dataSourceUuid: uuid }).then((res) => {
            if (!res) return;
            if (res.success) {
                let data = res?.data || [];

                return {
                    data
                };
            }
            message.error(res.message || res.msg);
        });
    };

    const onBack = () => {
        history.push('/handle/supplierManagement/dataServiceList');
    };
    return (
        <div className="page-parameter-list">
            <div className="page-global-header">
                <div className="u-back">
                    <span className="back" onClick={onBack}>
                        {I18N.list.index.fanHui}{' '}
                    </span>
                    &nbsp; &nbsp;{I18N.src.router.shuJuYuanFuWu}/{I18N.dataservicelist.baoWenJieGou}
                </div>
            </div>
            <div className="collapse-table">
                <QueryListScene query={query} actions={actions}>
                    <QueryList
                        rowKey="uuid"
                        bordered={false}
                        columns={columns}
                        className="collapse-table"
                        onExpand={(expanded, record) => {
                            if (!expanded) {
                                setMode('HIDE');
                                setCurRecord({});
                                setExpandedRowKeys([]);
                            } else {
                                setMode('VIEW');
                                setCurRecord(record);
                                setExpandedRowKeys([record?.uuid]);
                            }
                        }}
                        scroll={{ x: '100%' }}
                        pagination={{ hideOnSinglePage: true }}
                        localPagination={true}
                        loading={loading}
                        expandedRowKeys={expandedRowKeys}
                        expandedRowRender={(record) => (
                            <div className="version-view--content-wrap" key={randomCode}>
                                <DetailConfig
                                    visible={mode !== 'HIDE'}
                                    mode={mode}
                                    onClose={() => {
                                        setMode('HIDE');
                                        setCurRecord({});
                                        setExpandedRowKeys([]);
                                        actions.search();
                                    }}
                                    uuid={uuid}
                                    curRecord={curRecord}
                                />
                            </div>
                        )}
                    />
                </QueryListScene>

                <ParamModel
                    visible={paramsModalVisible}
                    onClose={() => {
                        setParamsModalVisible(false);
                    }}
                    currentRecord={recordItem}
                    onSave={() => {
                        actions.search();
                        setParamsModalVisible(false);
                    }}
                    isEdit={true}
                    dataSourceUuid={uuid}
                />
            </div>
        </div>
    );
});

export default connect((state) => ({
    globalStore: state.global,
    store: state.parameter
}))(PageContainer(List));
