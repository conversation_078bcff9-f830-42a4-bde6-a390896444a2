.page-parameter-list {
    .page-global-header{
        .u-back{
            
            .tnt-current-v3 & {
                background-color: transparent;
                color: white;
                .anticon-left {
                    color: #8b919e;
                }
            }
            
            .back {
                color:white;
                cursor: pointer;
                &:hover{
                    color: #2196F3;
                }
            }
        }
        .u-title{
            margin-left: 20px;
            font-size: 14px;
        }
    }
    .filter {
        display: flex;
        align-items: center;
		margin-bottom: 12px;
		justify-content: space-between;

        .input-search {
            width: auto;
        }

        .btn-add,
        .btn-import,
        .btn-export,
        .btn-search {
            margin-left: 14px;
        }
	}
	.ant-btn-link {
		padding: 0;
	}
	.operation {
		.disabled {
			color: rgba(0, 0, 0, 0.25);
			cursor: not-allowed;
		}
	}

    .displayName {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        max-width: 300px;
    }
    .ant-pagination{
        display: none;
    }
}




.collapse-table {
    min-height: 100%;
    background: #FFFFFF;
    box-shadow: 0 0 6px 0 rgba(215,219,243,0.50);
    border-radius: @border-radius-base;
    .page-parameter-detail {
        .config-field {
            >form {
                >div {
                    margin: 0px !important;
                }
            }
        }
    }
    .ant-table-fixed{
        // // ant-table-expanded-row 
        // .ant-table-expanded-row-level-1{
        //     display: none;
        // }
        //  .ant-table-expanded-row {
        //     display: none;
        //  }
    }
    .ant-table-row .expand {
        position: relative;
        top: 2px;
        display: inline-block;
        margin: 0px 10.5px 0 -23.5px;
        color: #17233d;
        padding-left: 11px;

    }

    .ant-table-tbody > tr > td:nth-child(2):has(.expand) {
        white-space: nowrap;
        padding: 0 0px 0 0px !important;
    }

    .ant-table-content {
        background: #ffffff;
        // box-shadow: 2px 0px 6px 0px rgba(0,0,0,0.07);
        // border-radius: 2px 0px 0px 2px;
        box-shadow: none;
        border: none !important;
        .ant-table-expand-icon-th,
        .ant-table-row-expand-icon-cell {
            padding: 0;
            padding-left: 10px;
            // padding-right: 10px;
            width: 30px;
            min-width: 35px;
            .ant-table-row-expand-icon.ant-table-row-collapsed,
            .ant-table-row-expand-icon.ant-table-row-expanded {
                width: 14.5px;
                height: 14.5px;
                border: 1px solid #17233d;
            }
            .ant-table-row-collapsed::after,
            .ant-table-row-expanded::after {
                font-weight: 500;
                color: #17233d;
                position: relative;
                top: -1px;
            }
            .anticon-plus-square,
            .anticon-minus-square {
                position: relative;
                top: 0px;
            }
            .expand {
                color: #17233d;
            }
        }

        .ant-table-expand-icon-th,
        .ant-table-selection-column {
            padding: 0;
            // padding-left: 10px;
            // padding-left: 0px;
        }
        .ant-table-thead {
            font-family: PingFangSC-Medium;
            font-size: 14px;
            font-weight: 500;
        }
    }

    &.collapse-table-expaned {
        .collapse-panel {
            .collapse-td-tags {
                text-overflow: clip;
                white-space: normal;
                // text-overflow: ellipsis;
                // white-space: nowrap;
            }
        }
    }

    .collapse-panel {
        .collapse-td-tags {
            overflow: hidden;
            text-overflow: clip;
            white-space: normal;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }

    .ant-table-scroll,
    .ant-table-content {
        .ant-table-body {
            .ant-table-expanded-row.ant-table-expanded-row-level-1 {
                box-shadow: inset 0 0 8px 0 #d1d5e3;
                background: #f3f4f6;
            }
        }
    }
    .ant-table-expanded-row{
        td:first-of-type{
            // display: none;
            width: 10px !important;
        }
        td{
            padding:0 !important
        }
        
    }

    .metric-name {
        .ant-tag.td-tag {
            border-radius: 0;
        }
    }
    .ant-pagination.ant-table-pagination {
        text-align: right;
        float: none;
        .ant-pagination-total-text {
            float: left;
        }
    }

    .ant-table-thead > tr > th,
    .ant-table-tbody > tr > td {
        padding: 8px 10px;
    }
    .ant-divider,
    .ant-divider-vertical {
        margin: 0 8px;
    }

    // tr.ant-table-row > td{
    //     cursor: pointer;
    // }

    .ant-table-fixed-left table,
    .ant-table-fixed-right table {
        width: min-content;
    }
        .ant-table-expanded-row{
        td:first-of-type{
            // display: none;
            width: 10px !important;
        }
        td{
            padding:0 !important;
            // {
                // display: block;
                background: white;
                width: 100%;
                position: relative;
                // left: 1px;
            // }
        }
        
    }
}

.back{
    cursor: pointer;
}