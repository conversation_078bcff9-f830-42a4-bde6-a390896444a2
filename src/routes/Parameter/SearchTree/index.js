import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Tree, Input, Dropdown, Icon, Modal, message, Menu, Ellipsis } from 'tntd';
import im from 'immutable';

import './index.less';

import CatalogModal from '../CatalogModal';
import { parameterAPI as service } from '@/services';
import { typeMap } from '@/constants';

const { TreeNode } = Tree;
const { Search } = Input;
const confirm = Modal.confirm;

const SearchTree = (props) => {
    const { onSelectItem, treeData, documentTypeUuid, mode, uuid, updateDataParams, addDataToNode, removeNode } = props;

    const [expandedKeys, setExpandedKeys] = useState([]);
    const [gData, setGData] = useState([]);
    const [item, setItem] = useState();
    const [editType, setEditType] = useState('edit');
    const [visible, setVisible] = useState(false);
    const [dataList, setDataList] = useState([]);
    const [searchValue, setSearchValue] = useState('');
    const [displayIcon, setDisplayIcon] = useState(null);
    const [autoExpandParent, setAutoExpandParent] = useState(false);

    const actions = {
        refreshTree: () => {
            props.queryTree(documentTypeUuid, true);
        },
        close: () => {
            setVisible(false);
        }
    };
    const getParentKey = (key, tree) => {
        let parentKey;
        for (let i = 0; i < tree.length; i++) {
            const node = tree[i];
            if (node.children) {
                if (node.children.some((item) => item.key === key)) {
                    parentKey = node.key;
                } else if (getParentKey(key, node.children)) {
                    parentKey = getParentKey(key, node.children);
                }
            }
        }
        return parentKey;
    };

    useEffect(() => {
        let temp = treeData || [];
        let list = [];

        if (temp?.length) {
            const generateData = (data) => {
                for (let i = 0; i < data.length; i++) {
                    const node = data[i];
                    node.key = node.uuid;
                    node.title = node.displayName;
                    list.push(node);
                    if (node.children) {
                        generateData(node.children);
                    }
                }
            };
            generateData(temp);
            setGData(temp);
            setDataList(list);
            setExpandedKeys(!expandedKeys.length ? [temp[0].key] : expandedKeys);
        } else {
            setGData(temp);
            setDataList(list);
        }

        return () => {};
    }, [treeData]);

    const onExpand = (expandedKeys) => {
        setExpandedKeys(expandedKeys);
        setAutoExpandParent(false);
    };

    const onSelect = (selectedKeys, e) => {
        onSelectItem(selectedKeys.length ? e.node.props.item : '');
    };
    let timeout;
    const onChangeSearch = (e) => {
        const { value } = e.target;
        timeout && clearTimeout(timeout);
        timeout = setTimeout(() => {
            if (!value) {
                setSearchValue('');
                setExpandedKeys([treeData[0].key]);
                setAutoExpandParent(false);
                return;
            }
            const expandedKeys = (dataList || [])
                ?.map((item) => {
                    if (item.title.indexOf(value) > -1 || item.sourcePath.indexOf(value) > -1 || item.path.indexOf(value) > -1) {
                        return getParentKey(item.key, gData);
                    }
                    return null;
                })
                .filter((item, i, self) => item && self.indexOf(item) === i);
            setExpandedKeys(expandedKeys);
            setSearchValue(value);
            setAutoExpandParent(true);
        }, 100);
    };

    const enterItem = (id) => {
        setDisplayIcon(id);
    };

    const leaveItem = () => {
        setDisplayIcon(null);
    };

    const openCatalog = (item, type) => {
        setItem(item);
        setEditType(type);
        setVisible(true);
    };
    const cloneConfig = (data) => {
        let tempConfig = im.List(data);
        tempConfig = tempConfig.toJS();
        return tempConfig;
    };

    const loop = (data) => {
        const dataSource = data || [];
        return dataSource?.map((item, idx) => {
            const sourcePath = item.sourcePath ? item.sourcePath.indexOf(searchValue) : 0;
            const titleColor = item.title.indexOf(searchValue);
            const path = item.path.indexOf(searchValue);
            const beforeStr = item.title.substring(0, titleColor);
            const afterStr = item.title.substring(titleColor + searchValue.length);
            let title = <span>{item.title}</span>;
            if (searchValue && (sourcePath !== -1 || path !== -1)) {
                title = <span style={{ color: '#f50' }}>{item.title}</span>;
            }

            if (searchValue && titleColor !== -1) {
                title = (
                    <span>
                        {beforeStr}
                        <span style={{ color: '#f50' }}>{searchValue}</span>
                        {afterStr}
                    </span>
                );
            }
            const dataType = item.dataType;
            const idataType = item.idataType;
            const dataTypeObj = typeMap[dataType] ? typeMap[dataType] : '';
            const idataTypeObj = typeMap[idataType] ? typeMap[idataType] : '';
            const isNewAdd = item.isNewAdd;
            if (item.children) {
                return (
                    <TreeNode
                        key={item.key}
                        item={item}
                        title={
                            <div className="container-title" onMouseEnter={() => enterItem(item.key)} onMouseLeave={leaveItem}>
                                {item?.showStar ? (
                                    <span className="tip-red" style={{ color: 'red' }}>
                                        。
                                    </span>
                                ) : (
                                    ''
                                )}
                                {/* <span className='tip-red' style={{color:'red'}}>。</span> */}
                                <div className="title-item">
                                    <div style={{ display: 'inline-block', width: '50%' }}>
                                        <Ellipsis title={<>{title}</>} />
                                    </div>
                                    {idataTypeObj ? (
                                        <span style={{ color: typeMap?.[item?.idataType]?.color }}>
                                            {typeMap?.[item?.idataType]?.displayName || '-'}{' '}
                                        </span>
                                    ) : (
                                        ''
                                    )}
                                    {/* <span className="title-item-type">{ }</span> sup */}
                                </div>
                                <div className="btns" style={{ display: displayIcon === item.key ? 'block' : 'none' }}>
                                    <Dropdown
                                        overlay={
                                            <Menu>
                                                {(dataType === 'OBJECT' || dataType === 'ARRAY') && mode !== 'VIEW' && (
                                                    <Menu.Item
                                                        onClick={(e) => {
                                                            e.domEvent.stopPropagation();
                                                            openCatalog(item, 'add');
                                                        }}>
                                                        {I18N.components.searchtree.tianJia}
                                                    </Menu.Item>
                                                )}
                                                {/* <Menu.Item
                                                    onClick={(e) => {
                                                        e.domEvent.stopPropagation();
                                                        openCatalog(item, 'edit');
                                                    }}>
                                                    {I18N.components.searchtree.xiuGai}
                                                </Menu.Item> */}
                                                {isNewAdd && (
                                                    <Menu.Item
                                                        onClick={(e) => {
                                                            e.domEvent.stopPropagation();
                                                            removeNode(item?.uuid, item?.parentUuid);
                                                        }}>
                                                        {I18N.components.searchtree.shanChu}
                                                    </Menu.Item>
                                                )}
                                            </Menu>
                                        }
                                        placement="bottomRight">
                                        <Icon className="type-icon" type="more" />
                                    </Dropdown>
                                </div>
                            </div>
                        }>
                        {loop(item.children || [])}
                    </TreeNode>
                );
            }
            return (
                <TreeNode
                    key={item.key}
                    item={item}
                    title={
                        <div className="container-title" onMouseEnter={() => enterItem(item.key)} onMouseLeave={leaveItem}>
                            {item?.showStar ? (
                                <span className="tip-red" style={{ color: 'red' }}>
                                    。
                                </span>
                            ) : (
                                ''
                            )}
                            <div className="title-item">
                                <div style={{ display: 'inline-block', width: '50%' }}>
                                    <Ellipsis title={<>{title}</>} />
                                </div>
                                {dataTypeObj ? <sup style={{ color: dataTypeObj.color }}> {dataTypeObj.displayName} </sup> : ''}
                            </div>
                            <div className="btns" style={{ display: displayIcon === item.key ? 'block' : 'none' }}>
                                {/* <Icon
                                    className={dataSource?.length - 1 === idx ? 'type-disable' : 'type-icon'}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        if (dataSource?.length - 1 === idx) {
                                            return;
                                        }
                                        const tempData = cloneConfig(dataSource);
                                        const down = tempData[idx + 1];
                                        tempData.splice(idx, 2, down, item);
                                        const uuids = tempData?.map((res) => res.uuid);
                                        service.fieldReorder(uuids).then(() => {
                                            actions.refreshTree();
                                        });
                                    }}
                                    type="down"
                                />
                                <Icon
                                    className={idx === 0 ? 'type-disable' : 'type-icon'}
                                    onClick={(e) => {
                                        e.stopPropagation();
                                        if (idx === 0) {
                                            return;
                                        }
                                        const tempData = cloneConfig(dataSource);
                                        const up = tempData[idx - 1];
                                        tempData.splice(idx - 1, 2, item, up);
                                        const uuids = tempData?.map((res) => res.uuid);
                                        service.fieldReorder(uuids).then(() => {
                                            actions.refreshTree();
                                        });
                                    }}
                                    type="up"
                                /> */}
                                <Dropdown
                                    overlay={
                                        <Menu>
                                            {(dataType === 'OBJECT' || dataType === 'ARRAY') && (
                                                <Menu.Item
                                                    onClick={(e) => {
                                                        e.domEvent.stopPropagation();
                                                        openCatalog(item, 'add');
                                                    }}>
                                                    {I18N.components.searchtree.tianJia}
                                                </Menu.Item>
                                            )}
                                            {/* <Menu.Item
                                                onClick={(e) => {
                                                    e.domEvent.stopPropagation();
                                                    openCatalog(item, 'edit');
                                                }}>
                                                {I18N.components.searchtree.xiuGai}
                                            </Menu.Item> */}
                                            {isNewAdd && (
                                                <Menu.Item
                                                    onClick={(e) => {
                                                        e.domEvent.stopPropagation();
                                                        removeNode(item?.uuid, item?.parentUuid);
                                                    }}>
                                                    {I18N.components.searchtree.shanChu}
                                                </Menu.Item>
                                            )}
                                        </Menu>
                                    }
                                    placement="bottomRight">
                                    <Icon className="type-icon" type="more" />
                                </Dropdown>
                            </div>
                        </div>
                    }
                />
            );
        });
    };

    return (
        <div className="parameter-search-tree">
            <div className="left-header">
                <Search className="search-input" placeholder={I18N.components.searchtree.souSuoZiDuanMing} onChange={onChangeSearch} />
            </div>

            <Tree
                onExpand={onExpand}
                showLine
                switcherIcon={<Icon type="caret-down" />}
                className="tree-doc"
                expandedKeys={expandedKeys}
                onSelect={onSelect}
                autoExpandParent={autoExpandParent}>
                {loop(gData || [])}
            </Tree>
            <CatalogModal
                uuid={uuid}
                documentTypeUuid={documentTypeUuid}
                item={item}
                editType={editType}
                visible={visible}
                actions={actions}
                addDataToNode={addDataToNode}
            />
        </div>
    );
};

export default SearchTree;
