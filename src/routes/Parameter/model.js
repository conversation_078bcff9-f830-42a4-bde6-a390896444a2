export default {
	namespace: 'parameter',
	state: {
		paramsModal: {
			currentRecord: {},
			loading: false,
			isEdit: true,
			visible: false
		},
		updateParams: []
	},
	effects: {

	},
	reducers: {
		update(state, { payload }) {
			return {
				...state,
				...payload
			};
		},
		setModalVisible(state, { payload }) {
			return {
				...state,
				paramsModal: {
					...state.paramsModal,
					...payload
				}
			};
		},
		handleSaveUpdateParams(state, { payload }) {
			return {
				...state,
				updateParams: {
					...state.updateParams,
					...payload
				}
			};
		}
	}
};
