import I18N, { createOtp } from '@/utils/I18N';
import React, { useEffect, useState } from 'react';
import { Modal, Form, Button, Input, Icon, Tooltip, message } from 'tntd';
import service from '../service';
import './index.less';
const formItemLayout = createOtp({
    cn: {
        formItemLayout: {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 5 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 18 }
            }
        },

        action: 220
    },

    en: {
        formItemLayout: {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 7 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            }
        },
        action: 100
    }
});
const IpModal = (props) => {
    const { visible, setVisible, form, dataItem, actions } = props;
    const { getFieldDecorator, setFieldsValue, resetFields, validateFields } = form;
    const [whiteList, setWhiteList] = useState('');
    const [blackList, setBlackList] = useState('');
    const [whiteBlack, setWhiteBlack] = useState(false);
    const checkIPRangesOverlap = (range1Start, range1End, range2Start, range2End) => {
        // 解析 IP 地址，将 IP 地址转换为数字
        const ipToNumber = (ip) => ip.split('.').reduce((acc, octet, i) => acc + parseInt(octet) * Math.pow(256, 3 - i), 0);
        const range1StartNum = ipToNumber(range1Start);
        const range1EndNum = ipToNumber(range1End);
        const range2StartNum = ipToNumber(range2Start);
        const range2EndNum = ipToNumber(range2End);
        // 检查 IP 段是否重叠
        if (
            (range1StartNum <= range2EndNum && range1StartNum >= range2StartNum) ||
            (range2StartNum <= range1EndNum && range2EndNum >= range1EndNum)
        ) {
            return true; // 重叠
        }
        return false; // 不重叠
    };
    useEffect(() => {
        setWhiteBlack(false);
        const whiteLists = whiteList?.split(';');
        const blackLists = blackList?.split(';');
        whiteLists.map((v) => {
            let whiteStart = v.split('-')[0];
            let whiteEnd = '';
            if (v.split('-').length < 2) {
                whiteEnd = v.split('-')[0];
            } else {
                whiteEnd = v.split('-')[1];
            }
            blackLists.map((item) => {
                let blackStart = item.split('-')[0];
                let blackEnd = '';
                if (item.split('-').length < 2) {
                    blackEnd = item.split('-')[0];
                } else {
                    blackEnd = item.split('-')[1];
                }
                if (checkIPRangesOverlap(whiteStart, whiteEnd, blackStart, blackEnd)) {
                    setWhiteBlack(true);
                }
            });
        });
    }, [whiteList, blackList]);
    const onSubmit = () => {
        validateFields((errors, data) => {
            const { whiteList, blackList } = data;
            if (!errors) {
                service
                    .putwhitelist({
                        uuid: dataItem.uuid,
                        ipWhiteList: whiteList,
                        ipBlackList: blackList
                    })
                    .then((res) => {
                        if (res?.success) {
                            message.success(res?.message);
                        } else {
                            message.error(res?.message);
                        }
                        setVisible(false);
                        actions.search();
                    });
            }
        });
    };
    const onCancel = () => {
        setVisible(false);
    };
    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.modal.addmodify.quXiao}
        </Button>,
        <Button type="primary" onClick={onSubmit} key="onSubmit">
            {I18N.modal.addmodify.queDing}
        </Button>
    ];
    return (
        <Modal
            className="ip-modal"
            maskClosable={false}
            title={I18N.ipmodal.index.iPMingDanShe}
            visible={visible}
            onCancel={onCancel}
            destroyOnClose
            width={660}
            footer={footerDom}>
            <Form {...formItemLayout.formItemLayout}>
                <Form.Item label={I18N.ipmodal.index.iPHeiMingDan2}>
                    <div className="ip-modal-item">
                        {getFieldDecorator('blackList', {
                            validateTrigger: ['onSubmit'],
                            initialValue: dataItem.ipBlackList || '',
                            rules: [
                                {
                                    max: 200,
                                    message: I18N.modal.addmodify.zuiDuoGeZiFu2
                                },
                                {
                                    validator: (rule, value, callback) => {
                                        let reg =
                                            /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\-(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))$/;
                                        const Ips = value.split(';');
                                        if (!value) {
                                            callback();
                                        } else {
                                            Ips.map((v) => {
                                                if (!reg.test(v)) {
                                                    callback(I18N.ipmodal.index.iPShuRuBu);
                                                }
                                            });
                                        }
                                        callback();
                                    }
                                },
                                {
                                    validator: (rule, value, callback) => {
                                        if (whiteBlack) {
                                            callback(I18N.ipmodal.index.tongYiIPBu);
                                        }
                                        callback();
                                    }
                                }
                            ]
                        })(
                            <Input
                                placeholder={I18N.ipmodal.index.qingShuRuIP2}
                                onChange={(e) => {
                                    setBlackList(e.target.value);
                                }}
                            />
                        )}
                        <Tooltip
                            title={
                                <>
                                    <div>{I18N.ipmodal.index.iPHeiMingDan}</div>
                                    <div>{I18N.ipmodal.index.iPSheZhiZhi}</div>
                                </>
                            }>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </div>
                </Form.Item>
                <Form.Item label={I18N.ipmodal.index.iPBaiMingDan2}>
                    <div className="ip-modal-item">
                        {getFieldDecorator('whiteList', {
                            validateTrigger: ['onSubmit'],
                            initialValue: dataItem.ipWhiteList || '',
                            rules: [
                                {
                                    max: 200,
                                    message: I18N.modal.addmodify.zuiDuoGeZiFu2
                                },
                                {
                                    validator: (rule, value, callback) => {
                                        let reg =
                                            /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)|(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\-(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?))$/;
                                        const Ips = value.split(';');
                                        if (!value) {
                                            callback();
                                        } else {
                                            Ips.map((v) => {
                                                if (!reg.test(v)) {
                                                    callback(I18N.ipmodal.index.iPShuRuBu);
                                                }
                                            });
                                        }
                                        callback();
                                    }
                                },
                                {
                                    validator: (rule, value, callback) => {
                                        if (whiteBlack) {
                                            callback(I18N.ipmodal.index.tongYiIPBu);
                                        }
                                        callback();
                                    }
                                }
                            ]
                        })(
                            <Input
                                placeholder={I18N.ipmodal.index.qingShuRuIP}
                                onChange={(e) => {
                                    setWhiteList(e.target.value);
                                }}
                            />
                        )}
                        <Tooltip
                            title={
                                <>
                                    <div>{I18N.ipmodal.index.iPBaiMingDan}</div>
                                    <div>{I18N.ipmodal.index.iPSheZhiZhi}</div>
                                </>
                            }>
                            <Icon type="question-circle" />
                        </Tooltip>
                    </div>
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(IpModal);
