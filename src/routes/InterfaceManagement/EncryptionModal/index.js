import I18N, { createOtp } from '@/utils/I18N';
import React, { useEffect, useState } from 'react';
import { Modal, Cascader, Form, Button, message } from 'tntd';
import service from '../service';
const formItemLayout = createOtp({
    cn: {
        formItemLayout: {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 5 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 18 }
            }
        },

        action: 220
    },

    en: {
        formItemLayout: {
            labelCol: {
                xs: { span: 24 },
                sm: { span: 7 }
            },
            wrapperCol: {
                xs: { span: 24 },
                sm: { span: 16 }
            }
        },
        action: 100
    }
});
const EncryptionModal = (props) => {
    const { visible, setVisible, encryption, dataItem,actions,form,setDataItem } = props;
    const {setFieldsValue, getFieldDecorator} = form;
    const [entryValue, setEntryValue] = useState('');
    useEffect(()=>{
        if(!dataItem.encryption) {
            setFieldsValue({encryption:''});
        }else {
            setFieldsValue({encryption: [dataItem.encryption,dataItem.encryptionType]});
        }
        
    },[dataItem])
    const onChange = (value) => {
        setEntryValue(value);
    };
    const onCancel = () => {
        setDataItem({});
        setVisible(false);
    };
    const onSubmit = () => {
        const params = {
            uuid: dataItem?.uuid,
            encryptionType: entryValue.length > 1 ? entryValue[1] : '',
            encryption: entryValue[0]
        };
        service.updateEncryption(params).then((res) => {
            if (res?.success) {
                message.success(res.message);
                setVisible(false);
                actions.search();
            } else {
                message.error(res?.message);
            }
        });
    };
    const footerDom = [
        <Button onClick={onCancel} key="cancel">
            {I18N.modal.addmodify.quXiao}
        </Button>,
        <Button type="primary" onClick={onSubmit} key="ok">
            {I18N.modal.addmodify.queDing}
        </Button>
    ];
    return (
        // <Modal visible={visible} title={'出参数据加密'} onCancel={onCancel} onOk={onOk}>
        //     <div className="encryption-modal-container">
        //         <div>{'加密方式'}</div>
        //         <Cascader options={encryption} onChange={onChange} placeholder="请选择加密方式" />
        //     </div>
        // </Modal>
        <Modal
            className="ip-modal"
            maskClosable={false}
            title={I18N.encryptionmodal.index.chuCanShuJuJia}
            visible={visible}
            destroyOnClose
            onCancel={onCancel}
            width={660}
            footer={footerDom}>
            <Form {...formItemLayout.formItemLayout}>
                <Form.Item label={I18N.encryptionmodal.index.jiaMiFangShi}>
                {getFieldDecorator('encryption', {})(
                    <Cascader options={encryption} onChange={onChange} placeholder={I18N.encryptionmodal.index.qingXuanZeJiaMi} />
                )}
                </Form.Item>
            </Form>
        </Modal>
    );
};

export default Form.create()(EncryptionModal);
