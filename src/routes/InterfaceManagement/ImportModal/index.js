import I18N from '@/utils/I18N';
import './index.less';
import { useState } from 'react';
import { connect } from 'dva';
import { Modal, Upload, Button, Icon, Radio, message, Select } from 'tntd';
import service from '../service';

const ImportModal = (props) => {
    const { updateVisible, importVisible, query, successMsg = I18N.dataservicelist.daoRuChengGong } = props;
    const [fileList, setFileList] = useState([]);
    const [radioValues, setRadioValues] = useState({
        condition1: 'SKIP'
    });
    const [confirmLoading, setConfirmLoading] = useState(false);
    const beforeUpload = () => {
        // setFileList(fileList);
        return false;
    };

    const onUploadChange = (info) => {
        let fileList = [...info.fileList];
        if (fileList.length > 1) {
            return message.warning(I18N.importmodal.index.yiCiZuiDuoShang);
        }
        setFileList(fileList);
    };

    const radioStyle = {
        display: 'block',
        height: '30px',
        lineHeight: '30px'
    };

    const radioGroupOnChange = (key, value) => {
        let temp = { ...radioValues };
        temp[key] = value.target.value;
        setRadioValues(temp);
    };

    const onOk = () => {
        if (fileList === null || fileList.length === 0) {
            message.warning(I18N.importmodal.index.qingXianShangChuanWen);
            return;
        }
        setConfirmLoading(true);
        const temp = [];
        fileList.forEach((item) => {
            temp.push(item.originFileObj);
        });
        const params = {
            serviceInterfaceImportDealType: radioValues.condition1
        };

        service
            .importInterface(params, temp, 'file')
            .then((res) => {
                if (res && res.success) {
                    message.success(successMsg); // 导入成功
                    query();
                    onCancel();
                } else {
                    message.error(res.message);
                }
            })
            .finally(() => {
                setConfirmLoading(false);
            });
    };

    const onCancel = () => {
        updateVisible(false);
        setFileList([]);
        setRadioValues({
            condition1: 'SKIP'
        });
    };
    return (
        <Modal
            className="import-modal"
            title={I18N.importmodal.index.daoRuFuWuJie}
            width={700}
            visible={importVisible}
            onCancel={onCancel}
            onOk={onOk}
            maskClosable={false}
            confirmLoading={confirmLoading}>
            <div className="file-select-container">
                <span>{I18N.components.importmodal.xuanZeWenJian}</span>
                <Upload action="123" beforeUpload={beforeUpload} fileList={fileList} onChange={onUploadChange} accept=".tar">
                    <Button>
                        <Icon type="upload" /> {I18N.importmodal.index.dianJiShangChuan}
                    </Button>
                </Upload>
            </div>
            <div className="content-container">
                <div className="content-choice-container">
                    <div className="content-title">{I18N.importmodal.index.chaXunDaoXiangTong8}</div>
                    <Radio.Group onChange={(e) => radioGroupOnChange('condition1', e)} value={radioValues.condition1}>
                        <Radio style={radioStyle} value={'SKIP'}>
                            {I18N.importmodal.index.tiaoGuoXiangTongBiao}
                        </Radio>
                        <Radio style={radioStyle} value={'COVER'}>
                            {I18N.importmodal.index.fuGaiXiangTongBiao}
                        </Radio>
                    </Radio.Group>
                </div>
            </div>
        </Modal>
    );
};

export default connect((state) => ({
    globalStore: state.global
}))(ImportModal);
