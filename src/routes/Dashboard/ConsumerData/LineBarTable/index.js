import I18N from '@/utils/I18N';
import { useEffect, useState } from 'react';
import { Table, Ellipsis, Progress } from 'tntd';
import NoData from '@/components/NoData';
const LineBarTable = (props) => {
    const { serviceRanks } = props;
    const [list, setList] = useState([]);
    const [maxCount, setMaxCount] = useState(0);
    useEffect(() => {
        let max = 0;
        let sortedList = serviceRanks.sort((a, b) => b.count - a.count);
        serviceRanks.map((v) => {
            if (v.count > max) {
                max = v.count;
            }
        });
        setMaxCount(max);
        setList(sortedList);
    }, [serviceRanks]);
    const columns = [
        {
            title: I18N.components.globalanalysis.paiMing,
            dataIndex: 'serviceCode',
            key: 'serviceCode',
            width: 100,
            render: (text, record, index) => {
                return <>{index + 1}</>;
            }
        },
        {
            title: I18N.appservicelist.fuWuMingCheng,
            dataIndex: 'serviceName',
            width: 150,
            render: (text) => {
                return <Ellipsis widthLimit={'180px'} title={text || '- -'} />;
            }
        },
        {
            title: I18N.toptable.index.diaoYongZongLiang,
            dataIndex: 'count',
            width: 150,
            render: (text) => {
                return (
                    <div style={{ display: 'flex', gap: '16px' }}>
                        <div style={{ width: '25px' }}>
                            <Ellipsis widthLimit={'25px'} title={text || '- -'} />
                        </div>
                        <Progress style={{ width: '100px' }} size="small" percent={Math.ceil((text * 100) / maxCount)} showInfo={false} />
                    </div>
                );
            }
        }
    ];
    return (
        <div className="serviceRanks">
            <Table
                style={{ marginLeft: '8px', marginRight: '16px' }}
                columns={columns}
                dataSource={list}
                pagination={{
                    total: serviceRanks.length,
                    pageSize: 5,
                    size: 'small'
                }}
            />
            {/* {!serviceRanks.length && <NoData top={90} />} */}
        </div>
    );
};

export default LineBarTable;
